import { promises as fs } from "fs";
import { NextResponse } from "next/server";
import path from "path";

// Function to get configuration paths
async function getConfigPaths(): Promise<{ testFlowPath: string; testSuitePath: string; recordingsPath: string }> {
  try {
    const configPath = path.join(process.cwd(), "src", "config", "config.json");
    await fs.access(configPath);
    
    const configContent = await fs.readFile(configPath, "utf8");
    const config = JSON.parse(configContent);

    if (!config.testflowpath) {
      throw new Error("Config.json is missing testflowpath. Please create a project first.");
    }

    if (!config.testsuitepath) {
      throw new Error("Config.json is missing testsuitepath. Please create a project first.");
    }

    return {
      testFlowPath: config.testflowpath,
      testSuitePath: config.testsuitepath,
      recordingsPath: config.recordingsPath || ""
    };
  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error('Config loading error:', error.message);
      throw error;
    } else {
      console.error('Unknown config error:', error);
      throw new Error('Failed to get config paths');
    }
  }
}

// Get test case name by ID
async function getTestCaseNameById(testCaseId: number): Promise<string> {
  try {
    const { recordingsPath } = await getConfigPaths();
    
    if (!recordingsPath) {
      return `TestCase${testCaseId}`;
    }
    
    await fs.access(recordingsPath);
    const fileNames = await fs.readdir(recordingsPath);
    const featureFiles = fileNames.filter((file) => file.endsWith(".feature"));
    
    const testCaseIndex = testCaseId - 1;
    if (testCaseIndex >= 0 && testCaseIndex < featureFiles.length) {
      return featureFiles[testCaseIndex].replace('.feature', '');
    }
    
    return `TestCase${testCaseId}`;
    
  } catch (error: unknown) {
    return `TestCase${testCaseId}`;
  }
}

export async function GET(request: Request) {
  try {
    // Get ID from search params
    const { searchParams } = new URL(request.url);
    const testCaseIdStr = searchParams.get('id');

    if (!testCaseIdStr || isNaN(parseInt(testCaseIdStr)) || parseInt(testCaseIdStr) <= 0) {
      return NextResponse.json(
        { error: "Invalid test case ID" },
        { status: 400 }
      );
    }

    const testCaseId = parseInt(testCaseIdStr);

    // Get configuration paths
    const { testFlowPath, testSuitePath } = await getConfigPaths();
    
    // Get test case name
    const testCaseName = await getTestCaseNameById(testCaseId);

    // Read test flows and test suites data
    const [testFlowsData, testSuitesData] = await Promise.all([
      fs.readFile(testFlowPath, "utf8").catch(() => "[]"),
      fs.readFile(testSuitePath, "utf8").catch(() => "[]"),
    ]);

    const testFlows = JSON.parse(testFlowsData);
    const testSuites = JSON.parse(testSuitesData);

    // Find test flows that use this test case
    const testFlowUsage: string[] = [];
    testFlows.forEach((flow: any) => {
      if (flow.testcases && Array.isArray(flow.testcases) && flow.testcases.includes(testCaseName)) {
        testFlowUsage.push(flow.name);
      }
    });

    // Find test suites that use this test case
    const testSuiteUsage = {
      direct: [] as string[],
      indirect: [] as string[],
    };

    testSuites.forEach((suite: any) => {
      // Checks direct usage (test case directly in test suite)
      if (suite.testCases && Array.isArray(suite.testCases) && suite.testCases.includes(testCaseName)) {
        testSuiteUsage.direct.push(suite.name);
      }
      
      // Checks indirect usage (test case via test flows in test suite)
      if (suite.testFlows && Array.isArray(suite.testFlows)) {
        const hasIndirectUsage = suite.testFlows.some((flowName: string) =>
          testFlowUsage.includes(flowName)
        );
        
        if (hasIndirectUsage && !testSuiteUsage.direct.includes(suite.name)) {
          testSuiteUsage.indirect.push(suite.name);
        }
      }
    });

    return NextResponse.json({
      testCaseId,
      testCaseName,
      testFlowUsage,
      testSuiteUsage,
      hasDependencies: testFlowUsage.length > 0 || 
                      testSuiteUsage.direct.length > 0 || 
                      testSuiteUsage.indirect.length > 0
    });

  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error('API error in getdependencies:', error.message);
      return NextResponse.json(
        { error: `Failed to check dependencies: ${error.message}` },
        { status: 500 }
      );
    } else {
      console.error('Unknown API error:', error);
      return NextResponse.json(
        { error: 'Unknown error occurred' },
        { status: 500 }
      );
    }
  }
}

"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface FormData {
  envName: string;
  baseUrl: string;
  dbName: string;
  dbCredentials: string;
}

interface ConfigRow {
  id: number;
  data: FormData;
}

interface ConfigTableProps {
  rows: ConfigRow[];
  onSave?: (id: number, data: FormData) => void;
  onRemove?: (id: number) => void;
}

export default function ConfigTable({ 
  rows, 
  onSave, 
  onRemove
}: ConfigTableProps) {
  const [editingCell, setEditingCell] = useState<{
    rowId: number | null;
    field: string | null;
  }>({ rowId: null, field: null });

  const handleCellClick = (rowId: number, field: string) => {
    setEditingCell({ rowId, field });
  };

  const handleCellChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    rowId: number,
    field: keyof FormData
  ) => {
    const updatedRows = rows.map(row => 
      row.id === rowId 
        ? { ...row, data: { ...row.data, [field]: e.target.value } }
        : row
    );
    
    // Auto-save on change
    const updatedRow = updatedRows.find(row => row.id === rowId);
    if (updatedRow) {
      onSave?.(rowId, updatedRow.data);
    }
  };

  const handleBlur = () => {
    setEditingCell({ rowId: null, field: null });
  };

  const handleRemove = (id: number) => {
    onRemove?.(id);
  };

  // NEW: Save button handler
  const handleSave = (rowId: number) => {
    console.log('Save clicked for row:', rowId);
    const row = rows.find(r => r.id === rowId);
    if (row && onSave) {
      onSave(rowId, row.data);
    }
  };

  return (
    <div className="w-full">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[20%]">Env Name</TableHead>
            <TableHead className="w-[30%]">Application Home</TableHead>
            <TableHead className="w-[20%]">DB Name</TableHead>
            <TableHead className="w-[20%]">DB Credentials</TableHead>
            <TableHead className="w-[10%]">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {rows.map((row) => (
            <TableRow key={row.id}>
              {/* Env Name - Inline Editing */}
              <TableCell className="w-[20%] p-1">
                {editingCell.rowId === row.id && editingCell.field === "envName" ? (
                  <input
                    type="text"
                    value={row.data.envName}
                    onChange={(e) => handleCellChange(e, row.id, "envName")}
                    onBlur={handleBlur}
                    autoFocus
                    className="w-full h-8 px-2 py-1 text-sm bg-white border border-gray-300 rounded"
                    placeholder="Env Name"
                  />
                ) : (
                  <div
                    onClick={() => handleCellClick(row.id, "envName")}
                    className="w-full h-8 px-2 py-1 text-sm cursor-pointer hover:bg-gray-50 rounded border border-transparent flex items-center"
                  >
                    {row.data.envName || "—"}
                  </div>
                )}
              </TableCell>

              {/* Base URL - Inline Editing */}
              <TableCell className="w-[30%] p-1">
                {editingCell.rowId === row.id && editingCell.field === "baseUrl" ? (
                  <input
                    type="text"
                    value={row.data.baseUrl}
                    onChange={(e) => handleCellChange(e, row.id, "baseUrl")}
                    onBlur={handleBlur}
                    autoFocus
                    className="w-full h-8 px-2 py-1 text-sm bg-white border border-gray-300 rounded"
                    placeholder="Base URL"
                  />
                ) : (
                  <div
                    onClick={() => handleCellClick(row.id, "baseUrl")}
                    className="w-full h-8 px-2 py-1 text-sm cursor-pointer hover:bg-gray-50 rounded border border-transparent flex items-center"
                  >
                    {row.data.baseUrl || "—"}
                  </div>
                )}
              </TableCell>

              {/* DB Name - Inline Editing */}
              <TableCell className="w-[20%] p-1">
                {editingCell.rowId === row.id && editingCell.field === "dbName" ? (
                  <input
                    type="text"
                    value={row.data.dbName}
                    onChange={(e) => handleCellChange(e, row.id, "dbName")}
                    onBlur={handleBlur}
                    autoFocus
                    className="w-full h-8 px-2 py-1 text-sm bg-white border border-gray-300 rounded"
                    placeholder="DB Name"
                  />
                ) : (
                  <div
                    onClick={() => handleCellClick(row.id, "dbName")}
                    className="w-full h-8 px-2 py-1 text-sm cursor-pointer hover:bg-gray-50 rounded border border-transparent flex items-center"
                  >
                    {row.data.dbName || "—"}
                  </div>
                )}
              </TableCell>

              {/* DB Credentials - Inline Editing */}
              <TableCell className="w-[20%] p-1">
                {editingCell.rowId === row.id && editingCell.field === "dbCredentials" ? (
                  <input
                    type="text"
                    value={row.data.dbCredentials}
                    onChange={(e) => handleCellChange(e, row.id, "dbCredentials")}
                    onBlur={handleBlur}
                    autoFocus
                    className="w-full h-8 px-2 py-1 text-sm bg-white border border-gray-300 rounded"
                    placeholder="DB Credentials"
                  />
                ) : (
                  <div
                    onClick={() => handleCellClick(row.id, "dbCredentials")}
                    className="w-full h-8 px-2 py-1 text-sm cursor-pointer hover:bg-gray-50 rounded border border-transparent flex items-center"
                  >
                    {row.data.dbCredentials || "—"}
                  </div>
                )}
              </TableCell>

              {/* Actions - Save and Delete Buttons */}
              <TableCell className="w-[10%] p-1">
                <div className="flex gap-2">
                  {/* NEW: Save Button */}
                  <a
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSave(row.id);
                    }}
                    className="cursor-pointer"
                    title="Save configuration"
                  >
                    <img
                      src="/file.svg"
                      className="w-[16px] h-[14px] cursor-pointer"
                    />
                  </a>

                  {/* EXISTING: Delete Button */}
                  <a 
                    onClick={() => handleRemove(row.id)}
                    className="cursor-pointer"
                    title="Delete configuration"
                  >
                    <img
                      src="/Group 21846.svg"
                      className="w-[16px] h-[14px] cursor-pointer"
                    />
                  </a>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {rows.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <p>No configurations found. Click "Add Configuration" to get started.</p>
        </div>
      )}
    </div>
  );
}

[{"action": "navigate", "url": "about:blank", "timestamp": "2025-08-06T07:48:30.816Z"}, {"action": "click", "selector": "//*[@id=\"email\"]", "url": "https://demo.automationtesting.in/", "label": "emailidforsignup_id_xpath", "timestamp": "2025-08-06T07:48:35.483Z"}, {"action": "type", "selector": "//*[@id=\"email\"]", "url": "https://demo.automationtesting.in/", "value": "34", "label": "emailidforsignup_id_xpath", "timestamp": "2025-08-06T07:48:37.467Z"}, {"action": "click", "selector": "//*[@id=\"enterimg\"]", "url": "https://demo.automationtesting.in/", "label": "img-1754466517694_id_xpath", "timestamp": "2025-08-06T07:48:37.696Z"}, {"action": "click", "selector": "//input[@placeholder=\"First Name\"]", "url": "https://demo.automationtesting.in/Register.html", "label": "firstname_placeholder_xpath", "timestamp": "2025-08-06T07:48:39.057Z"}, {"action": "type", "selector": "//input[@placeholder=\"First Name\"]", "url": "https://demo.automationtesting.in/Register.html", "value": "hfjghj", "label": "firstname_placeholder_xpath", "timestamp": "2025-08-06T07:48:40.749Z"}, {"action": "click", "selector": "//input[@placeholder=\"Last Name\"]", "url": "https://demo.automationtesting.in/Register.html", "label": "lastname_placeholder_xpath", "timestamp": "2025-08-06T07:48:40.979Z"}, {"action": "type", "selector": "//input[@placeholder=\"Last Name\"]", "url": "https://demo.automationtesting.in/Register.html", "value": "fghjk", "label": "lastname_placeholder_xpath", "timestamp": "2025-08-06T07:48:42.567Z"}, {"action": "click", "selector": "//textarea[contains(@class, \"form-control\")]", "url": "https://demo.automationtesting.in/Register.html", "label": "address_//textarea[contains(@_xpath", "timestamp": "2025-08-06T07:48:42.785Z"}, {"action": "type", "selector": "//textarea[contains(@class, \"form-control\")]", "url": "https://demo.automationtesting.in/Register.html", "value": "gfhjkl", "label": "address_//textarea[contains(@_xpath", "timestamp": "2025-08-06T07:48:45.715Z"}, {"action": "click", "selector": "//*[@id=\"basicBootstrapForm\"]", "url": "https://demo.automationtesting.in/Register.html", "label": "form-1754466525943_id_xpath", "timestamp": "2025-08-06T07:48:45.944Z"}, {"action": "click", "selector": "//input[@type=\"email\"]", "url": "https://demo.automationtesting.in/Register.html", "label": "input-1754466526563_type_xpath", "timestamp": "2025-08-06T07:48:46.564Z"}, {"action": "type", "selector": "//input[@type=\"email\"]", "url": "https://demo.automationtesting.in/Register.html", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>,", "label": "input-1754466532036_type_xpath", "timestamp": "2025-08-06T07:48:52.040Z"}]
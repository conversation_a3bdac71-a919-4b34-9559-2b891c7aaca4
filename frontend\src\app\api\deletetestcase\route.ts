import { promises as fs } from "fs";
import { NextResponse } from "next/server";
import config from "@/config/config.json";

const RECORDINGS_PATH = config.recordingsPath;

// Handle both route params AND search params
export async function DELETE(request: Request) {
  try {
    // Get ID from URL search params (to match your frontend call)
    const { searchParams } = new URL(request.url);
    const testCaseIdStr = searchParams.get('id');
    
    console.log('DELETE request received for test case ID:', testCaseIdStr);

    // Validate test case ID
    if (!testCaseIdStr || isNaN(parseInt(testCaseIdStr)) || parseInt(testCaseIdStr) <= 0) {
      return NextResponse.json(
        { error: "Invalid test case ID" },
        { status: 400 }
      );
    }

    const testCaseId = parseInt(testCaseIdStr);

    // error handling for directory access
    let fileNames;
    try {
      await fs.access(RECORDINGS_PATH);
      fileNames = await fs.readdir(RECORDINGS_PATH);
    } catch (error) {
      console.error('Cannot access recordings directory:', RECORDINGS_PATH);
      return NextResponse.json(
        { error: "Recordings directory not found" },
        { status: 404 }
      );
    }

    const featureFiles = fileNames.filter((file) => file.endsWith(".feature"));
    
    // Find the test case by ID (index + 1)
    const testCaseIndex = testCaseId - 1;
    if (testCaseIndex < 0 || testCaseIndex >= featureFiles.length) {
      return NextResponse.json(
        { error: `Test case ${testCaseId} not found` },
        { status: 404 }
      );
    }
    
    const fileName = featureFiles[testCaseIndex];
    const testCaseName = fileName.replace('.feature', '');
    const filePath = `${RECORDINGS_PATH}/${fileName}`;

    // Check if file exists before attempting deletion
    try {
      await fs.access(filePath);
    } catch (error) {
      return NextResponse.json(
        { error: "Test case file not found" },
        { status: 404 }
      );
    }
    
    // Delete the feature file
    await fs.unlink(filePath);
    
    console.log(`Successfully deleted test case: ${testCaseName}`);
    
    // Ensure consistent JSON response format
    return NextResponse.json({
      success: true,
      message: `Test case "${testCaseName}" deleted successfully`,
      testCaseId,
      testCaseName,
      deletedFile: fileName
    }, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
  } catch (error: unknown) {
    console.error('Error deleting test case:', error);
    
    // Always return JSON, never HTML
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return NextResponse.json(
      { 
        error: 'Failed to delete test case',
        details: errorMessage
      },
      { 
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        }
      }
    );
  }
}

// Also handle POST method (if your frontend uses POST)
export async function POST(request: Request) {
  return DELETE(request);
}

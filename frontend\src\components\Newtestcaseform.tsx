"use client";

import { useState, useEffect } from "react";
import { ChevronDown } from "lucide-react";
import browsersConfig from "@/config/config.json";
import {
  startRecording,
  stopRecording,
  pauseRecording,
} from "@/app/api/recorder/record";
import { Button } from "./ui/button";
import { useToast } from "@/hooks/use-toast";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

type Browser = {
  name: string;
  icon: string;
  value: string;
};

export default function CreateTestCaseForm({
  mode,
  projectUrl,
  onRecordingStop,
  onCancel,
}: {
  mode: "manual" | "recorded";
  projectUrl: string;
  onRecordingStop: () => void;
  onCancel: () => void;
}) {
  const { toast } = useToast();
  const [browser, setBrowser] = useState<Browser | null>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [name, setName] = useState("");
  const [scenario, setScenario] = useState("");
  const [url, setUrl] = useState("");
  const [useCustomUrl, setUseCustomUrl] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [browsers, setBrowsers] = useState<Browser[]>([]);
  const [loading, setLoading] = useState(false);
  const [sessionId, setSessionId] = useState<number>(-1);

  useEffect(() => {
    setBrowsers(browsersConfig.browsers);
    if (browsersConfig.browsers.length > 0) {
    setBrowser(browsersConfig.browsers[0]);
    }
    }, []);

  const toggleDropdown = () => setIsDropdownOpen(!isDropdownOpen);

  const selectBrowser = (selected: Browser) => {
    setBrowser(selected);
    setIsDropdownOpen(false);
  };

  const handleRecordingClick = async () => {
   
    
    if (!name || !browser?.value || (useCustomUrl && !url)) {
      toast({
        title: "Missing fields",
        description: "Please fill in all fields.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    const recordingUrl = useCustomUrl ? url : sessionStorage.getItem("currentProjectUrl");
    console.log('RECWDW',recordingUrl);
    
    try {
      const response = await startRecording({
        name : name,
        url: recordingUrl ,
        browser: browser?.value || "",
        scenario: scenario
      });
console.log('RESPONSE',response);
      setSessionId(response.sessionId);
      setIsRecording(true);
      setIsPaused(false);
    } catch (error) {
      console.error(error);
      toast({
        title: "Start failed",
        description: "An error occurred while starting recording.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleStopClick = async () => {
    if (sessionId === -1) {
      toast({
        title: "No session",
        description: "No recorder ID available to stop.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);

    try {
      await stopRecording(sessionId);
      setIsRecording(false);
      setIsPaused(false);
      setSessionId(-1);
      onRecordingStop();
      onCancel();
    } catch (error) {
      console.error(error);
      toast({
        title: "Stop failed",
        description: "An error occurred while stopping recording.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleResumeClick = async () => {
    if (sessionId === -1) {
      toast({
        title: "No session",
        description: "No session ID available to resume.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    const recordingUrl = useCustomUrl ? url : projectUrl;

    try {
      await startRecording({
        name,
        url: recordingUrl,
        browser: browser?.value || "",
        sessionId,
      });
      setIsRecording(true);
      setIsPaused(false);
    } catch (error) {
      console.error(error);
      toast({
        title: "Resume failed",
        description: "An error occurred while resuming recording.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePauseClick = async () => {
    if (sessionId === -1) {
      toast({
        title: "No session",
        description: "No active session to pause.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);

    try {
      await pauseRecording(sessionId);
      setIsRecording(false);
      setIsPaused(true);
    } catch (error) {
      console.error(error);
      toast({
        title: "Pause failed",
        description: "An error occurred while pausing.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full mx-auto my-6  bg-white 6 relative">
      <div>
        <div className="relative">
          <label className="absolute -top-3 left-2 bg-white px-1 text-xs font-medium text-gray-700">
            Name <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            className="w-full border border-gray-300 rounded p-3 pt-4"
            value={name}
            onChange={(e) => setName(e.target.value)}
            disabled={isRecording || isPaused}
          />
        </div>
        {/* 2. ADDED: The Scenario textbox */}

        <div className="relative mt-4">
          <label className="absolute -top-3 left-2 bg-white px-1 text-xs font-medium text-gray-700">
            Scenario <span className="text-red-500">*</span>
          </label>

          <input
            type="text"
            className="w-full border border-gray-300 rounded p-3 pt-4"
            value={scenario}
            onChange={(e) => setScenario(e.target.value)}
            disabled={isRecording || isPaused}
          />
        </div>
        <div className="relative mt-6 flex items-center space-x-2">
          <Switch
            id="custom-url-switch"
            checked={useCustomUrl}
            onCheckedChange={setUseCustomUrl}
            disabled={isRecording || isPaused}
          />

          <Label htmlFor="custom-url-switch">
            {useCustomUrl ? "Custom Url " : "Default Project Url "}
          </Label>

        </div>

         {!useCustomUrl ? (
          <div className="relative mt-6">
            <label className="absolute -top-3 left-2 bg-white px-1 text-xs font-medium text-gray-700">
              Project Url (Default)
            </label>

                <input

              type="text"
              className="w-full border border-gray-300 rounded p-3 pt-4 bg-gray-100 cursor-not-allowed"
              value={projectUrl = sessionStorage.getItem("currentProjectUrl") || "No project URL found"}
              disabled
            />

          </div>

        ) : (

          <div className="relative mt-6">

            <label className="absolute -top-3 left-2 bg-white px-1 text-xs font-medium text-gray-700">

              Custom URL <span className="text-red-500">*</span>

            </label>

            <input
              type="text"
              className="w-full border border-gray-300 rounded p-3 pt-4"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              disabled={isRecording || isPaused}
              placeholder="https://example.com"
            />
          </div>
        )}

        <div className="flex flex-wrap justify-between items-end mt-6 gap-4">
          {/* Browser Dropdown */}
          <div className="min-w-[200px] max-w-[400px] w-full sm:w-[300px]">
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Select Browser
            </label>
            <div className="relative">
              <div
                className={`w-full flex items-center justify-between border border-gray-300 rounded p-3 bg-white ${
                  isRecording || isPaused
                    ? "cursor-not-allowed opacity-50"
                    : "cursor-pointer"
                }`}
                onClick={isRecording || isPaused ? undefined : toggleDropdown}
              >
                <div className="flex items-center">
                  {browser && <span className="mr-2">{browser.icon}</span>}
                  {browser?.name}
                </div>
                <ChevronDown
                  size={16}
                  className={`transition-transform duration-200 ${
                    isDropdownOpen ? "rotate-180" : ""
                  }`}
                />
              </div>

              {isDropdownOpen && !isRecording && !isPaused && (
                <div className="absolute w-full bg-white border border-gray-300 mt-1 rounded shadow-lg z-10">
                  {browsers.map((b) => (
                    <div
                      key={b.name}
                      className="p-2 hover:bg-gray-100 cursor-pointer flex items-center"
                      onClick={() => selectBrowser(b)}
                    >
                      <span className="mr-2">{b.icon}</span>
                      {b.name}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="flex space-x-3">
            <Button
              className="px-5 py-3 border border-gray-300 rounded 
                         bg-white text-gray-700 font-medium hover:bg-gray-100 transition-colors duration-200"
              onClick={() => {
                setName("");
                setScenario("");
                setUrl("");
                setUseCustomUrl(false);
                setIsRecording(false);
                setIsPaused(false);
                setSessionId(-1);
                onCancel();
              }}
            >
              Cancel
            </Button>

            {/* Show different buttons based on state */}
            {!isRecording && !isPaused && (
              /* Initial state: Record button */
              <Button
                style={{ backgroundColor: "#AF3520" }}
                className="px-5 py-2.5 text-white rounded flex items-center font-medium"
                onClick={handleRecordingClick}
                disabled={loading}
              >
                {loading ? (
                  "Starting..."
                ) : (
                  <>
                    <svg
                      className="w-4 h-4 mr-1.5"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                    >
                      <circle cx="12" cy="12" r="10" />
                      <circle cx="12" cy="12" r="3" />
                    </svg>
                    Record
                  </>
                )}
              </Button>
            )}

            {isRecording && (
              /* Recording state: Pause + Stop buttons */
              <>
                <Button
                  className="px-5 py-2.5 border border-orange-300 rounded 
                             bg-orange-500 text-white font-medium hover:bg-orange-600 transition-colors duration-200"
                  onClick={handlePauseClick}
                  disabled={loading}
                >
                  {loading ? (
                    "Pausing..."
                  ) : (
                    <>
                      <svg
                        className="w-4 h-4 mr-1.5"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                      >
                        <rect x="6" y="4" width="4" height="16" />
                        <rect x="14" y="4" width="4" height="16" />
                      </svg>
                      Pause
                    </>
                  )}
                </Button>
                <Button
                  style={{ backgroundColor: "#AF3520" }}
                  className="px-5 py-2.5 text-white rounded flex items-center font-medium"
                  onClick={handleStopClick}
                  disabled={loading}
                >
                  {loading ? (
                    "Stopping..."
                  ) : (
                    <>
                      <svg
                        className="w-4 h-4 mr-1.5"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                      >
                        <rect x="6" y="6" width="12" height="12" />
                      </svg>
                      Stop
                    </>
                  )}
                </Button>
              </>
            )}

            {isPaused && (
              /* Paused state: Resume + Stop buttons */
              <>
                <Button
                  style={{ backgroundColor: "#28a745" }}
                  className="px-5 py-2.5 text-white rounded flex items-center font-medium"
                  onClick={handleResumeClick}
                  disabled={loading}
                >
                  {loading ? (
                    "Resuming..."
                  ) : (
                    <>
                      <svg
                        className="w-4 h-4 mr-1.5"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                      >
                        <polygon points="5,3 19,12 5,21" />
                      </svg>
                      Resume
                    </>
                  )}
                </Button>
                <Button
                  style={{ backgroundColor: "#AF3520" }}
                  className="px-5 py-2.5 text-white rounded flex items-center font-medium"
                  onClick={handleStopClick}
                  disabled={loading}
                >
                  {loading ? (
                    "Stopping..."
                  ) : (
                    <>
                      <svg
                        className="w-4 h-4 mr-1.5"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                      >
                        <rect x="6" y="6" width="12" height="12" />
                      </svg>
                      Stop
                    </>
                  )}
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Status indicator */}
        {(isRecording || isPaused) && (
          <div className="mt-3 p-3 rounded-md bg-blue-50 border border-blue-200">
            <div className="flex items-center">
              <div
                className={`w-3 h-3 rounded-full mr-2 ${
                  isRecording ? "bg-red-500 animate-pulse" : "bg-orange-500"
                }`}
              ></div>
              <span className="text-sm font-medium text-blue-800">
                {isRecording
                  ? "Recording in progress..."
                  : "Recording paused - click Resume to continue"}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

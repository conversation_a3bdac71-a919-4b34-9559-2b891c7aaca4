const db = require('../db');
const bcrypt = require('bcryptjs');


// CREATE User
exports.createUser = async (req, res) => {
  const { tenant_id, name, email, password, is_enabled } = req.body;
  const hashedPassword = await bcrypt.hash(password, 10);
  try {
    const [result] = await db.execute(
      'INSERT INTO users (tenant_id, name, email, password_hash, is_enabled) VALUES (?, ?, ?, ?, ?)',
      [tenant_id, name, email, hashedPassword, is_enabled ?? true]
    );
    res.status(201).json({ id: result.insertId, name, email });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// GET USER Details with Roles
exports.getProjectById = async (req, res) => {
  try {
    const [rows] = await db.execute('SELECT * FROM projects WHERE id = ?', [req.params.id]);
    if (rows.length === 0) return res.status(404).json({ error: 'Project not found' });
    res.json(rows[0]);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};


// GET All Users
exports.getUsers = async (_req, res) => {
  try {
    const [rows] = await db.execute('SELECT * FROM users');
    res.json(rows);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// GET User by ID
exports.getUserById = async (req, res) => {
  try {
    const [rows] = await db.execute('SELECT * FROM users WHERE id = ?', [req.params.id]);
    if (rows.length === 0) return res.status(404).json({ error: 'User not found' });
    res.json(rows[0]);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// UPDATE User
exports.updateUser = async (req, res) => {
  const { name, email, is_enabled } = req.body;
  try {
    await db.execute(
      'UPDATE users SET name = ?, email = ?, is_enabled = ? WHERE id = ?',
      [name, email, is_enabled, req.params.id]
    );
    res.json({ message: 'User updated' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// DELETE User
exports.deleteUser = async (req, res) => {
  try {
    await db.execute('DELETE FROM users WHERE id = ?', [req.params.id]);
    res.json({ message: 'User deleted' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

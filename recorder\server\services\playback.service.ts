// import puppeteer from 'puppeteer';
// import { Recording, recordings } from '@shared/schema';
// import { RecordedAction } from '@shared/types';

// export class PlaybackService {
//   private activePlayers: Map<string, { browser: any, page: any }>;

//   constructor() {
//     this.activePlayers = new Map();
//   }

//   async startPlayback(playbackId: string, recording: Recording, browserType: string, headless: boolean = true): Promise<void> {
//     try {
//       console.log(`Starting playback ${playbackId} for recording ID: ${recording.id}`);
      
//       // Determine if running in Replit or other cloud environment
//       const isCloudEnvironment = process.env.REPL_ID || process.env.NODE_ENV === 'production';
//       // If headless parameter is explicitly set, use it, otherwise decide based on environment
//       const useHeadless = headless ? true : isCloudEnvironment;
      
       
//       // Launch browser
//       const browser = await puppeteer.launch({
//         headless: useHeadless ? true : false, // Ensure proper boolean value
//         defaultViewport: { width: 1280, height: 720 },
//         args: [
//           '--window-size=1280,720',
//           '--no-sandbox',
//           '--disable-setuid-sandbox',
//           '--disable-dev-shm-usage',
//           '--disable-gpu',
//           '--no-zygote',
//           '--deterministic-fetch'
//         ],
//         timeout: 60000 // Increase timeout for slow environments
//       });

//    const [page] = await browser.pages();
      
//       // Configure navigation timeouts
//       await page.setDefaultNavigationTimeout(60000);
      
//       // Store active playback
//       this.activePlayers.set(playbackId, { browser, page });
      
//       // Start playback process in a separate async function
//       this.executePlayback(playbackId, recording)
//         .catch(error => console.error(`Error during playback ${playbackId}:`, error))
//         .finally(() => this.cleanupPlayback(playbackId));
      
//     } catch (error) {
//       console.error('Error starting playback:', error);
//       throw error;
//     }
//   }

//   private async executePlayback(playbackId: string, recording: Recording): Promise<void> {
//     const player = this.activePlayers.get(playbackId);
//     if (!player) {
//       throw new Error(`No active playback found for ID: ${playbackId}`);
//     }

//     const { page } = player;
//     const actions = recording.actions as RecordedAction[];

//     if (!actions || actions.length === 0) {
//       console.log(`No actions to play back for recording: ${recording.id}`);
//       return;
//     }

//     try {
//       // Navigate to the initial URL
//       await page.goto(recording.url, { waitUntil: 'domcontentloaded' });
//       console.log("actions",recordings.actions)

//       // Execute each action in sequence
//       for (const action of actions) {
//         await this.executeAction(page, action);
        
//         // Small delay between actions for stability
//        await new Promise(resolve => setTimeout(resolve, 500));
//       }
      
//     } catch (error) {
//       console.error(`Error during playback execution:`, error);
//       throw error;
//     }
//   }

//   private async executeAction(page: any, action: RecordedAction): Promise<void> {
//     try {
//       switch (action.action) {
//         case 'click':
//           if (action.selector) {
//             await page.waitForSelector(action.selector, { timeout: 5000 });
//             await page.click(action.selector);
//           }
//           break;
          
//         case 'type':
//           if (action.selector && action.value) {
//             await page.waitForSelector(action.selector, { timeout: 5000 });
//             await page.type(action.selector, action.value);
//           }
//           break;
          
//         case 'navigate':
//           if (action.url) {
//             await page.goto(action.url, { waitUntil: 'domcontentloaded' });
//           }
//           break;
          
//         case 'select':
//           if (action.selector && action.value) {
//             await page.waitForSelector(action.selector, { timeout: 5000 });
//             await page.select(action.selector, action.value);
//           }
//           break;
          
//         default:
//           console.warn(`Unknown action type: ${action.action}`);
//       }
//     } catch (error) {
//       console.error(`Failed to execute action ${action.action}:`, error);
//       throw error;
//     }
//   }

//   private async cleanupPlayback(playbackId: string): Promise<void> {
//     console.log(`Cleaning up playback ${playbackId}`);
    
//     const player = this.activePlayers.get(playbackId);
//     if (player) {
//       try {
//         const { browser, page } = player;
        
//         // First try to close the page
//         if (page && !page.isClosed()) {
//           console.log(`Closing page for playback ${playbackId}`);
//           await page.close().catch(e => console.warn(`Error closing page: ${e.message}`));
//         }
        
//         // Then close the browser
//         if (browser) {
//           console.log(`Closing browser for playback ${playbackId}`);
//           await browser.close().catch(e => console.warn(`Error closing browser: ${e.message}`));
//         }
//       } catch (error) {
//         console.error(`Error closing browser for playback ${playbackId}:`, error);
//       } finally {
//         this.activePlayers.delete(playbackId);
//         console.log(`Playback ${playbackId} cleaned up successfully`);
//       }
//     }
//   }

//   async stopPlayback(playbackId: string): Promise<void> {
//     await this.cleanupPlayback(playbackId);
//   }
// }

import {
  <PERSON><PERSON>,
  DialogTrigger,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>it<PERSON>,
} from "@/components/ui/dialog";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
  TabsContent,
} from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";

export default function DialogWithTabs() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button>Open Dialog</Button>
      </DialogTrigger>

      <DialogContent className="max-w-xl">
        <DialogHeader>
          <DialogTitle>User Settings</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="profile" className="w-full mt-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="profile">Profile</TabsTrigger>
            <TabsTrigger value="account">Account</TabsTrigger>
          </TabsList>

          <TabsContent value="profile">
            <div className="space-y-2 p-4">
              <p className="text-sm">Update your profile information here.</p>
              {/* Add form or inputs here */}
            </div>
          </TabsContent>

          <TabsContent value="account">
            <div className="space-y-2 p-4">
              <p className="text-sm">Manage your account settings here.</p>
              {/* Add form or inputs here */}
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}

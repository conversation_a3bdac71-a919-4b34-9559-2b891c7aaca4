"use client";

import { AppSidebar } from "@/components/dashboard-sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { toast } from "@/hooks/use-toast";
import CreateTestSuiteDialog from "@/components/test-suite";
import { AccordionTable } from "@/components/table";
import { useState, useEffect, useMemo } from "react";
import { ChevronDown, ChevronUp, Eye, MoreVertical, Edit } from "lucide-react";
import { useRouter } from "next/navigation";
import axios from 'axios';
interface TestSuite {
  name: string;
  description: string;
  testFlows: string[];
  testCases: string[];
}

interface TestCase {
  id: string;
  name: string;
  description?: string;
}

interface TestFlow {
  name: string;
  description: string;
  testcases: string[];
}

interface TestFlowWithCases {
  name: string;
  description: string;
  testCases: TestCase[];
}



// Fetch test suites
async function getTestSuitesFromAPI(): Promise<TestSuite[]> {
  try {
    const response = await axios.get<TestSuite[]>('/api/test-suite');
    return response.data;
  } catch (error) {
    console.error('Failed to fetch test suites:', error);
    return [];
  }
}

// Fetch test flows
async function getTestFlowsFromAPI(): Promise<TestFlow[]> {
  try {
    const response = await axios.get<TestFlow[]>('/api/test-flows');
    return response.data;
  } catch (error) {
    console.error('Failed to fetch test flows:', error);
    return [];
  }
}

// Fetch test cases
async function getTestCasesFromAPI(): Promise<TestCase[]> {
  try {
    const response = await axios.get<TestCase[]>('/api/test-cases');
    return response.data;
  } catch (error) {
    console.error('Failed to fetch test cases:', error);
    return [];
  }
}

function TestSuiteContent({
  testSuiteName,
  testSuites,
  allTestFlows,
  allTestCases,
}: {
  testSuiteName: string | null;
  testSuites: TestSuite[];
  allTestFlows: TestFlow[];
  allTestCases: TestCase[];
}) {
  const [expandedFlows, setExpandedFlows] = useState<string[]>([]);

  // Process data into unified list structure
  const processedData = useMemo(() => {
    if (!testSuiteName) return null;

    const testSuite = testSuites.find((suite) => suite.name === testSuiteName);
    if (!testSuite) return null;

    // Get test flows with their test cases
    const testFlowsWithCases: TestFlowWithCases[] = testSuite.testFlows
      .map((flowName) => {
        const testFlow = allTestFlows.find((flow) => flow.name === flowName);
        if (!testFlow) return null;

        const flowTestCases = allTestCases.filter((tc) =>
          testFlow.testcases.includes(tc.name)
        );

        return {
          name: testFlow.name,
          description: testFlow.description,
          testCases: flowTestCases,
        };
      })
      .filter(Boolean) as TestFlowWithCases[];

    // Get direct test cases (not part of any flow)
    const directTestCases = allTestCases.filter((tc) =>
      testSuite.testCases.includes(tc.name)
    );

    return {
      testFlows: testFlowsWithCases,
      directTestCases,
    };
  }, [testSuiteName, testSuites, allTestFlows, allTestCases]);

  const toggleFlow = (flowName: string) => {
    setExpandedFlows((prev) =>
      prev.includes(flowName)
        ? prev.filter((name) => name !== flowName)
        : [...prev, flowName]
    );
  };

  if (!testSuiteName) {
    return (
      <div className="p-8 text-center text-gray-500">
        <div className="text-gray-400 mb-4">
          <svg
            className="w-16 h-16 mx-auto mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
            />
          </svg>
          <p className="text-lg font-medium text-gray-600">
            Select a test suite to view test cases
          </p>
          <p className="text-sm text-gray-500">
            Choose a test suite from the sidebar to see its associated test
            cases and flows
          </p>
        </div>
      </div>
    );
  }

  if (
    !processedData ||
    (processedData.testFlows.length === 0 &&
      processedData.directTestCases.length === 0)
  ) {
    return (
      <div className="p-8 text-center">
        <div className="text-gray-500 mb-4">
          <svg
            className="w-12 h-12 mx-auto mb-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
            />
          </svg>
          <p className="text-lg font-medium">No test cases found</p>
          <p className="text-sm">
            This test suite doesn't have any test cases or flows yet.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div>
        {/* Test Flows */}
        {processedData.testFlows.map((flow, index) => (
          <div key={flow.name}>
            {/* Flow Header */}
            <div
              className={`flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50 transition-colors${
                index !== 0 ? " border-t border-gray-200" : ""
              }`}
              onClick={() => toggleFlow(flow.name)}
            >
              <span className="font-medium text-gray-900">{flow.name}</span>
              <div className="flex items-center space-x-2">
                {expandedFlows.includes(flow.name) ? (
                  <ChevronUp className="h-5 w-5 text-gray-500" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-gray-500" />
                )}
                <button
                  className="p-1 hover:bg-gray-200 rounded transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    console.log(`More actions for flow: ${flow.name}`);
                  }}
                >
                  <MoreVertical className="h-4 w-4 text-gray-600" />
                </button>
              </div>
            </div>

            {/* Flow Content */}
            {expandedFlows.includes(flow.name) && (
              <div>
                {flow.testCases.length > 0 ? (
                  <>
                    {flow.testCases.map((testCase) => (
                      <div
                        key={testCase.id}
                        className="border-t border-gray-200 p-3 hover:bg-gray-50 transition-colors flex items-center justify-between"
                      >
                        <span className="text-gray-900">{testCase.name}</span>
                        <div className="flex items-center space-x-2">
                          <button
                            className="p-1 hover:bg-gray-200 rounded transition-colors"
                            onClick={() =>
                              console.log(`View test case: ${testCase.name}`)
                            }
                          >
                            <Eye className="h-4 w-4 text-gray-600" />
                          </button>
                          <button
                            className="p-1 hover:bg-gray-200 rounded transition-colors"
                            onClick={() =>
                              console.log(
                                `More actions for test case: ${testCase.name}`
                              )
                            }
                          >
                            <MoreVertical className="h-4 w-4 text-gray-600" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </>
                ) : (
                  <div className="border-t border-gray-200 text-center py-3 text-gray-500">
                    <p className="text-sm">No test cases in this flow</p>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}

        {/* Direct Test Cases */}
        {processedData.directTestCases.length > 0 && (
          <>
            {processedData.directTestCases.map((testCase, index) => {
              const shouldHaveBorder =
                index !== 0 || processedData.testFlows.length > 0;
              return (
                <div
                  key={testCase.id}
                  className={`p-3 hover:bg-gray-50 transition-colors flex items-center justify-between${
                    shouldHaveBorder ? " border-t border-gray-200" : ""
                  }`}
                >
                  <span className="text-gray-900">{testCase.name}</span>
                  <div className="flex items-center space-x-2">
                    <button
                      className="p-1 hover:bg-gray-200 rounded transition-colors"
                      onClick={() =>
                        console.log(`View test case: ${testCase.name}`)
                      }
                    >
                      <Eye className="h-4 w-4 text-gray-600" />
                    </button>
                    <button
                      className="p-1 hover:bg-gray-200 rounded transition-colors"
                      onClick={() =>
                        console.log(
                          `More actions for test case: ${testCase.name}`
                        )
                      }
                    >
                      <MoreVertical className="h-4 w-4 text-gray-600" />
                    </button>
                  </div>
                </div>
              );
            })}
          </>
        )}
      </div>
    </div>
  );
}

export default function Page() {
   const router = useRouter();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [editingTestSuite, setEditingTestSuite] = useState<TestSuite | null>(
    null
  );
  const [testSuites, setTestSuites] = useState<TestSuite[]>([]);
  const [allTestFlows, setAllTestFlows] = useState<TestFlow[]>([]);
  const [allTestCases, setAllTestCases] = useState<TestCase[]>([]);
  const [selectedTestSuite, setSelectedTestSuite] = useState<string | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [runningTestSuite, setRunningTestSuite] = useState(false);
  const [projectName, setProjectName] = useState("Loading...");

  // Enhanced handleStopTestSuite with better error handling
  const handleStopTestSuite = async () => {
    if (!selectedTestSuite) return;

    // Immediately show stopping state
    setRunningTestSuite(false);


try {
  const response = await axios.post('/api/test-suite', {
    action: 'stop',
    suiteName: selectedTestSuite,
  });

  const result = response.data;
  console.log(`Stop result:`, result);

  if (result.success && result.action === 'STOPPED') {
    toast({
      title: 'Stopped',
      description: result.message || 'Test suite stopped successfully',
      variant: 'success',
    });
  } else if (result.success) {
    toast({
      title: 'Stopped',
      description: result.message || 'Test suite stopped successfully',
      variant: 'success',
    });
  } else {
    const errorMessage = result.error || result.message || 'Unknown error occurred';
    toast({
      title: 'Failed to stop',
      description: errorMessage,
      variant: 'destructive',
    });
  }

  router.push('/dashboard');

} catch (error: any) {
  console.error('Stop error:', error);

  const errorMessage =
    error.response?.data?.error ||
    error.response?.data?.message ||
    error.message ||
    'Unknown error occurred';

  toast({
    title: 'Network error',
    description: errorMessage,
    variant: 'destructive',
  });
}

     
  };

  useEffect(() => {
    // Fetch all data concurrently
    async function fetchData() {
      setLoading(true);
      setError(null);
      try {


const [suites, flows, testCases, configResponse] = await Promise.all([
  getTestSuitesFromAPI(),
  getTestFlowsFromAPI(),
  getTestCasesFromAPI(),
  axios.get('/api/get-config'),
]);

const config = configResponse.data;

        setProjectName(config.projectName || "No Project Selected");

        setTestSuites(suites);
        setAllTestFlows(flows);
        setAllTestCases(testCases);

        // Set default test suite
        if (!selectedTestSuite && suites.length > 0) {
          const defaultSuite = suites.find(
            (suite) => suite.name === "testsuite1"
          );
          setSelectedTestSuite(
            defaultSuite ? defaultSuite.name : suites[0].name
          );
        }
      } catch (err) {
        console.error(err);
        setError("Failed to load test suites, flows, or test cases");
      } finally {
        setLoading(false);
        
      }
    }
    fetchData();
  }, []);

  // AUTO-REFRESH MECHANISM
  const handleDialogChange = async (open: boolean) => {
    setDialogOpen(open);
    if (!open) {
      // Reset edit mode when closing
      setEditMode(false);
      setEditingTestSuite(null);

      // Refresh all data after dialog closes
      setLoading(true);
      setError(null);
      try {
        const [suites, flows, testCases] = await Promise.all([
          getTestSuitesFromAPI(),
          getTestFlowsFromAPI(),
          getTestCasesFromAPI(),
        ]);
        setTestSuites(suites);
        setAllTestFlows(flows);
        setAllTestCases(testCases);

        // Set selected test suite to first one if none selected
        if (!selectedTestSuite && suites.length > 0) {
          setSelectedTestSuite(suites[0].name);
        }
      } catch (err) {
        console.error(err);
        setError("Failed to refresh test suites, flows, or test cases");
      } finally {
        setLoading(false);
      }
    }
  };

  // Handle edit button click
  const handleEditTestSuite = () => {
    if (selectedTestSuite) {
      const suiteToEdit = testSuites.find(
        (suite) => suite.name === selectedTestSuite
      );
      if (suiteToEdit) {
        setEditingTestSuite(suiteToEdit);
        setEditMode(true);
        setDialogOpen(true);
      }
    }
  };

  // Enhanced handleRunTestSuite with better error handling
  const handleRunTestSuite = async () => {
   //   const router = useRouter();
    if (!selectedTestSuite) {
      toast({
        title: "No test suite selected",
        description: "Please select a test suite first",
        variant: "destructive",
      });

      return;
    }

    setRunningTestSuite(true);

    try {
      console.log(`Running test suite: ${selectedTestSuite}`);



const response = await axios.post('/api/test-suite', {
  action: 'run',
  suiteName: selectedTestSuite,
});

      // Check if response is ok
      if (!response.status.toString().startsWith('2')) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.data;
      console.log("🔍 API Response:", result);

      // Handle different response scenarios with better error checking
      if (result.action === "STOPPED_DURING_RUN") {
        console.log("Test suite was stopped during execution");
        toast({
          title: "Stopped",
          description: result.message || "Test suite was stopped",
          variant: "success",
        });
      } else if (result.action === "COMPLETED" && result.success) {
        console.log("Test suite completed successfully");
        toast({
          title: "Completed",
          description: result.message || "Test suite completed successfully",
          variant: "success",
        });
      } else if (result.action === "FAILED") {
        console.log("Test suite failed");
        toast({
          title: "Failed",
          description: result.message || "Test suite execution failed",
          variant: "destructive",
        });
      } else if (result.success === true) {
        console.log("Test suite executed");
        toast({
          title: "Success",
          description: `Test suite "${selectedTestSuite}" executed successfully!`,
          variant: "success",
        });
      } else if (result.success === false) {
        // Handle explicit failure with better error message
        const errorMessage =
          result.error || result.message || "Unknown error occurred";
        console.error("Test suite run failed:", errorMessage);
        toast({
          title: "Failed to run",
          description: errorMessage,
          variant: "destructive",
        });
      } else {
        // Handle unexpected response format
        console.warn("Unexpected response format:", result);
        toast({
          title: "Unexpected response",
          description: "Check console for details",
        });
      }
       router.push("/dashboard"); // Redirect to dashboard after running
        
    } catch (error) {
      console.error("Network/Parsing error:", error);

      // Better error message handling
      let errorMessage = "Unknown error occurred";
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === "string") {
        errorMessage = error;
      }

      toast({
        title: "Network error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setRunningTestSuite(false);
         
    }
       
  };

  if (loading) {
    return (
      <SidebarProvider
        style={{ "--sidebar-width": "45px" } as React.CSSProperties}
      >
        <AppSidebar />
        <SidebarInset>
          <div className="flex h-screen items-center justify-center">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#15537C]"></div>
              <span>Loading test suites and data...</span>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  if (error) {
    return (
      <SidebarProvider
        style={{ "--sidebar-width": "45px" } as React.CSSProperties}
      >
        <AppSidebar />
        <SidebarInset>
          <div className="flex h-screen items-center justify-center">
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600 font-medium">Error loading data</p>
              <p className="text-red-500 text-sm">{error}</p>
              <Button
                onClick={() => window.location.reload()}
                className="mt-2 text-sm"
                variant="outline"
              >
                Retry
              </Button>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider
      style={{ "--sidebar-width": "45px" } as React.CSSProperties}
    >
      <AppSidebar />
      <SidebarInset>
        <div className="flex h-screen overflow-hidden">
          {/* Test Suites Sidebar */}
          <aside className="w-[15%] border-r bg-muted p-2 space-y-1 bg-[#F7F9FC] font-normal tracking-normal font-roboto">
            <div className="bg-[#15537C] text-white px-2 py-1 flex items-center space-x-1 hover:brightness-110 rounded-xs justify-start">
              <span className="font-bold text-sm ml-1 truncate whitespace-nowrap overflow-hidden max-w-[160px]">
                Test Suites
              </span>
            </div>
            <ul className="text-[#37393A] py-2 flex flex-col gap-2 rounded-xs">
              {testSuites.map((suite) => (
                <li
                  key={suite.name}
                  className="text-left w-full text-sm hover:bg-[#B1DBEA]"
                >
                  <button
                    className={`w-full px-2 py-1 text-left${
                      selectedTestSuite === suite.name
                        ? " bg-[#B1DBEA] text-[#15537C] font-medium"
                        : ""
                    }`}
                    onClick={() => setSelectedTestSuite(suite.name)}
                    title={suite.description}
                  >
                    <div className="truncate">{suite.name}</div>
                  </button>
                </li>
              ))}
            </ul>
          </aside>

          {/* Main Content */}
          <div className="flex-1 flex flex-col overflow-hidden">
            <header className="bg-background flex items-center justify-between border-b p-2">
              <div className="flex items-center space-x-3">
                <h1 className="text-left font-medium text-[24px] leading-[20px] tracking-[0px] text-[#2C2E2E]">
                  {selectedTestSuite
                    ? `${projectName} - ${selectedTestSuite}`
                    : "Test Suites"}
                </h1>

                {selectedTestSuite && (
                  <button
                    onClick={handleEditTestSuite}
                    className="p-2 hover:bg-gray-200 rounded transition-colors"
                    title={`Edit ${selectedTestSuite}`}
                  >
                    <Edit className="h-4 w-4 text-gray-600" />
                  </button>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  className="bg-[#15537C] text-white hover:brightness-110 rounded-md w-34 h-6"
                  onClick={() => {
                    setEditMode(false);
                    setEditingTestSuite(null);
                    setDialogOpen(true);
                  }}
                  disabled={runningTestSuite}
                >
                  Create Test Suite
                </Button>
                {!runningTestSuite ? (
                  <Button
                    className="bg-[#15537C] text-white hover:brightness-110 rounded-md w-34 h-6"
                    onClick={handleRunTestSuite}
                    disabled={!selectedTestSuite}
                  >
                    Run Test Suite
                  </Button>
                ) : (
                  <div className="flex items-center space-x-2">
                    <Button
                      className="bg-gray-500 text-white rounded-md w-34 h-6 flex items-center space-x-2 cursor-not-allowed"
                      disabled
                    >
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
                      <span>Running...</span>
                    </Button>
                    {/* Stop button */}
                    <Button
                      className="bg-red-600 text-white hover:bg-red-700 rounded-md w-24 h-6"
                      onClick={handleStopTestSuite}
                    >
                      Stop
                    </Button>
                  </div>
                )}
              </div>
            </header>
            <div className="flex-1 overflow-auto p-4">
              <TestSuiteContent
                testSuiteName={selectedTestSuite}
                testSuites={testSuites}
                allTestFlows={allTestFlows}
                allTestCases={allTestCases}
              />
            </div>
          </div>
        </div>
      </SidebarInset>
      <CreateTestSuiteDialog
        open={dialogOpen}
        onOpenChange={handleDialogChange}
        editMode={editMode}
        editingTestSuite={editingTestSuite}
      />
    </SidebarProvider>
  );
}

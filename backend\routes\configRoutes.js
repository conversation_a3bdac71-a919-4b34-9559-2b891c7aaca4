
const express = require('express');
const router = express.Router();
const configController = require('../controllers/configController');

// GET config data from Excel file
router.get('/read', configController.readConfig);
// router.put('/update/:rowIndex',configController.updateConfig);
// router.delete('/delete',configController.deleteConfig);
router.post('/save',configController.saveConfig);

router.get('/envnames'),

module.exports = router;


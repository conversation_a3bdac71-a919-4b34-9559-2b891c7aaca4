"use client";
import { useState } from "react";
import ConfigPage from "./newconfig";
import ConfigWeb from "./configweb";

export function ConfigManagement() {
    const [selectedTab, setSelectedTab] = useState("web");

    return (
        <div>
            <header className="bg-background sticky top-0 flex items-center justify-between border-b p-[20px]">
                 <h1 className="text-left font-medium text-[24px] leading-[20px] tracking-[0px] text-[#2C2E2E]">
                    Project Configurations
                </h1>
            </header>
            <div className="navbar pl-5 bg-[#15537c]">
                <div className="text-sm font-medium text-center text-gray-500 border-gray-200 dark:text-gray-400 dark:border-gray-700">
                <ul className="flex flex-wrap">
                    <li className="me-2 mt-2">
                        <a
                            className={`${selectedTab === "web" ? "inline-block px-8 py-2 rounded-t-lg text-[#1e2939] bg-white active dark:text-blue-500" : "inline-block px-8 py-2 border-transparent rounded-t-lg text-[#fff] cursor-pointer"}`}
                            onClick={() => {
                                setSelectedTab("web");
                            }}
                        >
                            Web
                        </a>
                    </li>
                    <li className="me-2 mt-2">   
                        <a
                        className={`${selectedTab === "API" ? "inline-block px-8 py-2 rounded-t-lg text-[#1e2939] bg-white active dark:text-blue-500" : "inline-block px-8 py-2 border-transparent rounded-t-lg text-[#fff] cursor-pointer"}`}
                            aria-current="page"
                            onClick={() => {
                                setSelectedTab("API");
                            }}
                        >
                            API
                        </a>
                    </li>
                </ul>
                </div>
            </div>
            <div>
                {selectedTab === "web" ? <ConfigWeb /> : <ConfigPage /> }
            </div>
        </div>
    );
}

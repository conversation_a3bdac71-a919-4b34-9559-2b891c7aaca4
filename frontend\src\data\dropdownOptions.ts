export const ASSIGNEES = [
    "<PERSON><PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
  ];
  
  export const ROLES = [
    "<PERSON>A <PERSON>",
    "<PERSON>er",
    "<PERSON><PERSON><PERSON>",
    "Project Manager",
  ];

  export const REQUEST_TYPE_OPTIONS: RequestTypeOption[] = [
  { value: "get", label: "GET" },
  { value: "post", label: "POST" },
  { value: "put", label: "PUT" },
  { value: "delete", label: "DELETE" },
  { value: "patch", label: "PATCH" },
];

export const REQUEST_TYPE_OPTIONS_FILTER: RequestTypeOption[] = [
  { value: "all", label: "ALL" },
  ...REQUEST_TYPE_OPTIONS,
];

export interface RequestTypeOption {
  value: string;
  label: string;
}

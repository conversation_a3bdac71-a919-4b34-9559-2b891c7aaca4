"use client"
import {
  Tooltip as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YA<PERSON>s,
  ResponsiveContainer,
  CartesianGrid,
} from "recharts"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { barData } from "@/data/barData"

export default function Testexecution() {
    return (
        <div>
          <Card>
          <CardHeader>
            <CardTitle className="text-lg">Test Execution Summary</CardTitle>
          </CardHeader>
          <CardContent className="w-full h-[300px] max-w-full overflow-hidden">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={barData} margin={{ top: 15, right: 15, bottom: 15, left: 15 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" type="category" />
                <YAxis type="number" />
                <RechartsTooltip />
                <Legend />
                <Bar dataKey="Passed" stackId="a" fill="#0D47A1" /> {/* darker blue */}
                <Bar dataKey="Failed" stackId="a" fill="#EF4444" /> {/* rose red */}
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
        </div>
         )
        }
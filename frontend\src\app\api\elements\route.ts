import { promises as fs } from "fs";
import path from "path";
import { NextResponse } from "next/server";
import config from "@/config/config.json";

// Absolute folder that contains the *.properties files
const ELEMENT_DIR = config.elementsPath;

/** Convert ONE line of a .properties file into a UIElement object */
function parsePropertyLine(
  rawLine: string,
  runningId: number,
  moduleName: string // NEW: Add moduleName parameter
): {
  id: number;
  name: string;
  fullKey: string;
  type: string;
  value: string;
  screenshot: string;
  moduleName: string; // NEW: Add moduleName field
} | null {
  const trimmed = rawLine.trim();

  // ignore blank lines or comments that usually start with # or !
  if (!trimmed || trimmed.startsWith("#") || trimmed.startsWith("!"))
    return null;

  // expecting the canonical `key=value` form
  const equalIdx = trimmed.indexOf("=");
  if (equalIdx === -1) return null;

  const key = trimmed.slice(0, equalIdx).trim();
  const val = trimmed.slice(equalIdx + 1).trim();

  /* key looks like  firstname_id_xpath   OR   input-1234_id_css  */
  const idSplitter = "_id_";
  const splitIdx = key.indexOf(idSplitter);
  if (splitIdx === -1) {
    // fallback → treat everything before first '_' as name, after last '_' as type
    const parts = key.split("_");
    return {
      id: runningId,
      name: parts[0],
      fullKey: key, // Full key for AccordionTable dropdown
      type: parts.pop() ?? "",
      value: val,
      screenshot: "",
      moduleName: moduleName, // NEW: Include moduleName
    };
  }

  const namePart = key.slice(0, splitIdx); // firstname
  const typePart = key.slice(splitIdx + idSplitter.length); // xpath / css / ...
  return {
    id: runningId,
    name: namePart,
    fullKey: key, // Full key: "firstname_id_xpath"
    type: typePart,
    value: val,
    screenshot: "",
    moduleName: moduleName, // NEW: Include moduleName
  };
}

/** Parse a whole .properties file into an array of UIElement objects */
async function parsePropertyFile(filePath: string, moduleName: string) {
  // NEW: Add moduleName parameter
  const raw = await fs.readFile(filePath, "utf-8");
  const lines = raw.split(/\r?\n/);

  const elements = [];
  let counter = 1;
  for (const ln of lines) {
    const parsed = parsePropertyLine(ln, counter, moduleName); // NEW: Pass moduleName
    if (parsed) {
      elements.push(parsed);
      counter += 1;
    }
  }
  return elements;
}

// GET handler
export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const wantedFile = url.searchParams.get("file"); // ?file=session_1

    // Case 1 – client asked for ONE file
    if (wantedFile) {
      const fullPath = path.join(ELEMENT_DIR, wantedFile);
      const moduleName = path.parse(wantedFile).name; // NEW: Extract moduleName
      const elements = await parsePropertyFile(fullPath, moduleName); // NEW: Pass moduleName

      return NextResponse.json({
        id: 1,
        name: path.parse(wantedFile).name,
        elements,
      });
    }

    /* Case 2 – return ALL property files */
    const dirEntries = await fs.readdir(ELEMENT_DIR, { withFileTypes: true });

    const propertyFiles = dirEntries
      .filter((ent) => ent.isFile())
      // ignore obvious non-property helper docs (e.g. readme.txt)
      .filter((ent) => !ent.name.toLowerCase().endsWith(".txt"))
      .map((ent) => ent.name);

    const groups = await Promise.all(
      propertyFiles.map(async (fileName, idx) => {
        const fullPath = path.join(ELEMENT_DIR, fileName);
        const moduleName = path.parse(fileName).name; // NEW: Extract moduleName from filename
        const elements = await parsePropertyFile(fullPath, moduleName); // NEW: Pass moduleName
        return {
          id: idx + 1,
          name: path.parse(fileName).name,
          elements,
        };
      })
    );

    return NextResponse.json(groups);
  } catch (err) {
    console.error("Error reading element property files:", err);
    return NextResponse.json(
      { error: "Failed to load element properties" },
      { status: 500 }
    );
  }
}

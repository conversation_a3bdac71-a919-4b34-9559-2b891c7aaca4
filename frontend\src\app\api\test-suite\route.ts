import { NextRequest, NextResponse } from "next/server";
import { promises as fs } from "fs";
import path from "path";
import { exec } from "child_process";
import { promisify } from "util";


const execAsync = promisify(exec);

// Track running process for stop functionality
let runningProcess: any = null;

// Function to get test suites file path from config
async function getTestSuitesFilePath(): Promise<string> {
  const configPath = path.join(process.cwd(), "src", "config", "config.json");
  const configContent = await fs.readFile(configPath, "utf8");
  const config = JSON.parse(configContent);

  if (!config.testsuitepath) {
    throw new Error(
      "Config.json is missing testsuitepath. Please create a project first."
    );
  }

  return config.testsuitepath;
}

interface TestSuite {
  name: string;
  description: string;
  testFlows: string[];
  testCases: string[];
}

// GET - Fetch all test suites
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get("type");

    // Read from file
    let testSuites: TestSuite[] = [];
    try {
      const TEST_SUITES_FILE_PATH = await getTestSuitesFilePath();
      const fileContents = await fs.readFile(TEST_SUITES_FILE_PATH, "utf8");
      testSuites = JSON.parse(fileContents);
    } catch (error) {
      // If file doesn't exist, return empty array
      testSuites = [];
    }

    // If type=names, return only basic info for lists/selectors
    if (type === "names") {
      const basicInfo = testSuites.map((suite, index) => ({
        id: index + 1,
        name: suite.name,
        type: "Test Suite",
      }));
      return NextResponse.json(basicInfo);
    }

    // Return full test suites data
    return NextResponse.json(testSuites);
  } catch (error) {
    console.error("Error fetching test suites:", error);
    return NextResponse.json({ error: "Failed to fetch test suites" });
  }
}

export async function POST(request: NextRequest) {
  try {
    const requestData = await request.json();

    // Check if this is a "run test suite" request
    if (requestData.action === "run" && requestData.suiteName) {
      return await runTestSuite(requestData.suiteName);
    }

    // Check if this is a "stop test suite" request
    if (requestData.action === "stop" && requestData.suiteName) {
      return await stopTestSuite(requestData.suiteName);
    }

    // Otherwise, handle "create test suite" request
    const { name, description, testFlows = [], testCases = [] } = requestData;

    // Validation
    if (!name || !name.trim()) {
      return NextResponse.json(
        { error: "Test suite name is required" },
        { status: 400 }
      );
    }

    if (!Array.isArray(testFlows) || !Array.isArray(testCases)) {
      return NextResponse.json(
        { error: "testFlows and testCases must be arrays" },
        { status: 400 }
      );
    }

    if (testFlows.length === 0 && testCases.length === 0) {
      return NextResponse.json(
        { error: "At least one test flow or test case must be selected" },
        { status: 400 }
      );
    }

    // Read existing test suites
    let testSuites: TestSuite[] = [];
    try {
      const TEST_SUITES_FILE_PATH = await getTestSuitesFilePath();
      const fileContents = await fs.readFile(TEST_SUITES_FILE_PATH, "utf8");
      testSuites = JSON.parse(fileContents);
    } catch (error) {
      // File doesn't exist, start with empty array
      testSuites = [];
    }

    // Check if test suite with same name already exists
    const exists = testSuites.find(
      (suite) => suite.name.toLowerCase() === name.trim().toLowerCase()
    );
    if (exists) {
      return NextResponse.json(
        { error: "Test suite with this name already exists" },
        { status: 409 }
      );
    }

    // Create new test suite with only required fields
    const newTestSuite: TestSuite = {
      name: name.trim(),
      description: description?.trim() || "",
      testFlows: Array.from(new Set(testFlows)), // Remove duplicates
      testCases: Array.from(new Set(testCases)), // Remove duplicates
    };

    // Add to array
    testSuites.push(newTestSuite);

    // Ensure directory exists and write to file
    const TEST_SUITES_FILE_PATH_WRITE = await getTestSuitesFilePath();
    const dir = path.dirname(TEST_SUITES_FILE_PATH_WRITE);
    await fs.mkdir(dir, { recursive: true });
    await fs.writeFile(
      TEST_SUITES_FILE_PATH_WRITE,
      JSON.stringify(testSuites, null, 2),
      "utf8"
    );

    return NextResponse.json(
      {
        success: true,
        message: "Test suite created successfully",
        testSuite: newTestSuite,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error in POST request:", error);
    return NextResponse.json(
      { error: "Failed to process request" },
      { status: 500 }
    );
  }
}

// Helper function to read config.json
async function getProjectWorkingDirectory(): Promise<string> {
  const configPath = path.join(process.cwd(), "src", "config", "config.json");
  const configContent = await fs.readFile(configPath, "utf8");
  const config = JSON.parse(configContent);

  if (!config.currentProjectPath) {
    throw new Error(
      "Config.json is missing currentProjectPath. Please create a project first."
    );
  }

  return config.currentProjectPath;
}

// Enhanced stop function with clearer messaging
async function stopTestSuite(suiteName: string) {
  console.log(`STOPPING test suite: ${suiteName}`);

  try {
    if (runningProcess) {
      console.log(`Terminating Maven process for: ${suiteName}`);

      // Kill the Maven process with all its children
      if (process.platform === "win32") {
        // Windows - Kill the entire process tree
        exec(`taskkill /pid ${runningProcess.pid} /T /F`, (error) => {
          if (error) {
            console.log("Error killing Maven process:", error);
          } else {
            console.log("Maven process tree killed successfully");
          }
        });
      } else {
        // Unix-like systems
        runningProcess.kill("SIGKILL");
        console.log("Maven process killed with SIGKILL");
      }

      runningProcess = null;

      // Wait a moment for process cleanup
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Additional cleanup - kill any remaining test processes
      if (process.platform === "win32") {
        // Kill any remaining WebDriver processes
        exec("taskkill /IM chromedriver.exe /F", () => {});
        exec("taskkill /IM geckodriver.exe /F", () => {});
        exec("taskkill /IM msedgedriver.exe /F", () => {});
        exec("taskkill /IM safaridriver.exe /F", () => {});

        // Kill browsers with automation flags
        exec(
          "wmic process where \"name='chrome.exe' and commandline like '%--remote-debugging-port%'\" delete",
          () => {}
        );
        exec(
          "wmic process where \"name='firefox.exe' and commandline like '%-marionette%'\" delete",
          () => {}
        );

        console.log("Windows browser cleanup completed");
      } else {
        // Unix cleanup
        exec("pkill -f chromedriver", () => {});
        exec("pkill -f geckodriver", () => {});
        exec('pkill -f "chrome.*--remote-debugging-port"', () => {});
        exec('pkill -f "firefox.*-marionette"', () => {});

        console.log("Unix browser cleanup completed");
      }

      console.log(
        `Test suite "${suiteName}" STOPPED and all processes terminated`
      );

      return NextResponse.json({
        success: true,
        message: `Test suite "${suiteName}" STOPPED - All processes terminated`,
        action: "STOPPED",
      });
    } else {
      console.log("No running process found, but attempting cleanup anyway");

      // Even if no tracked process, try to clean up any running test processes
      if (process.platform === "win32") {
        exec("taskkill /IM chromedriver.exe /F", () => {});
        exec("taskkill /IM geckodriver.exe /F", () => {});
        exec(
          "wmic process where \"name='chrome.exe' and commandline like '%--remote-debugging-port%'\" delete",
          () => {}
        );
      } else {
        exec("pkill -f chromedriver", () => {});
        exec("pkill -f geckodriver", () => {});
      }

      return NextResponse.json({
        success: true,
        message:
          "STOPPED - No active test suite found, but cleaned up any remaining processes",
        action: "STOPPED",
      });
    }
  } catch (error) {
    console.error("Error stopping test suite:", error);
    return NextResponse.json({
      success: false,
      error: "Failed to stop test suite",
      action: "STOP_FAILED",
    });
  }
}

// Helper function for running test suites with proper stop detection
async function runTestSuite(suiteName: string) {
  try {
    if (!suiteName || !suiteName.trim()) {
      return NextResponse.json(
        { error: "Test suite name is required" },
        { status: 400 }
      );
    }

    console.log(`Starting test suite run: ${suiteName}`);

    // Step 1: Read test suites and find the selected one
    let testSuites: TestSuite[] = [];
    try {
      console.log("Reading test suites...");
      const TEST_SUITES_FILE_PATH = await getTestSuitesFilePath();
      const fileContents = await fs.readFile(TEST_SUITES_FILE_PATH, "utf8");
      testSuites = JSON.parse(fileContents);
      console.log("Test suites loaded successfully");
    } catch (error) {
      console.error("Failed to read test suites file:", error);
      return NextResponse.json(
        {
          success: false,
          error: "Test suites file not found or could not be read",
        },
        { status: 500 }
      );
    }

    // Step 2: Find the specific test suite
    const testSuite = testSuites.find(
      (suite) => suite.name.toLowerCase() === suiteName.trim().toLowerCase()
    );

    if (!testSuite) {
      return NextResponse.json(
        {
          success: false,
          error: `Test suite "${suiteName}" not found`,
        },
        { status: 404 }
      );
    }

    if (!testSuite.testFlows || testSuite.testFlows.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: `Test suite "${suiteName}" has no test flows to execute`,
        },
        { status: 400 }
      );
    }

    // Step 3: Build the Maven command with test flows
    console.log(
      `Building command for test flows: ${testSuite.testFlows.join(", ")}`
    );

    // Step 4: Get dynamic working directory from config
    const workingDirectory = await getProjectWorkingDirectory();
    const batchFileFolder = path.join(workingDirectory, "antp-web");
    const batchFilePath = path.join(batchFileFolder, "mvnrun.bat");

    // Build full command string with absolute batch file path and test flows
    let cmdCommand = `"${batchFilePath}" `;

    for (let i = 0; i < testSuite.testFlows.length; i++) {
      if (i === 0) {
        cmdCommand += `"@${testSuite.testFlows[i]}`;
      } else {
        cmdCommand += ` or @${testSuite.testFlows[i]}`;
      }
    }
    cmdCommand += `"`;

    console.log(`Executing command: ${cmdCommand}`);
    console.log(`Using working directory: ${batchFileFolder}`);

    // Check if batch file exists before execution
    try {
      await fs.access(batchFilePath);
      console.log(`Batch file found: ${batchFilePath}`);
    } catch (err) {
      console.error(`Batch file NOT found at: ${batchFilePath}`);
      return NextResponse.json(
        { error: `Batch file not found at: ${batchFilePath}` },
        { status: 500 }
      );
    }

    // Step 5: Execute the Maven command with proper termination detection
   // Execute the Maven command with enhanced process management
try {
  console.log("Running Maven tests...");
  console.log("Full command being executed:", cmdCommand);

  // Use direct exec instead of execAsync for better process control
  runningProcess = exec(cmdCommand, { 
    cwd: batchFileFolder,  // Keep your existing working directory logic
    shell: "cmd.exe"       // Keep your existing shell setting
  });

  const { stdout, stderr, wasKilled } = await new Promise<{
    stdout: string;
    stderr: string;
    wasKilled: boolean;
  }>((resolve, reject) => {
    let stdoutData = "",
        stderrData = "";
    let processKilled = false;

    runningProcess.stdout.on(
      "data",
      (data: string) => (stdoutData += data)
    );
    runningProcess.stderr.on(
      "data",
      (data: string) => (stderrData += data)
    );

    runningProcess.on(
      "close",
      (code: number | null, signal: string | null) => {
        console.log(`Process closed with code: ${code}, signal: ${signal}`);
        runningProcess = null;
  
        // Check if process was killed/terminated
        if (
          signal === "SIGTERM" ||
          signal === "SIGKILL" ||
          code === 1 ||
          processKilled
        ) {
          console.log(
            `Process was terminated with signal: ${signal} or code: ${code}`
          );
          resolve({
            stdout: stdoutData,
            stderr: stderrData,
            wasKilled: true,
          });
        } else {
          resolve({
            stdout: stdoutData,
            stderr: stderrData,
            wasKilled: false,
          });
        
        }

      }
    );

    runningProcess.on("error", (error: Error) => {
      console.log(`Process error: ${error.message}`);
      runningProcess = null;
      reject(error);
    });

    // Handle external termination
    runningProcess.on(
      "exit",
      (code: number | null, signal: string | null) => {
        console.log(`Process exited with code: ${code}, signal: ${signal}`);
        if (signal === "SIGTERM" || signal === "SIGKILL") {
          processKilled = true;
        }
      }
    );
  });

  // Check if the process was killed/stopped
  if (wasKilled) {
    console.log(`Test suite "${suiteName}" was STOPPED during execution`);
    return NextResponse.json({
      success: false,
      message: `Test suite "${suiteName}" was STOPPED by user`,
      action: "STOPPED_DURING_RUN",
      details: {
        command: cmdCommand,
        testFlows: testSuite.testFlows,
        executed: false,
        stopped: true,
        workingDirectory: batchFileFolder, // Use your existing variable
      },
    });
  }

  console.log("Maven command executed successfully");
 
  console.log("STDOUT:", stdout);
  console.log("STDERR:", stderr);

  return NextResponse.json({
    success: true,
    message: `Test suite "${suiteName}" run completed successfully`,
    action: "COMPLETED", // Add this action field
    details: {
      command: cmdCommand,
      testFlows: testSuite.testFlows,
      executed: true,
      stdout: stdout,
      stderr: stderr,
      workingDirectory: batchFileFolder, // Use your existing variable
    },
  });

} catch (error) {
  runningProcess = null;
  console.error("Maven command completed with errors:", error);

  return NextResponse.json({
    success: false, // Change this to false for better error handling
    message: `Test suite "${suiteName}" failed during execution`,
    action: "FAILED", // Add this action field
    details: {
      command: cmdCommand,
      testFlows: testSuite.testFlows,
      executed: false,
      error: error && typeof error === "object" && "message" in error
        ? (error as { message: string }).message
        : String(error),
      workingDirectory: batchFileFolder, // Use your existing variable
    },
  });
  
}
  } catch (error) {
    console.error("Error running test suite:", error);
    return NextResponse.json(
      { error: "Failed to run test suite" },
      { status: 500 }
    );
  }
}

// PUT - Update an existing test suite
export async function PUT(request: NextRequest) {
  try {
    const requestData = await request.json();
    const {
      originalName,
      name,
      description,
      testFlows = [],
      testCases = [],
    } = requestData;

    // Validation
    if (!originalName || !originalName.trim()) {
      return NextResponse.json(
        { error: "Original test suite name is required for updating" },
        { status: 400 }
      );
    }

    if (!name || !name.trim()) {
      return NextResponse.json(
        { error: "Test suite name is required" },
        { status: 400 }
      );
    }

    if (!Array.isArray(testFlows) || !Array.isArray(testCases)) {
      return NextResponse.json(
        { error: "testFlows and testCases must be arrays" },
        { status: 400 }
      );
    }

    if (testFlows.length === 0 && testCases.length === 0) {
      return NextResponse.json(
        { error: "At least one test flow or test case must be selected" },
        { status: 400 }
      );
    }

    // Read existing test suites
    let testSuites: TestSuite[] = [];
    try {
      const TEST_SUITES_FILE_PATH = await getTestSuitesFilePath();
      const fileContents = await fs.readFile(TEST_SUITES_FILE_PATH, "utf8");
      testSuites = JSON.parse(fileContents);
    } catch (error) {
      return NextResponse.json(
        { error: "Test suites file not found" },
        { status: 404 }
      );
    }

    // Find the test suite to update
    const suiteIndex = testSuites.findIndex(
      (suite) => suite.name.toLowerCase() === originalName.trim().toLowerCase()
    );

    if (suiteIndex === -1) {
      return NextResponse.json(
        { error: "Test suite not found" },
        { status: 404 }
      );
    }

    // If name is being changed, check if new name already exists
    if (originalName.toLowerCase() !== name.trim().toLowerCase()) {
      const nameExists = testSuites.find(
        (suite) => suite.name.toLowerCase() === name.trim().toLowerCase()
      );
      if (nameExists) {
        return NextResponse.json(
          { error: "Test suite with this name already exists" },
          { status: 409 }
        );
      }
    }

    // Update the test suite
    const updatedTestSuite: TestSuite = {
      name: name.trim(),
      description: description?.trim() || "",
      testFlows: Array.from(new Set(testFlows)), // Remove duplicates
      testCases: Array.from(new Set(testCases)), // Remove duplicates
    };

    testSuites[suiteIndex] = updatedTestSuite;

    // Write back to file
    const TEST_SUITES_FILE_PATH_WRITE = await getTestSuitesFilePath();
    await fs.writeFile(
      TEST_SUITES_FILE_PATH_WRITE,
      JSON.stringify(testSuites, null, 2),
      "utf8"
    );

    return NextResponse.json(
      {
        success: true,
        message: "Test suite updated successfully",
        testSuite: updatedTestSuite,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating test suite:", error);
    return NextResponse.json(
      { error: "Failed to update test suite" },
      { status: 500 }
    );
  }
}

"use client";

import { useState, useEffect, useRef } from "react";
import { createPortal } from "react-dom";
import { useToast } from "@/hooks/use-toast";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { GripVertical, Plus } from "lucide-react";
import { fetchConfigs } from "@/services/config";
import { ChevronDown } from "lucide-react";
import axios from "axios";
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult,
} from "@hello-pangea/dnd";

type Config = {
  CollectionName: string;
  DBExportVariables: string;
  Dependent: string;
  Environment: string;
  Execute: string;
  ExportVariable: string;
  HeaderParameter: string;
  Module: string;
  Query: string;
  QueryParameter: string;
  ResponseValidation: string;
  SamplerProxyBody: string;
  SamplerProxyMethod: string;
  SamplerProxyName: string;
  SamplerProxyPath: string;
  SamplerProxyResponseCode: string;
};

type UpdatedConfig = {
  rowIndex: number;
  Environment: string;
  URI: string;
  DBtype: string;
  DBhost: string;
  DBport: string;
  DBuser: string;
  DBpwd: string;
  DBname: string;
};

type deleteConfig = {
  rowIndex: number;
};

export default function TestFlowUIList(selectedTestFlow: any) {
  const [configs, setConfigs] = useState<Config[]>([]);
  const [searchConfig, setSearchConfig] = useState<string>("");
  const [environments, setEnvironments] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [allowEditing, setAllowEditing] = useState(true);
  const [testFlowData, setTestFlowData] = useState<Config[]>([]);
  const columns: (keyof Config)[] =
    configs.length > 0 ? (Object.keys(configs[0]) as (keyof Config)[]) : [];
  const placeholders: string[] = [
    "",
    "",
    "",
    "Enter Environment",
  ];
  const [selectedConfigs, setSelectedConfigs] = useState<boolean[]>(
    Array(configs.length).fill(false)
  );

  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  const [editingCell, setEditingCell] = useState<{
    row: number | null;
    field: string | null;
  }>({ row: null, field: null });

  const [envDropdownOpen, setEnvDropdownOpen] = useState<
    Record<number, boolean>
  >({});
  const dropdownRefs = useRef<(HTMLDivElement | null)[]>([]);
  const dropdownTriggerRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [dropdownPosition, setDropdownPosition] = useState<{
    top: number;
    left: number;
    width: number;
  } | null>(null);

  const [columnWidths, setColumnWidths] = useState<number[]>([]);
  const tableRef = useRef<HTMLTableElement>(null);

  useEffect(() => {
    if (tableRef.current && configs.length > 0) {
      const headerCells = tableRef.current.querySelectorAll('thead th');
      const widths = Array.from(headerCells).map(cell => cell.clientWidth);
      setColumnWidths(widths);
    }
  }, [configs]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      let clickInside = false;
      dropdownRefs.current.forEach((ref) => {
        if (ref && ref.contains(event.target as Node)) {
          clickInside = true;
        }
      });
      if (!clickInside) {
        setEnvDropdownOpen({});
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    fetchConfigs().then((data) => setEnvironmentArray(data));
  }, []);

  const setEnvironmentArray = (data: any) => {
    const environmentsTmp: string[] = [];
    data.map((config) => {
      environmentsTmp.push(config.Environment);
    });
    setEnvironments(environmentsTmp);
  };

  const fetchTestFlowData = async () => {
    setLoading(true);
    if (selectedTestFlow) {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/testFlowUI/${selectedTestFlow.selectedTestFlow}`
        );

        if (!response.ok) {
          throw new Error(`Failed to fetch configs: ${response.status}`);
        }

        const userData = await response.json();
        setTestFlowData(userData.data);
        const requiredKeys = [
          "Execute",
          "Module",
          "SamplerProxyName",
          "Environment",
        ];
        const filteredColumns = userData.data.map((obj) =>
          Object.fromEntries(requiredKeys.map((key) => [key, obj[key]]))
        );
        setConfigs(filteredColumns);
      } catch (error: any) {
        console.error("Error fetching configs:", error);
        toast({
          title: "Error",
          description: error.message || "Something went wrong",
          variant: "destructive",
          duration: 2000,
          style: {
            backgroundColor: "#dc3545",
            color: "#f8f9fa",
            borderRadius: "12px",
          },
        });
      } finally {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchTestFlowData();
  }, [selectedTestFlow]);

  const handleCellClick = (rowIndex: number, field: string) => {
    setEditingCell({ row: rowIndex, field });
  };

  const handleBlur = (
    e: React.FocusEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    setEditingCell({ row: null, field: null });
  };

  const handleCellChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
    rowIndex: number,
    field: keyof Config
  ) => {
    const updatedConfigs = [...configs];
    updatedConfigs[rowIndex] = {
      ...updatedConfigs[rowIndex],
      [field]: e.target.value,
    };
    setIsSaving(true);
    setConfigs(updatedConfigs);
  };

  function getNewConfig(): Config {
    return {
      Environment: "",
      Execute: "No",
      Module: "",
      SamplerProxyName: "",
      CollectionName: "",
      DBExportVariables: "",
      Dependent: "",
      ExportVariable: "",
      HeaderParameter: "",
      Query: "",
      QueryParameter: "",
      ResponseValidation: "",
      SamplerProxyBody: "",
      SamplerProxyMethod: "",
      SamplerProxyPath: "",
      SamplerProxyResponseCode: "",
    };
  }
  const handleAddRow = () => {
    const newConfig = getNewConfig();
    setConfigs([...configs, newConfig]);
    setSelectedConfigs([...selectedConfigs, false]);
  };

  const toggleElement = (index: number) => {
    const updatedConfigs = [...configs];
    updatedConfigs[index].Execute =
      updatedConfigs[index].Execute === "Yes" ? "No" : "Yes";
    setConfigs(updatedConfigs);
    setIsSaving(true);
  };

  const handleDeleteSelected = () => {
    const elementsToKeep = configs.filter(
      (_, index) => !selectedConfigs[index]
    );
    setConfigs(elementsToKeep);
    setSelectedConfigs(Array(elementsToKeep.length).fill(false));
  };

  const handleSaveClick = async () => {
    const mergedData = setDataForUpdate(configs);
    // return
    const data = {
      testFlowName: selectedTestFlow.selectedTestFlow,
      testCases: mergedData,
    };

    try {
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/testflowui/addtestflow`,
        data,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );


      if (response.data.status === "success") {
        toast({
          title: "Success",
          description: response.data.message,
          duration: 2000,
          style: {
            backgroundColor: "oklch(59.6% 0.145 163.225)",
            color: "#f8f9fa",
            borderRadius: "12px",
          },
        });
        setIsSaving(false);
      } else {
        toast({
          title: "Error",
          description: response.data.error || "Something went wrong",
          variant: "destructive",
          duration: 2000,
          style: {
            backgroundColor: "#dc3545",
            color: "#f8f9fa",
            borderRadius: "12px",
          },
        });
      }
    } catch (error: any) {
      console.error("Error fetching configs:", error);
      toast({
        title: "Error",
        description: error.message || "Something went wrong",
        variant: "destructive",
        duration: 2000,
        style: {
          backgroundColor: "#dc3545",
          color: "#f8f9fa",
          borderRadius: "12px",
        },
      });
    }
  };

  const setDataForUpdate = (filteredData) => {
    const mergedData = filteredData.map((filter) => {
      const original = testFlowData.find(
        (o) =>
          o.Module === filter.Module &&
          o.SamplerProxyName === filter.SamplerProxyName
      );

      return {
        ...original, // keep all original fields
        Execute: filter.Execute, // overwrite Execute
        Environment: filter.Environment, // add/overwrite Environment
      };
    });

    return mergedData;
  };

  const handleDropdownToggle = (index: number) => {
    if (dropdownTriggerRefs.current[index]) {
      const rect = dropdownTriggerRefs.current[index]!.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom,
        left: rect.left,
        width: rect.width,
      });
    }
    const newEnvDropdownOpen = { [index]: !envDropdownOpen[index] };
    setEnvDropdownOpen(newEnvDropdownOpen);
  };

  const onDragEnd = (result: DropResult) => {
    if (!result.destination) {
      return;
    }
    const reorderedConfigs = Array.from(configs);
    const [removed] = reorderedConfigs.splice(result.source.index, 1);
    reorderedConfigs.splice(result.destination.index, 0, removed);
    setConfigs(reorderedConfigs);
    setIsSaving(true);
  };

  return (
    <div className="flex flex-col h-full">
      <header className="bg-background flex items-center justify-between px-[20px] pb-[16px]">
        <div className="w-[85%]">
          <input
            type="text"
            placeholder="Search by Test Case name"
            value={searchConfig}
            onChange={(e) => setSearchConfig(e.target.value)}
            onClick={() => setSearchConfig("")}
            className="border px-3 py-2 rounded w-full"
          />
        </div>
        <div className="flex items-center gap-[20px]">
          {/* <a
            onClick={(e) => {
              e.stopPropagation();
              handleAddRow();
            }}
            title="Add new config"
          >
            <Plus className="w-7 h-8 cursor-pointer text-[#1d5881]" />
          </a> */}
          <a
            onClick={(e) => {
              e.stopPropagation();
              handleSaveClick();
            }}
            className={`${
              isSaving
                ? "pointer-events-auto cursor-pointer"
                : "pointer-events-none"
            }`}
            title="Save Test Flow"
          >
            <img
              src="/save1.svg"
              className="w-7 h-8 cursor-pointer"
              style={{ opacity: isSaving ? 1 : 0.5 }}
            />
          </a>
          <a
            onClick={(e) => {
              e.stopPropagation();
              handleDeleteSelected();
            }}
            title="Delete config"
          >
            <img
              src="/Group 21846.svg"
              className="w-6 h-6 cursor-pointer hover:text-[#7b7c7a] cursor-pointer"
            />
          </a>
        </div>
      </header>
      <div
        style={{ height: "calc(100vh - 80px)" }}
        className="overflow-y-auto"
      >
        {loading && (
          <div className="flex h-screen items-center justify-center">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#15537C]"></div>
              <span>Loading test configurations...</span>
            </div>
          </div>
        )}
        <div>
          {configs.length == 0 ? (
            <div className="p-8 text-center text-gray-500">
              <p>No configurations found for this test flow.</p>
            </div>
          ) : (
            <div className="px-5">
              <Table className="table-fixed" ref={tableRef}>
                <TableHeader className="bg-[#15537c]">
                  <TableRow>
                    <TableHead className="w-12"></TableHead>
                    <TableHead className="w-[1%]"></TableHead>
                    <TableHead className="text-[#ffffff] w-[25%]">
                      Execute
                    </TableHead>
                    <TableHead className="text-[#ffffff] w-[25%]">
                      Module
                    </TableHead>
                    <TableHead className="text-[#ffffff] w-[25%]">
                      Test Case
                    </TableHead>
                    <TableHead className="text-[#ffffff] w-[25%]">
                      Environment
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <DragDropContext onDragEnd={onDragEnd}>
                  <Droppable droppableId="configs">
                    {(provided) => (
                      <TableBody
                        {...provided.droppableProps}
                        ref={provided.innerRef}
                      >
                        {configs
                          .filter((config) =>
                            searchConfig
                              ? config.SamplerProxyName.toLowerCase().includes(
                                  searchConfig.toLowerCase()
                                )
                              : true
                          )
                          .map((config: Config, index: number) => (
                            <Draggable
                              key={index}
                              draggableId={index.toString()}
                              index={index}
                            >
                              {(provided, snapshot) => (
                                <TableRow
                                  ref={provided.innerRef}
                                  {...provided.draggableProps}
                                  style={{
                                    ...provided.draggableProps.style,
                                    width: snapshot.isDragging && tableRef.current ? tableRef.current.clientWidth : 'auto',
                                  }}
                                  className={
                                    index % 2 === 0
                                      ? "bg-[#F7F9FC] "
                                      : "bg-[#FFFFFF4D]"
                                  }
                                >
                                  <TableCell
                                    {...provided.dragHandleProps}
                                    className="w-12"
                                    style={snapshot.isDragging ? { width: columnWidths[0] } : {}}
                                  >
                                    <GripVertical className="text-gray-400" />
                                  </TableCell>
                                  <TableCell
                                    className="w-[5%]"
                                    style={snapshot.isDragging ? { width: columnWidths[1] } : {}}
                                  >
                                    <input
                                      type="checkbox"
                                      checked={
                                        config.Execute == "Yes" ? true : false
                                      }
                                      onChange={() => toggleElement(index)}
                                      className="accent-blue-500"
                                    />
                                  </TableCell>
                                  {columns.map((col, i) => (
                                    <TableCell
                                      key={i}
                                      className="h-13"
                                      style={snapshot.isDragging ? { width: columnWidths[i + 2] } : {}}
                                    >
                                      {i === 0 ? null : col === "Environment" ? (
                                        <div
                                          ref={(el) =>
                                            (dropdownTriggerRefs.current[
                                              index
                                            ] = el)
                                          }
                                          className="relative w-full"
                                        >
                                          <div
                                            onClick={() =>
                                              handleDropdownToggle(index)
                                            }
                                            className="w-full h-full flex items-center justify-between cursor-pointer"
                                          >
                                            <span>
                                              {config.Environment ||
                                                "Select Env"}
                                            </span>
                                            <ChevronDown className="h-4 w-4 text-gray-400" />
                                          </div>
                                          {envDropdownOpen[index] &&
                                            dropdownPosition &&
                                            createPortal(
                                              <div
                                                ref={(el) =>
                                                  (dropdownRefs.current[
                                                    index
                                                  ] = el)
                                                }
                                                style={{
                                                  position: "fixed",
                                                  top: dropdownPosition.top,
                                                  left: dropdownPosition.left,
                                                  width: dropdownPosition.width,
                                                }}
                                                className="z-10 bg-white border rounded-md mt-1 shadow-lg"
                                              >
                                                {environments.map((env) => (
                                                  <div
                                                    key={env}
                                                    className="p-2 text-sm hover:bg-gray-100 cursor-pointer"
                                                    onMouseDown={() => {
                                                      handleCellChange(
                                                        {
                                                          target: {
                                                            value: env,
                                                          },
                                                        } as React.ChangeEvent<HTMLSelectElement>,
                                                        index,
                                                        "Environment"
                                                      );
                                                      setEnvDropdownOpen({
                                                        ...envDropdownOpen,
                                                        [index]: false,
                                                      });
                                                    }}
                                                  >
                                                    {env}
                                                  </div>
                                                ))}
                                              </div>,
                                              document.body
                                            )}
                                        </div>
                                      ) : editingCell.row === index &&
                                        editingCell.field === col ? (
                                        <input
                                          value={config[col]}
                                          onChange={(e) =>
                                            handleCellChange(e, index, col)
                                          }
                                          onBlur={handleBlur}
                                          autoFocus
                                          className="w-full bg-white border border-gray-300 rounded px-2 py-2"
                                          placeholder={placeholders[i]}
                                        />
                                      ) : (
                                        <div
                                          onClick={() =>
                                            allowEditing &&
                                            col !== "Module" &&
                                            col !== "SamplerProxyName"
                                              ? handleCellClick(index, col)
                                              : undefined
                                          }
                                          className={`w-full h-full flex items-center ${
                                            allowEditing &&
                                            col !== "Module" &&
                                            col !== "SamplerProxyName"
                                              ? "cursor-pointer"
                                              : "cursor-default"
                                          }`}
                                        >
                                          {config[col]}
                                        </div>
                                      )}
                                    </TableCell>
                                  ))}
                                </TableRow>
                              )}
                            </Draggable>
                          ))}
                        {provided.placeholder}
                      </TableBody>
                    )}
                  </Droppable>
                </DragDropContext>
              </Table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

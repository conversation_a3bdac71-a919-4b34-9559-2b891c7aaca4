"use client";

import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { Plus } from "lucide-react";

type Config2 = {
  Environment: string;
  URI: string;
  DBtype: string;
  DBhost: string;
  DBport: string;
  DBuser: string;
  DBpwd: string;
  DBname: string;
  BasicAuth: string;
  Username: string;
  Password: string;
};

type Config = {
CollectionName: string;
DBExportVariables: string;
Dependent: string;
Environment: string;
Execute: string;
ExportVariable: string;
HeaderParameter: string;
Module: string;
Query: string;
QueryParameter: string;
ResponseValidation: string;
SamplerProxyBody: string;
SamplerProxyMethod: string;
SamplerProxyName: string;
SamplerProxyPath: string;
SamplerProxyResponseCode: string;
}

type UpdatedConfig = {
  rowIndex: number;
  Environment: string;
  URI: string;
  DBtype: string;
  DBhost: string;
  DBport: string;
  DBuser: string;
  DBpwd: string;
  DBname: string;
};

type deleteConfig = {
  rowIndex: number;
};



export default function TestFlowUIList(selectedTestFlow: any) {
  const [configs, setConfigs] = useState<Config[]>([]);
  const [searchConfig, setSearchConfig] = useState<string>("");
  // const [config,setConfig] = useState<Config>({} as Config); //why to write as Config

  const [loading, setLoading] = useState(false);
  const [allowEditing, setAllowEditing] = useState(true);
  // can cause error here when config array is empty
  const columns: (keyof Config)[] =
    configs.length > 0 ? (Object.keys(configs[0]) as (keyof Config)[]) : [];
  const placeholders: string[] = [
    "",
    "",
    "",
    "Enter Environment",
    // "Enter DB port",
    // "Enter DB user",
    // "Enter DB pwd",
    // "Enter DB name",
    // "Enter Basic Auth",
    // "Enter Username",
    // "Enter Password",
  ];
  const [selectedConfigs, setSelectedConfigs] = useState<boolean[]>(
    Array(configs.length).fill(false)
  );

  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  const [editingCell, setEditingCell] = useState<{
    row: number | null;
    field: string | null;
  }>({ row: null, field: null });

  //fetch configs
  const fetchConfigs = async () => {
    setLoading(true);
    if(selectedTestFlow){
    try {
      // console.log('0 - ', selectedTestFlow)
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/testFlowUI/${selectedTestFlow.selectedTestFlow}`
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch configs: ${response.status}`);
      }

      const userData = await response.json();
      let allData = userData.data;

      const requiredKeys = ["Execute", "Module", "SamplerProxyName", "Environment"];

      const filteredColumns = userData.data.map(obj =>
        Object.fromEntries(requiredKeys.map(key => [key, obj[key]]))
      );
      setConfigs(filteredColumns);
    } catch (error: any) {
      console.error("Error fetching configs:", error);
       toast({
          title: "Error",
          description: error.message || "Something went wrong",
          variant: "destructive",
          duration: 2000,
           style: {
            backgroundColor: "#dc3545",
            color: "#f8f9fa",
            borderRadius: "12px",
          },
        });
      // setUsersFetchError(error.message || "Failed to load users");
    } finally {
      // setIsLoadingUsers(false);
      setLoading(false);
    }
    }

  };

  useEffect(() => {
    fetchConfigs();
  }, [selectedTestFlow]);

  const handleCellClick = (rowIndex: number, field: string) => {
    console.log(rowIndex, field);
    setEditingCell({ row: rowIndex, field });
  };

  const handleBlur = (
    e: React.ChangeEvent<HTMLInputElement>,
    field: keyof Config
  ) => {
    // console.log(e.target.value);
    const currentCellValue = e.target.value.trim();
    // if (field == "Environment") {
    //   const exists = configs
    //     .slice(0, -1)
    //     .some(
    //       (item) =>
    //         item.Environment.toLowerCase() === currentCellValue.toLowerCase()
    //     );
    //   console.log(exists);
    //   if (exists) {
    //     toast({
    //       title: "Validation Error",
    //       description: "Environment Name should be unique",
    //       duration: 2000,
    //       style: {
    //         backgroundColor: "oklch(82.8% 0.189 84.429)",
    //         color: "oklch(21.6% 0.006 56.043)",
    //         borderRadius: "12px",
    //       },
    //     });
    //   }
    // }
    // if (field == "URI") {
    //   const exists = configs
    //     .slice(0, -1)
    //     .some(
    //       (item) => item.URI.toLowerCase() === currentCellValue.toLowerCase()
    //     );
    //   if (exists) {
    //     toast({
    //       title: "Validation Error",
    //       description: "URI should be unique",
    //       duration: 2000,
    //       style: {
    //        backgroundColor: "oklch(82.8% 0.189 84.429)",
    //         color: "oklch(21.6% 0.006 56.043)",
    //         borderRadius: "12px",
    //       },
    //     });
    //   }
    // }

    setEditingCell({ row: null, field: null });
  };

  const handleCellChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    rowIndex: number,
    field: keyof Config
  ) => {
    const updatedConfigs = [...configs];
    updatedConfigs[rowIndex] = {
      ...updatedConfigs[rowIndex],
      [field]: e.target.value,
    };
    setIsSaving(true);
    setConfigs(updatedConfigs);
  };

  // Create new config template
  function getNewConfig(): Config {
    return {
      Environment: "",
      URI: "",
      DBtype: "",
      DBhost: "",
      DBport: "",
      DBuser: "",
      DBpwd: "",
      DBname: "",
      BasicAuth: "",
      Username: "",
      Password: "",
    };
  }
  //Add row logic
  const handleAddRow = () => {
    const newConfig = getNewConfig();
    setConfigs([...configs, newConfig]);
    setSelectedConfigs([...selectedConfigs, false]);
  };

  //toggle logic
  const toggleElement = (index: number) => {
    const updatedConfigs = [...configs];
    updatedConfigs[index].Execute =
      updatedConfigs[index].Execute === "Yes" ? "No" : "Yes";
    setConfigs(updatedConfigs);
    setIsSaving(true);
  };

  const handleDeleteSelected = () => {
    const elementsToKeep = configs.filter(
      (_, index) => !selectedConfigs[index]
    );
    console.log(elementsToKeep);

    // Update local state - renumber the remaining elements
    const renumbered = elementsToKeep.map((element) => ({
      ...element,
    }));

    setConfigs(renumbered);
    setSelectedConfigs(Array(renumbered.length).fill(false));

  };

  //save logic
  const handleSaveClick = async () => {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/config/save`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(configs),
      }
    );

    const result = await res.json();
    console.log(result);
    if (result.success) {
        toast({
          title: "Success",
          description: result.message,
          duration: 2000,
          style: {
            backgroundColor: "oklch(59.6% 0.145 163.225)",
            color: "#f8f9fa",
            borderRadius: "12px",
          },
        });
        
      setIsSaving(false);
      } else {
        toast({
          title: "Error",
          description: result.error || "Something went wrong",
          variant: "destructive",
          duration: 2000,
           style: {
            backgroundColor: "#dc3545",
            color: "#f8f9fa",
            borderRadius: "12px",
          },
        });
      }
  };

  return (
    <>
      <header className="bg-background sticky top-0 flex items-center justify-between p-[20px]">
        <div className="w-[85%]">
           <input
            type="text"
            placeholder="Search by Test Case name"
            value={searchConfig}
            onChange={(e) => setSearchConfig(e.target.value)}
            onClick={() => setSearchConfig("")}
            className="border px-3 py-2 rounded w-full"
          />
        </div>
        <div className="flex items-center gap-[20px]">
          {/* Add Element Button */}
          <a
            onClick={(e) => {
              e.stopPropagation();
              handleAddRow();
            }}
            title="Add new config"
          >
            <Plus className="w-7 h-8 cursor-pointer text-[#1d5881]" />
          </a>
          {/* save elements */}
          <a
            onClick={(e) => {
              e.stopPropagation();
              handleSaveClick();
            }}
            className={`${
              isSaving
                ? "pointer-events-auto cursor-pointer"
                : "pointer-events-none"
            }`}
            title="Save new configs"
          >
            <img
              src="/save1.svg"
              className="w-7 h-8 cursor-pointer"
              style={{ opacity: isSaving ? 1 : 0.5 }}
            />
          </a>
          <a
            onClick={(e) => {
              e.stopPropagation();
              handleDeleteSelected();
            }}
            title="Delete config"
          >
            <img
              src="/Group 21846.svg"
              className="w-6 h-6 cursor-pointer hover:text-[#7b7c7a] cursor-pointer"
            />
          </a>
        </div>
      </header>
      {loading && (
        <div className="flex h-screen items-center justify-center">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#15537C]"></div>
            <span>Loading test configurations...</span>
          </div>
        </div>
      )}
      <div>
        {configs.length == 0 ? (
          <div className="p-8 text-center text-gray-500">
            <div className="text-gray-400 mb-4">
              <svg
                className="w-16 h-16 mx-auto mb-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                />
              </svg>

              <p className="text-lg font-medium text-gray-600">
                No configurations found
              </p>
              <p className="text-sm text-gray-500">
                Start by identifying and creating configurations for your
                project
              </p>
            </div>
          </div>
        ) : (
          <div className="px-5">
            <Table className="table-fixed">
              <TableHeader className="bg-[#15537c]">
                <TableRow>
                  <TableHead className="w-[1%]"></TableHead>
                  <TableHead className="text-[#ffffff] w-[25%]">Execute</TableHead>
                  <TableHead className="text-[#ffffff] w-[25%]">Module</TableHead>
                  <TableHead className="text-[#ffffff] w-[25%]">Test Case</TableHead>
                  <TableHead className="text-[#ffffff] w-[25%]">Environment</TableHead>
                  {/* <TableHead className="text-[#ffffff] w-[7%]">Port</TableHead>
                  <TableHead className="text-[#ffffff] w-[8%]">Database User</TableHead>
                  <TableHead className="text-[#ffffff] w-[10%]">Database Password</TableHead>
                  <TableHead className="text-[#ffffff] w-[9%]">Database Name</TableHead>
                  <TableHead className="text-[#ffffff] w-[8%]">Basic Auth</TableHead>
                  <TableHead className="text-[#ffffff] w-[7%]">Username</TableHead>
                  <TableHead className="text-[#ffffff] w-[9%]">Password</TableHead> */}
                </TableRow>
              </TableHeader>
              <TableBody>
                {/* show all configs/ only show which match search */}
                {configs.map((config: Config, index: number) => {
                    if(searchConfig && config.Environment.toLowerCase().includes(searchConfig.toLowerCase())){
                        return(
                          <TableRow
                            key={index}
                            className={
                              index % 2 === 0 ? "bg-[#F7F9FC] " : "bg-[#FFFFFF4D]"
                            }
                          >
                            {/* static checkbox cell */}
                            <TableCell className="w-[5%]">
                              <input
                                type="checkbox"
                                checked={config.Execute == "Yes"? true : false}
                                onChange={() => toggleElement(index)}
                                className="accent-blue-500"
                              />
                            </TableCell>
                            {columns.map((col, i) => (
                              
                              <TableCell key={i} className="h-13">
                                {i==0?(""):
                                allowEditing &&
                                editingCell.row === index &&
                                editingCell.field === col ? (
                                  <input
                                    value={config[col]}
                                    onChange={(e) => handleCellChange(e, index, col)}
                                    onBlur={(e) => handleBlur(e, col)}
                                    autoFocus
                                    className="w-35 h-5 bg-white border border-gray-300 rounded px-2 py-2"
                                    placeholder={placeholders[i]}
                                  />
                                ) : (
                                  <input
                                    type={
                                      (col === "DBpwd" || col === "Password") 
                                        ? "password" 
                                        : "text"
                                    }
                                                                        onClick={
                                      allowEditing && col !== "Module" && col !== "SamplerProxyName"
                                        ? () => handleCellClick(index, col)
                                        : undefined
                                    }
                                    className={`flex-1 bg-transparent border-none outline-none ${
                                      allowEditing && col !== "Module" && col !== "SamplerProxyName"
                                        ? "cursor-pointer"
                                        : "cursor-default"
                                    }`}
                                    value={config[col]}
                                    placeholder={placeholders[i]}
                                    readOnly
                                  />
                                )}
                              </TableCell>
                            ))}
                          </TableRow>
                        );
                    }
                    if(searchConfig == ''){
                        return(
                          <TableRow
                            key={index}
                            className={
                              index % 2 === 0 ? "bg-[#F7F9FC] " : "bg-[#FFFFFF4D]"
                            }
                          >
                            {/* static checkbox cell */}
                            <TableCell className="w-[5%]">
                              <input
                                type="checkbox"
                                checked={config.Execute == "Yes"? true : false}
                                onChange={() => toggleElement(index)}
                                className="accent-blue-500"
                              />
                            </TableCell>
                            {columns.map((col, i) => (
                              <TableCell key={i} className="h-13">
                                 {i==0?(""):
                                allowEditing &&
                                editingCell.row === index &&
                                editingCell.field === col ? (
                                  <input
                                    value={config[col]}
                                    onChange={(e) => handleCellChange(e, index, col)}
                                    onBlur={(e) => handleBlur(e, col)}
                                    autoFocus
                                    className="w-35 h-5 bg-white border border-gray-300 rounded px-2 py-2"
                                    placeholder={placeholders[i]}
                                  />
                                ) : (
                                  <input
                                    type={
                                      (col === "DBpwd" || col === "Password") 
                                        ? "password" 
                                        : "text"
                                    }
                                                                        onClick={
                                      allowEditing && col !== "Module" && col !== "SamplerProxyName"
                                        ? () => handleCellClick(index, col)
                                        : undefined
                                    }
                                    className={`flex-1 bg-transparent border-none outline-none ${
                                      allowEditing && col !== "Module" && col !== "SamplerProxyName"
                                        ? "cursor-pointer"
                                        : "cursor-default"
                                    }`}
                                    value={config[col]}
                                    placeholder={placeholders[i]}
                                    readOnly
                                  />
                                )}
                              </TableCell>
                            ))}
                          </TableRow>
                        );
                    }
                })}
              </TableBody>
            </Table>
          </div>
        )}
      </div>
    </>
  );
}




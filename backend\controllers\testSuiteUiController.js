const path = require("path");
const fs = require("fs");
const xlsx = require("xlsx");
const testFlowExcelService = require('../services/testFlowExcelService');

// Start from the current controller directory
// // path.join() will automatically adds the correct path separator (\ on Windows, / on Linux/Mac)
// const excelDir = path.join(
//   __dirname,                        // current folder: controllers
//   "..", "..",                       // go 2 levels up (to ANTP_APIs_25-07-1215)
//   "..",                             // go 1 more level up (to ANTP root)
//   "ANTPTool", "antp-platform", "antp-api",
//   "src", "main", "resources", "API", "data"
// );

const excelDir = __dirname + '/../create-api';


// GET All API Modules(files)
exports.getTestSuites = async (_req, res) => {
  try {
    const files = await fs.promises.readdir(excelDir);

    const excelFiles = files
    .filter(file =>
      file.startsWith("TS_") && (file.endsWith(".xlsx") || file.endsWith(".xls"))
    )
    .map(file => {
      // remove prefix "TC_" and extension
      return file.replace(/^TS_/, "").replace(/\.(xlsx|xls)$/, "");
    });

    res.json({ files: excelFiles });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// GET API Module by name(file data)
exports.getTestSuiteByName = async (req, res) => {
  try {
    const file = 'TS_' + req.params.name + '.xlsx';
    console.log(file);
    const filePath = path.join(excelDir, file);

    // Read excelFile
    const excelFile = xlsx.readFile(filePath);

    // Get first sheet
    const sheetName = excelFile.SheetNames[0];
    const worksheet = excelFile.Sheets[sheetName];

    // Convert to JSON
    const data = xlsx.utils.sheet_to_json(worksheet);

    res.json({ file: file, data: data });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// GET TestCase Names only by module name
exports.getTCNamesByModule = async (req, res) => {
  try {
    const file = 'TC_' + req.query.name + '.xlsx';
    const filePath = path.join(excelDir, file);

    // Read excelFile
    const excelFile = xlsx.readFile(filePath);

    // Get first sheet
    const sheetName = excelFile.SheetNames[1];
    const worksheet = excelFile.Sheets[sheetName];

    // Convert to JSON
    const data = xlsx.utils.sheet_to_json(worksheet);

    const testCaseNames = data.map(item => item.SamplerProxyName)

    res.json({ file: file, testcases: testCaseNames });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};



exports.addDataController = async (req, res) => {
  try {
    const requestData = (req.body);
    const testFlowName = requestData.testFlowName
    const rowData = requestData.testCases

    if (
      testFlowName.trim() == ""
    ) {
      return res.status(400).json({
        status: "error",
        message:
          'Bad Request: Test Suite Name must be provided.',
      });
    }


    if (!Array.isArray(rowData) ||  rowData.length == 0) {
      return res.status(400).json({
        status: "error",
        message:
          'Bad Request: No data provided in the array.',
      });
    }

    const savedTestFlow =await testFlowExcelService.updateOrCreateExcel(testFlowName, rowData);

    res.status(200).json({
      status: "success",
      message: `Data has been added successfully to ${path.basename(
        savedTestFlow
      )}.xlsx!`,
    });
  } catch (error) {
    console.error("Error in addDataController:", error);
    res.status(500).json({
      status: "error",
      message: "Internal server error occurred while adding data.",
      error: error.message
    });
  }
};





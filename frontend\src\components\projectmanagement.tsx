"use client";
import { Testcases } from "@/components/testcases";
import { Elements } from "./elements";
import { ElementExtractorList } from "@/components/ElementExtractorList";
import { Testapis } from "./testapis";

interface ProjectManagementContentProps {
  name: string;
  section: string;
  searchTerm?: string; // searchTerm prop
  onModuleChange?: (moduleName?: any) => void;
}

export function ProjectManagementContent({
  section,
  name,
  searchTerm = "", // searchTerm with default value
  onModuleChange
}: ProjectManagementContentProps) {
  if (section === "Testcases") {
    return (
      <div>
        <Testcases projectname={name} searchTerm={searchTerm} /> {/*  Pass searchTerm */}
      </div>
    );
  }
  
  if (section === "Elements") {
    return (
      <div>
        <Elements projectname={name} />
      </div>
    );
  }

  // Add ElementExtractor section - exactly like test cases
  if (section === "ElementExtractor") {
    return (
      <div>
        <ElementExtractorList />
      </div>
    );
  }

  if (section === "APIs") {
    return (
      <div>
        <Testapis modulename={name} onModuleChange={onModuleChange} />
      </div>
    );
  }
  
  // Default message with styled modal
  return (
    <div className="p-8 text-center text-gray-500">
      <div className="text-gray-400 mb-4">
        <svg
          className="w-16 h-16 mx-auto mb-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
          />
        </svg>
        <p className="text-lg font-medium text-gray-600">
          Select a section to view content
        </p>
        <p className="text-sm text-gray-500">
          Choose a section from the sidebar to manage your project components
        </p>
      </div>
    </div>
  );
}

const ExcelJS = require("exceljs");
const fs = require("fs").promises;
const path = require("path");

const UPLOADS_DIR = __dirname + '/../create-api';


async function fileExists(filePath) {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

function getOrCreateWorksheet(workbook, worksheetName = "Sheet 1") {
  let worksheet = workbook.getWorksheet(worksheetName) || workbook.getWorksheet(1);
  if (!worksheet) {
    worksheet = workbook.addWorksheet(worksheetName);
  }
  return worksheet;
}



async function updateOrCreateExcel(fileName, rowData) {
  const safeFileName = path.basename("TF_" + fileName);
  const filePath = path.join(UPLOADS_DIR, `${safeFileName}.xlsx`);

  await fs.mkdir(UPLOADS_DIR, { recursive: true });

  const workbook = new ExcelJS.Workbook();

  let worksheet = workbook.getWorksheet("Sheet 1");

if (worksheet) {
  // Clear all existing rows (keep the sheet)
  worksheet.spliceRows(1, worksheet.rowCount); // removes all rows including header
} else {
  // Sheet doesn't exist → create new
  worksheet = workbook.addWorksheet("Sheet 1");
}
  
  // Set headers from first object keys
  const headers = Object.keys(rowData[0]).map((key) => ({
    header: key,
    key: key,
  }));
  worksheet.columns = headers;

  // Add rows in given order
  rowData.forEach((item) => {
    worksheet.addRow(item);
  });

  // (Optional) Auto-fit column widths
  worksheet.columns.forEach((col) => {
    let maxLength = 15;
    col.eachCell({ includeEmpty: true }, (cell) => {
      const cellLength = cell.value ? cell.value.toString().length : 0;
      if (cellLength > maxLength) {
        maxLength = cellLength;
      }
    });
    col.width = maxLength + 2;
  });

  // Save back
  try {
    await workbook.xlsx.writeFile(filePath);
    console.log("Workbook saved:", safeFileName);
    return fileName;
  } catch (err) {
    console.error("Error writing Excel file:", err.message);
    throw new Error(`Failed to write ${filePath}`);
  }
}


module.exports = {
  updateOrCreateExcel,
};
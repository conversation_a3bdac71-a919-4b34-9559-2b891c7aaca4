const ExcelJS = require("exceljs");
const fs = require("fs").promises;
const path = require("path");

const UPLOADS_DIR = __dirname + '/../create-api';


async function fileExists(filePath) {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

function getOrCreateWorksheet(workbook, worksheetName = "Sheet 1") {
  let worksheet = workbook.getWorksheet(worksheetName) || workbook.getWorksheet(1);
  if (!worksheet) {
    worksheet = workbook.addWorksheet(worksheetName);
  }
  return worksheet;
}


async function updateOrCreateExcel(fileName, rowData) {
  const safeFileName = path.basename("TF_" + fileName);
  const filePath = path.join(UPLOADS_DIR, `${safeFileName}.xlsx`);
  let modifiedData;

  await fs.mkdir(UPLOADS_DIR, { recursive: true });

  const workbook = new ExcelJS.Workbook();

  if (await fileExists(filePath)) {
    return
    const fileBuffer = await fs.readFile(filePath);
    await workbook.xlsx.load(fileBuffer);

    const worksheet = getOrCreateWorksheet(workbook);
    
    const autoId = getNextAutoId(worksheet);
    
    modifiedData = { id: autoId, ...data };

    console.log(`Worksheet found. Row count before adding: ${worksheet.rowCount}`);
    worksheet.insertRow(worksheet.rowCount + 1, Object.values(modifiedData));
    console.log("Row count after adding:", worksheet.rowCount);
    console.log(`File '${safeFileName}.xlsx' exists. Appending a new row with ID: ${autoId}`);
  } else {
    
    const headers = Object.keys(rowData[0]).map((key) => ({ header: key, key: key }));

    const worksheet = workbook.addWorksheet("Sheet 1");
    worksheet.columns = headers;

    

    
    // Add all rows
    rowData.forEach((item) => {
      worksheet.addRow({...item });
    });

  }

  try {
    await workbook.xlsx.writeFile(filePath);
    console.log("Successfully saved the workbook.");
    return safeFileName;
  } catch (err) {
    console.error("Error writing the Excel file:", err.message);
    throw new Error(
      `Failed to write to ${filePath}. Check file permissions or if the file is open elsewhere.`
    );
  }
}


module.exports = {
  updateOrCreateExcel,
};
{"browsers": [{"name": "Chrome", "icon": "🌐", "value": "chromium"}, {"name": "Firefox", "icon": "🦊", "value": "firefox"}, {"name": "Safari", "icon": "🧭", "value": "safari"}, {"name": "Edge", "icon": "📘", "value": "edge"}], "recordingsPath": "D:/TargetCode/TestWeb/antp-web/src/test/resources/features", "baseUrl": "D:/TargetCode", "projectName": "TestWeb", "currentProjectPath": "D:/TargetCode/TestWeb", "testflowpath": "D:/TargetCode/TestWeb/antp-web/src/testflows.json", "testsuitepath": "D:/TargetCode/TestWeb/antp-web/src/testsuites.json", "projectPath": "D:/TargetCode/TestWeb/antp-web", "elementsPath": "D:/TargetCode/TestWeb/antp-web/src/main/resources/elementProperties", "elementextractorpath": "D:/TargetCode/TestWeb/webElementExtractor/src/test/resources/features", "projectConfigurations": "D:/TargetCode/TestWeb/antp-web/src/main/resources/config/url.properties"}
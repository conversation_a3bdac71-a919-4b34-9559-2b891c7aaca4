{"browsers": [{"name": "Chrome", "icon": "🌐", "value": "chromium"}, {"name": "Firefox", "icon": "🦊", "value": "firefox"}, {"name": "Safari", "icon": "🧭", "value": "safari"}, {"name": "Edge", "icon": "📘", "value": "edge"}], "recordingsPath": "D:/TargetCode/proj2/antp-web/src/test/resources/features", "baseUrl": "D:/TargetCode", "projectName": "proj2", "currentProjectPath": "D:/TargetCode/proj2", "testflowpath": "D:/TargetCode/proj2/antp-web/src/testflows.json", "testsuitepath": "D:/TargetCode/proj2/antp-web/src/testsuites.json", "projectPath": "D:/TargetCode/proj2/antp-web", "elementsPath": "D:/TargetCode/proj2/antp-web/src/main/resources/elementProperties", "elementextractorpath": "D:/TargetCode/proj2/webElementExtractor/src/test/resources/features", "projectConfigurations": "D:/TargetCode/proj2/antp-web/src/main/resources/config/url.properties"}
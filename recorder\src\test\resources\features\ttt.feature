Feature: ttt
  <PERSON>: User performs actions on https://www.abjayon.com
    Given I am on the home page
    Then I load the "elementProperties" module "Abjayon_Bring_IT_On" locators
    Then I should be navigated to "about:blank"
    Then I click on "firstname_id_xpath" on "https://www.abjayon.com/" page
    Then I send "firstname_id_xpath" as a "test" in to "https://www.abjayon.com/" page
    Then I click on "contactus_//div[contains(@_xpath" on "https://www.abjayon.com/" page
    Then I click on "iagreetodataprivacypolicyofabjayon_id_xpath" on "https://www.abjayon.com/contact-us/" page
    Then I send "iagreetodataprivacypolicyofabjayon_id_xpath" as a "1" in to "https://www.abjayon.com/contact-us/" page
    Then I click on "checktosubscribetoournewsletter_id_xpath" on "https://www.abjayon.com/contact-us/" page
    Then I send "checktosubscribetoournewsletter_id_xpath" as a "1" in to "https://www.abjayon.com/contact-us/" page
    Then I verify "iagreetodataprivacypolicyofabjayon__xpath" has data "Data Privacy Policy" on "https://www.abjayon.com/contact-us/" page

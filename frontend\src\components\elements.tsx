"use client";

import { useState, useEffect } from "react";
import { ElementsTable } from "@/components/elementstable";

// Define the UIElement interface
export interface UIElement {
  id: number;
  name: string;
  fullKey: string;
  type: string;
  value: string;
  screenshot: string;
  moduleName: string;
}

// Define the ElementGroup interface to match API response
export interface ElementGroup {
  id: number;
  name: string;
  elements: UIElement[];
}

interface ElementsProps {
  projectname: string;
}

// API function to fetch elements from the new endpoint
import axios from 'axios';

async function getElementsFromAPI(): Promise<ElementGroup[]> {
  try {
    const response = await axios.get<ElementGroup[]>('/api/elements');
    return response.data;
  } catch (error) {
    console.error('Error fetching elements:', error);
    return [];
  }
}

export function Elements({ projectname }: ElementsProps) {
  const [elementGroups, setElementGroups] = useState<ElementGroup[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const fetchElements = async () => {
    setIsLoading(true);
    const data = await getElementsFromAPI();
    setElementGroups(data);
    setIsLoading(false);
  };

  useEffect(() => {
    fetchElements();
  }, []);

  const refreshElements = () => {
    console.log("Refreshing elements from API...");
    fetchElements();
  };

  if (isLoading) {
    return <div className="p-4">Loading elements...</div>;
  }

  if (elementGroups.length === 0) {
    return <div className="p-4">No elements found</div>;
  }

  return (
    <div className="relative">
      <ElementsTable
        elementGroups={elementGroups}
        onRefresh={refreshElements}
        projectName={projectname}
      />
    </div>
  );
}

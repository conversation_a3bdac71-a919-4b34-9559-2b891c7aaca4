/* eslint-disable @typescript-eslint/no-explicit-any */
import { Draggable } from "@hello-pangea/dnd";
import { createPortal } from "react-dom";
import React, { useRef } from "react";

function DragPortal({ children }: { children: React.ReactNode }) {
  if (typeof document === "undefined") return null;
  let el = document.getElementById("dnd-portal");
  if (!el) {
    el = document.createElement("div");
    el.id = "dnd-portal";
    document.body.appendChild(el);
  }
  return createPortal(children, el);
}

export function PortalAwareDraggable({
  draggableId,
  index,
  children,
}: {
  draggableId: string;
  index: number;
  children: (provided: any, snapshot: any) => React.ReactNode;
}) {
  const ref = useRef<HTMLDivElement | null>(null);

  return (
    <Draggable draggableId={draggableId} index={index}>
      {(provided, snapshot) => {
        const child = (
          <div
            ref={(instance) => {
              ref.current = instance;
              provided.innerRef(instance);
            }}
            {...provided.draggableProps}
            {...provided.dragHandleProps}
            style={{
              ...provided.draggableProps.style,
              width: snapshot.isDragging
                ? ref.current?.getBoundingClientRect().width
                : "auto",
              boxSizing: "border-box",
              display: "flex",
              alignItems: "center",
            }}
          >
            {children(provided, snapshot)}
          </div>
        );
        if (snapshot.isDragging) {
          return <DragPortal>{child}</DragPortal>;
        }
        return child;
      }}
    </Draggable>
  );
}
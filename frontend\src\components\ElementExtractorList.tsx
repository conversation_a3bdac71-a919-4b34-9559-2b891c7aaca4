"use client";
import { useState, useEffect } from "react";
import axios from 'axios';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import Record from "@/components/Record";
import { playFeatureFile } from "@/app/api/recorder/record";


interface ElementExtractor {
  id: number;
  name: string;
  filePath: string;
  gherkinContent?: string;
  data?: any[];
}

export function ElementExtractorList({ refreshTrigger }: { refreshTrigger?: number }) {
  const [extractors, setExtractors] = useState<ElementExtractor[]>([]);
  const [loading, setLoading] = useState(true);
  const [playingExtractors, setPlayingExtractors] = useState<Set<number>>(new Set());
  const [savingExtractors, setSavingExtractors] = useState<Set<number>>(new Set());
  const [recordDialogOpen, setRecordDialogOpen] = useState(false);
  const [selectedExtractorName, setSelectedExtractorName] = useState("");
  const [selectedExtractorContent, setSelectedExtractorContent] = useState("");

  useEffect(() => {
    fetchExtractors();
  }, [refreshTrigger]);

  const fetchExtractors = async () => {


try {
  const response = await axios.get('/api/element-extractor');
  setExtractors(response.data);
} catch (error) {
  console.error('Failed to fetch element extractors:', error);
} finally {
  setLoading(false);
}

  };

  // SAVE Button - Same as test case save functionality
  const handleSave = async (extractorName: string, extractorId: number) => {
    setSavingExtractors(prev => new Set([...prev, extractorId]));


try {
  const response = await axios.post('/api/element-extractor/save', {
    fileName: extractorName,
    // Add any extractor-specific data to save here
  });

  if (response.status === 200) {
    console.log(`Successfully saved: ${extractorName}`);
  } else {
    console.error(`Failed to save: ${extractorName}`);
  }
} catch (error) {
  console.error('Save error:', error);
} finally {
  setSavingExtractors(prev => {
    const newSaving = new Set(prev);
    newSaving.delete(extractorId);
    return newSaving;
  });
}


  };

  // ✅ DEBUG VERSION: CUCUMBER Button - Fetch file content before opening modal  
  const handleCucumber = async (extractorName: string) => {
    console.log('🥒 Cucumber clicked for:', extractorName);


try {
  const apiUrl = '/api/element-extractor';
  console.log('🌐 Fetching from:', `${apiUrl}?type=feature&feature=${extractorName}`);

  const response = await axios.get(apiUrl, {
    params: {
      type: 'feature',
      feature: extractorName,
    },
  });

  console.log('📡 Response status:', response.status, response.status === 200);
  const data = response.data;
  console.log('📦 Response data:', data);

  setSelectedExtractorName(extractorName);

  if (data.content) {
    console.log('✅ Content received:', data.content.substring(0, 100) + '...');
    setSelectedExtractorContent(data.content);
  } else {
    console.log('⚠️ No content in response');
    setSelectedExtractorContent("No content found in response.");
  }

  console.log('🔓 Opening dialog...');
  setRecordDialogOpen(true);

} catch (error: any) {
  console.error("💥 Failed to fetch feature content:", error);
  setSelectedExtractorName(extractorName);
  setSelectedExtractorContent("Feature file not found or could not be loaded."); 
  setRecordDialogOpen(true);
}

  };

  // PLAY Button - Same as test case play functionality with playFeatureFile
  const handlePlay = async (extractorName: string, extractorId: number) => {
    if (playingExtractors.has(extractorId)) return;
    
    setPlayingExtractors(prev => new Set([...prev, extractorId]));
    
    try {
      // Use the same playFeatureFile function as test cases
      const featureFileName = `${extractorName}.feature`;
      console.log(`Playing element extractor file: ${featureFileName}`);
      
      // Import the same function used in test cases
   
      const result = await playFeatureFile(featureFileName, "chromium","webElementExtractor");
      console.log("Element extractor playback result:", result);
      
    } catch (error) {
      console.error("Element extractor playback failed:", error);
    } finally {
      setPlayingExtractors(prev => {
        const newPlaying = new Set(prev);
        newPlaying.delete(extractorId);
        return newPlaying;
      });
    }
  };

  // DELETE Button - Same as test case delete functionality
  const handleDelete = async (extractorName: string, extractorId: number) => {
    if (window.confirm(`Are you sure you want to delete "${extractorName}"?`)) {


try {
  const response = await axios.delete('/api/element-extractor', {
    data: {
      fileName: extractorName,
    },
  });

  if (response.status === 200) {
    console.log(`Successfully deleted: ${extractorName}`);
    // Refresh the list after deletion
    fetchExtractors();
  } else {
    console.error(`Failed to delete: ${extractorName}`, response.data.error);
    alert(`Failed to delete: ${response.data.error || "Unknown error"}`);
  }
} catch (error: any) {
  console.error("Delete error:", error);
  alert("Network error: Failed to delete extractor");
}

    }
  };

  if (loading) {
    return <div className="p-4">Loading element extractors...</div>;
  }

  if (extractors.length === 0) {
    return (
      <div className="p-8 text-center text-gray-500">
        <div className="text-gray-400 mb-4">
          <svg
            className="w-16 h-16 mx-auto mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
            />
          </svg>
          <p className="text-lg font-medium text-gray-600">
            No element extractors found
          </p>
          <p className="text-sm text-gray-500">
            Create your first Element Extractor to get started
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Accordion type="multiple" className="w-full h-full space-y-0.5">
        {extractors.map((extractor) => {
          const isPlaying = playingExtractors.has(extractor.id);
          const isSaving = savingExtractors.has(extractor.id);

          return (
            <AccordionItem key={extractor.id} value={`extractor-${extractor.id}`}>
              <AccordionTrigger 
                className="hover:no-underline p-0 [&>svg]:hidden"
                onClick={(e) => e.preventDefault()} // Disable accordion toggle
              >
                <div className="flex items-center h-[45px] px-4 w-full">
                  <div className="flex items-center flex-1">
                    <span className="text-left text-base font-medium">
                      {extractor.name}
                    </span>
                  </div>
                  
                  {/* Action buttons with real functionalities */}
                  <div className="flex items-center gap-3 pr-2">
                    {/* SAVE Button - Real save functionality */}
                    <a
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSave(extractor.name, extractor.id);
                      }}
                      className="cursor-pointer"
                      title="Save element extractor"
                    >
                      <img
                        src="/file.svg"
                        className="w-[16px] h-[14px] cursor-pointer"
                        style={{ opacity: isSaving ? 0.5 : 1 }}
                      />
                    </a>

                    {/* CUCUMBER Button - ✅ DEBUG VERSION: Fetches and displays feature content */}
                    <a
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCucumber(extractor.name);
                      }}
                      className="cursor-pointer"
                      title="Launch Cucumber recorder"
                    >
                      <img
                        src="/Cucumber.svg"
                        className="w-[16px] h-[19px] cursor-pointer"
                      />
                    </a>

                    {/* PLAY Button - Runs the extractor with play-once functionality */}
                    <a
                      onClick={(e) => {
                        e.stopPropagation();
                        if (isPlaying) return;
                        handlePlay(extractor.name, extractor.id);
                      }}
                      className={`${isPlaying ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                      title={isPlaying ? "Element extractor is running..." : "Run element extractor"}
                    >
                      <img
                        src="/play.svg"
                        className="w-[16px] h-[19px]"
                        style={{ 
                          opacity: isPlaying ? 0.3 : 1,
                          transition: 'opacity 0.3s ease'
                        }}
                      />
                    </a>

                    {/* DELETE Button - Deletes the extractor with confirmation */}
                    <a
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete(extractor.name, extractor.id);
                      }}
                      className="cursor-pointer"
                      title="Delete element extractor"
                    >
                      <img
                        src="/Group 21846.svg"
                        className="w-[16px] h-[14px] cursor-pointer"
                      />
                    </a>
                  </div>
                </div>
              </AccordionTrigger>

              <AccordionContent className="p-4">
                <div className="text-sm text-gray-600 space-y-2">
                  <div className="border-b pb-2 mb-3">
                    <p><strong>Name:</strong> {extractor.name}</p>
                    <p><strong>Type:</strong> Element Extractor</p>
                    <p><strong>File:</strong> {extractor.name}.feature</p>
                  </div>
                  
                  {/* Display parsed steps */}
                  {extractor.data && extractor.data.length > 0 ? (
                    <div>
                      <p className="font-medium text-gray-700 mb-2">Steps ({extractor.data.length}):</p>
                      <div className="space-y-1 max-h-48 overflow-y-auto">
                        {extractor.data.map((step, index) => (
                          <div key={index} className="bg-gray-50 p-2 rounded text-xs border-l-2 border-blue-400">
                            <div className="flex items-start gap-2">
                              <span className="font-medium text-blue-600 min-w-fit">
                                {step.action}:
                              </span>
                              <span className="flex-1">{step.description}</span>
                            </div>
                            {step.url && (
                              <div className="mt-1 text-gray-500 text-xs ml-2">
                                → URL: {step.url}
                              </div>
                            )}
                            {step.fileName && (
                              <div className="mt-1 text-gray-500 text-xs ml-2">
                                → File: {step.fileName}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-gray-500 italic">No steps parsed from this extractor</p>
                      <p className="text-xs text-gray-400 mt-1">
                        The feature file may be empty or contain only comments/tags
                      </p>
                    </div>
                  )}

                  {/* Show raw content preview if available */}
                  {extractor.gherkinContent && (
                    <details className="mt-3">
                      <summary className="cursor-pointer text-xs font-medium text-gray-600 hover:text-gray-800">
                        View Raw Content
                      </summary>
                      <div className="mt-2 p-2 bg-gray-50 rounded text-xs font-mono whitespace-pre-wrap max-h-32 overflow-y-auto border">
                        {extractor.gherkinContent.slice(0, 500)}
                        {extractor.gherkinContent.length > 500 && "..."}
                      </div>
                    </details>
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>
          );
        })}
      </Accordion>

      {/* ✅ DEBUG: Record Dialog with debug info */}
      <Record
        isOpen={recordDialogOpen}
        onOpenChange={setRecordDialogOpen}
        testCaseName={selectedExtractorName}
        featureContent={selectedExtractorContent}
        isElementExtractor={true}
      />
    </>
  );
}

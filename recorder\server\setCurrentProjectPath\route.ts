import type { Request, Response } from "express";
import fs from "fs";

// Global variable to store current project path
let currentProjectPath: string | null = null;

// Helper function to get current project path (with fallback)
export function getCurrentProjectPath(): string {
  console.log(` Current project path: ${currentProjectPath}`);
  return currentProjectPath || process.cwd();
}

// Helper function to set current project path
export function setCurrentProjectPath(path: string): void {
  currentProjectPath = path;
  console.log(` Current project path updated to: ${path}`);
}

// POST route to receive project path from Next.js frontend
export const setCurrentProjectPathRoute = (req: Request, res: Response) => {
  try {
    const { currentprojectpath } = req.body;

    // Validation
    if (!currentprojectpath || typeof currentprojectpath !== 'string') {
      return res.status(400).json({ 
        success: false, 
        error: 'currentprojectpath is required and must be a string' 
      });
    }

    // Validate path exists (optional but recommended)
    if (!fs.existsSync(currentprojectpath)) {
      return res.status(400).json({ 
        success: false, 
        error: `Path does not exist: ${currentprojectpath}` 
      });
    }

    // Store the path in global variable
    setCurrentProjectPath(currentprojectpath);

    console.log(` Project path received from frontend: ${currentprojectpath}`);

    res.json({
      success: true,
      message: 'Current project path updated successfully',
      currentPath: currentprojectpath
    });

  } catch (error) {
    console.error(' Error setting current project path:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to set current project path' 
    });
  }
};

// GET route to check current project path (for testing)
export const getCurrentProjectPathRoute = (req: Request, res: Response) => {
  res.json({
    success: true,
    currentPath: getCurrentProjectPath(),
    isDefault: currentProjectPath === null
  });
};
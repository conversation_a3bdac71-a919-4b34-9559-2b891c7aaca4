
"use client";

import { useState, useCallback, useEffect, useRef, useMemo } from "react";
import { Button } from "@/components/ui/button";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, FileText, Paperclip, ChevronDown } from "lucide-react";
import { ApiData, TestApi } from "./testapis";
import { formatXML, getModuleNamesGlobal } from "@/services/testApi";
import { REQUEST_TYPE_OPTIONS, RequestTypeOption } from "@/data/dropdownOptions";

interface FormData {
  name: string;
  moduleCategory: string;
  requestType: string;
  url: string;
  environment: string;
  headerParams?: string;
  queryParameters?: string;
  expectedStatusCode?: string;
  exportVariable?: string;
  body?: string;
  exportVariables?: string;
  responseValidation?:string;
  dependent?:string;
  execute?: string;
  query?: string;
  dbExportVariables?: string;
  collectionName?: string;
}

type CreateAPIProps = {
  apiData?: TestApi | null;
  isReadOnly?: boolean;
  onCancel?: () => void;
  onApiCreated?: (moduleName: string) => void;
};

// Define the three modes
type ComponentMode = 'CREATE' | 'VIEW' | 'EDIT';

// const REQUEST_TYPE_OPTIONS: RequestTypeOption[] = [
//   { value: "get", label: "GET" },
//   { value: "post", label: "POST" },
//   { value: "put", label: "PUT" },
//   { value: "delete", label: "DELETE" },
//   { value: "patch", label: "PATCH" },
// ] as const;

const INITIAL_FORM_DATA: FormData = {
  name: "",
  moduleCategory: "",
  requestType: "",
  url: "",
  queryParameters: "",
  expectedStatusCode: "",
  exportVariable: "",
  body: "",
  exportVariables: "",
  environment: "",
  headerParams: "",
  responseValidation:"",
  dependent: "",
  execute: "",
  query: "",
  dbExportVariables: "",
  collectionName: ""
};

const CreateAPI: React.FC<CreateAPIProps> = ({ apiData, isReadOnly = false, onCancel, onApiCreated }) => {
  // Initialize form data directly if apiData is available
  const getInitialFormData = () => {
    if (apiData) {
      return {
        name: apiData.name || "",
        moduleCategory: apiData.module || "",
        requestType: apiData.requestType?.toLocaleLowerCase() || "",
        url: apiData.url || "",
        environment: apiData.environment || "",
        headerParams: apiData.headerParams || "",
        queryParameters: apiData.queryParams || "",
        expectedStatusCode: apiData.expectedStatusCode || "",
        exportVariable: apiData.exportVariable || "",
        body: apiData.body ? JSON.stringify(apiData.body, null, 2) : "",
        responseValidation: apiData.responseValidation || "",
        dependent: apiData.dependent || "",
        execute: apiData.execute || "Yes",
        query: apiData.query || "",
        dbExportVariables: apiData.dbExportVariables || "",
        collectionName: apiData.collectionName || "",
      };
    }
    return INITIAL_FORM_DATA;
  };


  const [formData, setFormData] = useState<FormData>(getInitialFormData);
  const [isLoading, setIsLoading] = useState(false);
  const [attachedFile, setAttachedFile] = useState<File | null>(null);
  const initializedApiId = useRef<number | null>(null);
  const [apiNames, setApiNames] = useState<string[]>([]);
  //initializedApiId tracks which API's data has already been loaded into the form, so the form doesn't get reset every time the component re-renders.

  // Module dropdown state
  const [isModuleDropdownOpen, setIsModuleDropdownOpen] = useState(false);
  const [moduleSearchTerm, setModuleSearchTerm] = useState("");
  const moduleDropdownRef = useRef<HTMLDivElement>(null);

  // Dependent dropdown state
  const [isDependentDropdownOpen, setIsDependentDropdownOpen] = useState(false);
  const [dependentSearchTerm, setDependentSearchTerm] = useState("");
  const [dependentDropdownModule,setDependentDropdownModule] = useState(""); //to track the last API call to fetch the testcases name for a module
  const dependentDropdownRef = useRef<HTMLDivElement>(null);

  // Three distinct modes - only one should be true at a time
  const [mode, setMode] = useState<ComponentMode>(() => {
    if (apiData && isReadOnly) return 'VIEW';
    if (apiData && !isReadOnly) return 'EDIT';
    return 'CREATE';
  });

  // Computed mode flags
  const isCreateMode = mode === 'CREATE';
  const isViewMode = mode === 'VIEW';
  const isEditMode = mode === 'EDIT';

  const moduleNames: string[] = getModuleNamesGlobal();

  // Filter modules based on search term
  const filteredModules = moduleNames.filter(module =>
    module.toLowerCase().includes(moduleSearchTerm.toLowerCase())
  );

  // Filter dependent APIs based on search term
  const filteredDependentAPIs = (apiNames || [])?.filter(apiName =>
    apiName?.toLowerCase()?.includes(dependentSearchTerm?.toLowerCase())
  );




const isGetRequest = formData.requestType === "get";
const isPostOrPutRequest = formData.requestType === "post" || formData.requestType === "put";


const isFormValid =
  formData.name.trim() &&
  formData.moduleCategory.trim() &&
  formData.requestType &&
  formData.url.trim();

  // Update form data only when a new API is loaded (not when edit mode changes)
  useEffect(() => {
    if (apiData && initializedApiId.current !== apiData.id) {
      const newFormData = {
        name: apiData.name || "",
        moduleCategory: apiData.module || "",
        requestType: apiData?.requestType?.toLowerCase() || "",
        url: apiData.url || "",
        environment: apiData.environment || "",
        headerParams: apiData.headerParams || "",
        queryParameters: apiData.queryParams || "",
        expectedStatusCode: apiData.expectedStatusCode || "",
        exportVariable: apiData.exportVariable || "",
        body: apiData.body ? JSON.stringify(apiData.body, null, 2) : "",
        responseValidation: apiData.responseValidation || "",
        dependent: apiData.dependent || "",
        execute: apiData.execute || "Yes",
        query: apiData.query || "",
        dbExportVariables: apiData.dbExportVariables || "",
        collectionName: apiData.collectionName || "",
      };

      setFormData(newFormData);
      setDependentValues(apiData.module);
      setMode(isReadOnly ? 'VIEW' : 'EDIT');
      initializedApiId.current = apiData.id || 0;
    }
  }, [apiData, isReadOnly]);

  // Sync search term with form data (for edit mode)
  useEffect(() => {
    if (formData.moduleCategory && !isModuleDropdownOpen) {
      setModuleSearchTerm(formData.moduleCategory);
    }
  }, [formData.moduleCategory, isModuleDropdownOpen]);

  // Sync dependent search term with form data (for edit mode)
  useEffect(() => {
    if (formData.dependent && !isDependentDropdownOpen) {
      setDependentSearchTerm(formData.dependent);
    }
  }, [formData.dependent, isDependentDropdownOpen]);

  // Handle click outside to close dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (moduleDropdownRef.current && !moduleDropdownRef.current.contains(event.target as Node)) {
        setIsModuleDropdownOpen(false);
      }
      if (dependentDropdownRef.current && !dependentDropdownRef.current.contains(event.target as Node)) {
        setIsDependentDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = useCallback((name: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  }, []);

  // Handle module selection
  const handleModuleSelect = useCallback((module: string) => {
    handleInputChange("moduleCategory", module);
    setModuleSearchTerm("");
    setIsModuleDropdownOpen(false);
    setDependentValues(module);
  }, [handleInputChange]);

  // Handle module input change
  const handleModuleInputChange = useCallback((value: string) => {
    handleInputChange("moduleCategory", value);
    setModuleSearchTerm(value);
    setIsModuleDropdownOpen(true);
    setDependentValues(value);
  }, [handleInputChange]);

  async function setDependentValues(module: string){
    if(moduleNames.includes(module)){
      const res = await fetchTestCases(module);
      setApiNames(res);
      setDependentDropdownModule(module);
    }
    else(
      setApiNames([])
    )
  }

  // Handle dependent selection
  const handleDependentSelect = useCallback((apiName: string) => {
    handleInputChange("dependent", apiName);
    setDependentSearchTerm("");
    setIsDependentDropdownOpen(false);
  }, [handleInputChange]);

  const convertFromTestData = (data: any) => {
    const allApiData = {
      SamplerProxyName: data.name,
      SamplerProxyPath: data.url,
      SamplerProxyMethod: data?.requestType?.toUpperCase(),
      Environment: data.environment,
      HeaderParameter: data.headerParams,
      QueryParameter: data.queryParameters,
      SamplerProxyBody: data.body,
      SamplerProxyResponseCode: data.expectedStatusCode
        ? parseInt(data.expectedStatusCode, 10)
        : undefined,
      ExportVariable: data.exportVariable,
      ResponseValidation: data.responseValidation,
      Dependent: data.dependent,
      Execute: data?.execute || "",
      Query: data.query,
      DBExportVariables: data.dbExportVariables,
      CollectionName: data.collectionName,
      moduleCategory: data.moduleCategory
    };
    return allApiData;
  }

  // Handle dependent input change
  const handleDependentInputChange = useCallback((value: string) => {
    handleInputChange("dependent", value);
    setDependentSearchTerm(value);
    setIsDependentDropdownOpen(true);
  }, [handleInputChange]);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isFormValid) return;

    setIsLoading(true);
    const newFormData = convertFromTestData(formData)

    try {
      if (isEditMode) {
        console.log(apiData?.id)
        console.log("Updating test case:", {id:apiData?.id, ...newFormData});
        const moduleCategory = newFormData.moduleCategory;
        const searchCriteria = apiData?.id;
        const updateData = {id:searchCriteria, ...newFormData};
        const response = await fetch("http://localhost:3001/api/excel/update", {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({moduleCategory, searchCriteria, updateData}),
        });

        if (!response.ok) {
          throw new Error(`API error: ${response.status}`);
        }

        const result = await response.json();
        console.log("API submission result:", result);
      } else {
        console.log("Creating test case:", newFormData);
        const response = await fetch("http://localhost:3001/api/excel/add", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(newFormData),
        });

        if (!response.ok) {
          throw new Error(`API error: ${response.status}`);
        } else {
          onApiCreated ? onApiCreated(newFormData?.moduleCategory) : '';
        }

        const result = await response.json();
        console.log("API submission result:", result);
      }
      // Only reset form data if it's create mode
      if (isCreateMode) {
        setFormData(INITIAL_FORM_DATA);
      }
      // Close dialog if onCancel is provided (for view/edit dialogs)
      if (onCancel) {
        onCancel();
      }
    } catch (error) {
      console.error(`Error submitting API:`, error);
    } finally {
      setIsLoading(false);
    }
  }, [formData, isFormValid, isEditMode, isCreateMode, onCancel]);

  const handleCancel = useCallback(() => {
    // Only reset form data if it's create mode
    if (isCreateMode) {
      setFormData(INITIAL_FORM_DATA);
      setAttachedFile(null);
    }

    // Close dialog if onCancel is provided
    if (onCancel) {
      console.log("canceling...");
      onCancel();
    }
  }, [isCreateMode, onCancel]);

  const handleFileAttach = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setAttachedFile(file);
      const reader = new FileReader();
      reader.onload = (event) => {
        let content = event.target?.result as string;

      if (file.name.endsWith(".xml")) {
        content = formatXML(content);
      }


        handleInputChange("body", content);
      };
      reader.readAsText(file);
    }
  }, [handleInputChange]);

  const handleRemoveFile = useCallback(() => {
    setAttachedFile(null);
  }, []);

  const handleEditMode = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setMode('EDIT');
  };

  async function fetchTestCases(module: string){
    const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/testapi/gettestcases?name=${module}`
      );
      const jsonData: { file: string; testcases: string[] } = await response.json();
      console.log(jsonData);
      return jsonData.testcases;
  }



  return (
    <div className="w-full bg-white p-4 relative">
      <div className="space-y-6">
        <form onSubmit={handleSubmit} className="space-y-6" noValidate>
                  <div className="relative mt-5">
                    <label className="absolute -top-3 left-2 bg-white px-1 text-xs font-medium text-gray-700">
                      Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      className={`w-full border border-gray-300 rounded p-2.5 pt-3.5 ${isViewMode ? 'bg-gray-50 cursor-not-allowed' : ''}`}
                      value={formData.name}
                      onChange={(e) => handleInputChange("name", e.target.value)}
                      required
                      disabled={isViewMode}
                      readOnly={isViewMode}
                    />
                  </div>

                  <div className="relative mt-5" ref={moduleDropdownRef}>
                    <label className="absolute -top-2.5 left-3 bg-white px-1 text-xs font-medium text-gray-700 z-10">
                      Module <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        className={`w-full border border-gray-300 rounded p-2.5 pt-3.5 pr-10 ${isViewMode ? 'bg-gray-50 cursor-not-allowed' : ''}`}
                        value={formData.moduleCategory}
                        onChange={(e) => handleModuleInputChange(e.target.value)}
                        onFocus={() => !isViewMode && setIsModuleDropdownOpen(true)}
                        placeholder="Type to search or select a module"
                        required
                        disabled={isViewMode}
                        readOnly={isViewMode}
                      />
                      {!isViewMode && (
                        <ChevronDown
                          className={`absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 cursor-pointer transition-transform ${isModuleDropdownOpen ? 'rotate-180' : ''}`}
                          onClick={() => setIsModuleDropdownOpen(!isModuleDropdownOpen)}
                        />
                      )}

                      {/* Dropdown */}
                      {isModuleDropdownOpen && !isViewMode && (
                        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                          {filteredModules.length > 0 ? (
                            filteredModules.map((module) => (
                              <div
                                key={module}
                                className="px-3 py-2 hover:bg-blue-50 cursor-pointer text-sm"
                                onClick={() => handleModuleSelect(module)}
                              >
                                {module}
                              </div>
                            ))
                          ) : (
                            formData.moduleCategory.trim() && (
                              <div
                                className="px-3 py-2 text-sm text-blue-600 bg-blue-50 hover:bg-blue-100 cursor-pointer"
                                onClick={() => handleModuleSelect(formData.moduleCategory)}
                              >
                                Creating a module &ldquo;{formData.moduleCategory}&rdquo;
                              </div>
                            )
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="relative mt-5">
                    <label className="absolute -top-3 left-2 bg-white px-1 text-xs font-medium text-gray-700">
                      Request Type <span className="text-red-500">*</span>
                    </label>
                    <Select
                      key={`request-type-${formData.requestType}`}
                      value={formData.requestType || ""}
                      onValueChange={(value) => handleInputChange("requestType", value)}
                      disabled={isViewMode}
                    >
                      <SelectTrigger className={`w-full border border-gray-300 rounded p-2.5 pt-3.5 h-auto ${isViewMode ? 'bg-gray-50 cursor-not-allowed' : ''}`}>
                        <SelectValue placeholder="Select Request Type" />
                      </SelectTrigger>
                      <SelectContent className="rounded-lg shadow-lg border-gray-200">
                        {REQUEST_TYPE_OPTIONS.map((option: RequestTypeOption) => (
                          <SelectItem key={option.value} value={option.value} className="rounded-md">
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="relative mt-5">
                    <label className="absolute -top-3 left-2 bg-white px-1 text-xs font-medium text-gray-700">
                      URL <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="url"
                      className={`w-full border border-gray-300 rounded p-2.5 pt-3.5 ${isViewMode ? 'bg-gray-50 cursor-not-allowed' : ''}`}
                      value={formData.url}
                      onChange={(e) => handleInputChange("url", e.target.value)}
                      required
                      placeholder="https://example.com/api/endpoint"
                      disabled={isViewMode}
                      readOnly={isViewMode}
                    />
                  </div>

                  {/* <div className="relative mt-5">
                    <label className="absolute -top-3 left-2 bg-white px-1 text-xs font-medium text-gray-700">
                      Environment
                    </label>
                    <input
                      type="environment"
                      className={`w-full border border-gray-300 rounded p-2.5 pt-3.5 ${isViewMode ? 'bg-gray-50 cursor-not-allowed' : ''}`}
                      value={formData.environment}
                      onChange={(e) => handleInputChange("environment", e.target.value)}
                      placeholder="environment"
                      disabled={isViewMode}
                      readOnly={isViewMode}
                    />
                  </div> */}

                  {isGetRequest && (
                    <>
                      <div className="relative mt-5">
                        <label className="absolute -top-3 left-2 bg-white px-1 text-xs font-medium text-gray-700 flex items-center gap-2">
                          Query Parameters
                          {!isViewMode && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              className="h-5 w-5 p-0 rounded-full hover:bg-gray-50 transition-colors"
                              aria-label="Add query parameter"
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                          )}
                        </label>
                        <input
                          type="text"
                          className={`w-full border border-gray-300 rounded p-2.5 pt-3.5 font-mono text-sm ${isViewMode ? 'bg-gray-50 cursor-not-allowed' : ''}`}
                          value={formData.queryParameters}
                          onChange={(e) => handleInputChange("queryParameters", e.target.value)}
                          placeholder="key1=value1&key2=value2"
                          disabled={isViewMode}
                          readOnly={isViewMode}
                        />
                      </div>
                    </>
                  )}

                  {isPostOrPutRequest && (
                    <>
                      <div className="relative mt-5">
                        <label className="absolute -top-3 left-2 bg-white px-1 text-xs font-medium text-gray-700 flex items-center gap-2">
                          Request Body
                          {!isViewMode && (
                            <div className="relative">
                              <input
                                type="file"
                                id="file-upload"
                                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                                onChange={handleFileAttach}
                                accept=".json,.xml,.txt,.csv"
                              />
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                className="h-6 w-32 p-0 rounded-full hover:bg-gray-50 transition-colors"
                                aria-label="Attach file"
                              >
                                <Paperclip className="h-3 w-3" /> Attach file
                              </Button>
                            </div>
                          )}
                        </label>

                        {attachedFile && !isViewMode && (
                          <div className="flex items-center gap-2 p-2 bg-blue-50 border border-blue-200 rounded-lg mb-2">
                            <FileText className="h-4 w-4 text-blue-600" />
                            <span className="text-sm text-blue-800 flex-1">{attachedFile.name}</span>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={handleRemoveFile}
                              className="h-6 w-6 p-0 text-blue-600 hover:text-blue-800 hover:bg-blue-100"
                              aria-label="Remove file"
                            >
                              ×
                            </Button>
                          </div>
                        )}

{isViewMode ? (
  <pre className="w-full border border-gray-300 rounded p-2.5 font-mono text-sm bg-gray-50 whitespace-pre-wrap">
    {formData.body}
  </pre>
) : (
                        <textarea
                          className={`w-full border border-gray-300 rounded p-2.5 pt-3.5 font-mono text-sm resize-none ${isViewMode ? 'bg-gray-50 cursor-not-allowed' : ''}`}
                          value={formData.body}
                          onChange={(e) => handleInputChange("body", e.target.value)}
                          placeholder="Enter request body (JSON, XML, etc.) or attach a file"
                          rows={4}
                          disabled={isViewMode}
                          readOnly={isViewMode}
                        />
                        )}
                      </div>

                    </>
                  )}

                  <div className="relative mt-5">
                    <label className="absolute -top-3 left-2 bg-white px-1 text-xs font-medium text-gray-700">
                      Expected Status Code
                    </label>
                    <input
                      type="number"
                      min="100"
                      max="599"
                      className={`w-full border border-gray-300 rounded p-2.5 pt-3.5 ${isViewMode ? 'bg-gray-50 cursor-not-allowed' : ''}`}
                      value={formData.expectedStatusCode}
                      onChange={(e) => handleInputChange("expectedStatusCode", e.target.value)}
                      placeholder="200"
                      disabled={isViewMode}
                      readOnly={isViewMode}
                    />
                  </div>

                  <div className="relative mt-5">
                    <label className="absolute -top-3 left-2 bg-white px-1 text-xs font-medium text-gray-700">
                      Response Validation
                    </label>
                    <input
                      type="text"
                      className={`w-full border border-gray-300 rounded p-2.5 pt-3.5 ${isViewMode ? 'bg-gray-50 cursor-not-allowed' : ''}`}
                      value={formData.responseValidation}
                      onChange={(e) => handleInputChange("responseValidation", e.target.value)}
                      placeholder="Response Validation"
                      disabled={isViewMode}
                      readOnly={isViewMode}
                    />
                  </div>

                  <div className="relative mt-5">
                    <label className="absolute -top-3 left-2 bg-white px-1 text-xs font-medium text-gray-700">
                      Export Variable
                    </label>
                    <input
                      type="text"
                      className={`w-full border border-gray-300 rounded p-2.5 pt-3.5 font-mono text-sm ${isViewMode ? 'bg-gray-50 cursor-not-allowed' : ''}`}
                      value={formData.exportVariable}
                      onChange={(e) => handleInputChange("exportVariable", e.target.value)}
                      placeholder="variableName"
                      disabled={isViewMode}
                      readOnly={isViewMode}
                    />
                  </div>

                  <div className="relative mt-5" ref={dependentDropdownRef}>
                    <label className="absolute -top-2.5 left-3 bg-white px-1 text-xs font-medium text-gray-700 z-10">
                      Dependent
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        className={`w-full border border-gray-300 rounded p-2.5 pt-3.5 pr-10 ${isViewMode ? 'bg-gray-50 cursor-not-allowed' : ''}`}
                        value={formData.dependent || ""}
                        onChange={(e) => handleDependentInputChange(e.target.value)}
                        onFocus={() => !isViewMode && setIsDependentDropdownOpen(true)}
                        placeholder="Type to search for dependent API"
                        disabled={isViewMode}
                        readOnly={isViewMode}
                      />
                      {!isViewMode && (
                        <ChevronDown
                          className={`absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 cursor-pointer transition-transform ${isDependentDropdownOpen ? 'rotate-180' : ''}`}
                          onClick={() => setIsDependentDropdownOpen(!isDependentDropdownOpen)}
                        />
                      )}

                      {/* Dropdown */}
                      {isDependentDropdownOpen && !isViewMode && (
                        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                          {filteredDependentAPIs.length > 0 ? (
                            filteredDependentAPIs.map((apiName, index) => (
                              <div
                                key={`${apiName}-${index}`}
                                className="px-3 py-2 hover:bg-blue-50 cursor-pointer text-sm"
                                onClick={() => handleDependentSelect(apiName)}
                              >
                                {apiName}
                              </div>
                            ))
                          ) : (
                            <div className="px-3 py-2 text-sm text-gray-500">
                              No matching Test cases found
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>



                  <div className="flex justify-end gap-4 pt-6 mt-8">
                    <Button
                      type="button"
                      onClick={handleCancel}
                      disabled={isLoading}
                      className="px-5 py-2.5 border border-gray-300 rounded bg-white text-gray-700 font-medium hover:bg-gray-100 transition-colors duration-200"
                    >
                      Cancel
                    </Button>

                    {isViewMode ? (
                      <Button
                        type="button"
                        onClick={handleEditMode}
                        className="px-5 py-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded font-medium transition-all duration-200"
                      >
                        Edit
                      </Button>
                    ) : (
                      <Button
                        type="submit"
                        disabled={isLoading || !isFormValid}
                        className="px-5 py-2.5 bg-green-600 hover:bg-green-700 text-white rounded font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                      >
                        {isLoading ? (
                          <>
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                            {isEditMode ? "Updating..." : "Creating..."}
                          </>
                        ) : (
                          isEditMode ? "Update" : "Create"
                        )}
                      </Button>
                    )}
                  </div>
                </form>
              </div>
            </div>
  );
};

export default CreateAPI;

[{"action": "navigate", "url": "https://demo.automationtesting.in/Index.html", "timestamp": "2025-07-24T14:52:38.609Z"}, {"action": "click", "selector": "//*[@id=\"email\"]", "label": "emailidforsignup_@id=\"email\"]", "timestamp": "2025-07-24T14:52:40.567Z"}, {"action": "type", "selector": "//*[@id=\"email\"]", "value": "hello", "label": "emailidforsignup_id_xpath", "timestamp": "2025-07-24T14:52:43.170Z"}, {"action": "click", "selector": "//*[@id=\"enterimg\"]", "label": "img-1753368763386_@id=\"enterimg\"]", "timestamp": "2025-07-24T14:52:43.385Z"}, {"action": "click", "selector": "//input[@placeholder=\"First Name\"]", "label": "firstname_@placeholder=\"First Name\"]", "timestamp": "2025-07-24T14:52:45.255Z"}, {"action": "type", "selector": "//input[@placeholder=\"First Name\"]", "value": "syed", "label": "firstname_placeholder_xpath", "timestamp": "2025-07-24T14:52:46.311Z"}, {"action": "type", "selector": "//input[@placeholder=\"Last Name\"]", "value": "nauman", "label": "lastname_placeholder_xpath", "timestamp": "2025-07-24T14:52:48.556Z"}, {"action": "click", "selector": "//textarea[contains(@class, \"form-control\")]", "label": "address_@class, \"form-control\")]", "timestamp": "2025-07-24T14:52:48.772Z"}, {"action": "type", "selector": "//textarea[contains(@class, \"form-control\")]", "value": "abc ", "label": "address_//textarea[contains(@_xpath", "timestamp": "2025-07-24T14:52:52.201Z"}, {"action": "click", "selector": "//input[@type=\"email\"]", "label": "input-1753368772427_@type=\"email\"]", "timestamp": "2025-07-24T14:52:52.425Z"}, {"action": "type", "selector": "//input[@type=\"email\"]", "value": "<EMAIL>", "label": "input-1753368776694_type_xpath", "timestamp": "2025-07-24T14:52:56.693Z"}, {"action": "click", "selector": "//input[@type=\"tel\"]", "label": "phone*_@type=\"tel\"]", "timestamp": "2025-07-24T14:52:56.914Z"}, {"action": "type", "selector": "//input[@type=\"tel\"]", "value": "1112223334", "label": "phone*_type_xpath", "timestamp": "2025-07-24T14:53:00.793Z"}, {"action": "click", "selector": "//div[contains(@class, \"form-group\")]/div[1]/label[1]", "label": "male_@class, \"form-group\")]/div[1]/label[1]", "timestamp": "2025-07-24T14:53:01.010Z"}, {"action": "click", "selector": "//div[contains(@class, \"col-md-4\")]/label[1]/input[1]", "label": "radiooptions_@class, \"col-md-4\")]/label[1]/input[1]", "timestamp": "2025-07-24T14:53:01.013Z"}, {"action": "type", "selector": "//div[contains(@class, \"col-md-4\")]/label[1]/input[1]", "value": "Male", "label": "radiooptions_//div[contains(@_xpath", "timestamp": "2025-07-24T14:53:01.016Z"}]
"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON>ead<PERSON>, CardTitle } from "@/components/ui/card"

type StatCardProps = {
  title: string
  value: number
}

export default function StatCard({ title, value }: StatCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-sm text-muted-foreground">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
      </CardContent>
    </Card>
  )
}

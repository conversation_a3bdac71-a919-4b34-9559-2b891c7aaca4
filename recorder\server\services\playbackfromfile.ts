// puppeteerPlayer.mjs (or .js if you set type: "module" in package.json)

import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Fix __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);


async function runPlayback(browserName, jsonFilePath) {
 const actions = JSON.parse(fs.readFileSync(path.resolve(__dirname, '../../recordings', jsonFilePath), 'utf-8'));
     const executablePath = browserName === 'chrome'
    ? 'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe' // or your system-specific path
    : browserName === 'chromium'
    ? puppeteer.executablePath()
    : undefined;

  const browser = await puppeteer.launch({
    headless: false,
    executablePath,
    defaultViewport: { width: 1280, height: 720 },
    args: [
      '--window-size=1280,720',
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-gpu',
      '--no-zygote',
      '--deterministic-fetch'
    ],
    timeout: 60000
  });

  const page = await browser.newPage();

  for (const action of actions) {
    console.log(`Performing: ${action.action}`);
    try {
      switch (action.action) {
        case 'navigate':
          await page.goto(action.url, { waitUntil: 'networkidle2' });
          break;

        case 'click':
          await page.waitForSelector(action.selector);
          await page.click(action.selector);
          break;

        case 'type':
          await page.waitForSelector(action.selector);
          await page.type(action.selector, action.value || '');
          break;

        case 'waitForSelector':
          await page.waitForSelector(action.selector, { timeout: action.timeout || 5000 });
          break;

        case 'wait':
          await page.waitForTimeout(action.time || 1000);
          break;

        case 'screenshot':
          await page.screenshot({ path: action.path || 'screenshot.png' });
          break;

        case 'scroll':
          await page.evaluate((y) => window.scrollBy(0, y), action.y || 100);
          break;

        case 'hover':
          await page.hover(action.selector);
          break;

        case 'press':
          await page.keyboard.press(action.key);
          break;

        default:
          console.warn(`Unknown action: ${action.action}`);
      }
    } catch (err) {
      console.error(`Error during ${action.action}:`, err.message);
    }
  }

  return browser;
}

export default runPlayback;

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import api from '@/app/services/api';

interface AddUserFormProps {
  onCancel: () => void;
  onRecordingStop: () => void;
}

export default function AddUserForm({ onCancel, onRecordingStop }: AddUserFormProps) {
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [submitMessage, setSubmitMessage] = useState<{type: 'success' | 'error', text: string} | null>(null);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.username.trim()) {
      newErrors.username = "Username is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (formData.password.length < 6) {
      newErrors.password = "Password must be at least 6 characters";
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "Please confirm your password";
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      setIsLoading(true);
      setSubmitMessage(null);
      
    // Adjust the path to your axios instance

try {
  const response = await api.post('/users', {
    tenant_id: 1, // Ideally dynamic
    name: formData.username, // Backend expects 'name'
    email: formData.email,
    password: formData.password, // Hash on backend
    is_enabled: 1,
  });

  // Axios auto-parses JSON and throws on error
  const data = response.data;

  setSubmitMessage({
    type: 'success',
    text: 'User created successfully!',
  });

  // Reset form
  setFormData({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
  });

  // Close dialog after short delay
  setTimeout(() => {
    onRecordingStop();
    onCancel();
  }, 1500);

} catch (error: any) {
  console.error('Error creating user:', error);

  const message =
    error.response?.data?.error ||
    error.response?.data?.message ||
    'Network error. Please check your connection and try again.';

  setSubmitMessage({
    type: 'error',
    text: message,
  });

} finally {
  setIsLoading(false);
}

      
    }
  };

  return (
    <div className="p-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        {submitMessage && (
          <div className={`p-4 rounded-md border-2 ${submitMessage.type === 'success' ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'}`}>
            <div className={`text-sm font-medium ${submitMessage.type === 'success' ? 'text-green-800' : 'text-red-800'}`}>
              {submitMessage.text}
            </div>
          </div>
        )}
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-3">
            <Label htmlFor="username" className="text-base font-medium">
              Username <span className="text-red-500">*</span>
            </Label>
            <Input
              id="username"
              type="text"
              value={formData.username}
              onChange={(e) => handleInputChange("username", e.target.value)}
              placeholder="Enter username"
              disabled={isLoading}
              className={`h-12 text-base ${errors.username ? "border-red-500 focus:ring-red-500" : "focus:ring-[#15537C]"}`}
            />
            {errors.username && <span className="text-red-500 text-sm font-medium">{errors.username}</span>}
          </div>

          <div className="space-y-3">
            <Label htmlFor="email" className="text-base font-medium">
              Email ID <span className="text-red-500">*</span>
            </Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange("email", e.target.value)}
              placeholder="Enter email address"
              disabled={isLoading}
              className={`h-12 text-base ${errors.email ? "border-red-500 focus:ring-red-500" : "focus:ring-[#15537C]"}`}
            />
            {errors.email && <span className="text-red-500 text-sm font-medium">{errors.email}</span>}
          </div>

          <div className="space-y-3">
            <Label htmlFor="password" className="text-base font-medium">
              Password <span className="text-red-500">*</span>
            </Label>
            <Input
              id="password"
              type="password"
              value={formData.password}
              placeholder="Enter password"
              disabled={isLoading}
              onChange={(e) => handleInputChange("password", e.target.value)}
              className={`h-12 text-base ${errors.password ? "border-red-500 focus:ring-red-500" : "focus:ring-[#15537C]"}`}
            />
            {errors.password && <span className="text-red-500 text-sm font-medium">{errors.password}</span>}
          </div>

          <div className="space-y-3">
            <Label htmlFor="confirmPassword" className="text-base font-medium">
              Confirm Password <span className="text-red-500">*</span>
            </Label>
            <Input
              id="confirmPassword"
              type="password"
              value={formData.confirmPassword}
              onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
              placeholder="Confirm password"
              disabled={isLoading}
              className={`h-12 text-base ${errors.confirmPassword ? "border-red-500 focus:ring-red-500" : "focus:ring-[#15537C]"}`}
            />
            {errors.confirmPassword && <span className="text-red-500 text-sm font-medium">{errors.confirmPassword}</span>}
          </div>
        </div>

        <div className="flex justify-end gap-4 pt-6 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
            className="px-8 py-3 text-base"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isLoading}
            className="bg-[#15537C] text-white hover:brightness-110 px-8 py-3 text-base"
          >
            {isLoading ? "Adding User..." : "Add User"}
          </Button>
        </div>
      </form>
    </div>
  );
}
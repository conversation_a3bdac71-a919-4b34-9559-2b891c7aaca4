"use client";

import { useState, useEffect } from "react";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { Plus } from "lucide-react";

type Config = {
  Environment: string;
  URI: string;
  DBtype: string;
  DBhost: string;
  DBport: string;
  DBuser: string;
  DBpwd: string;
  DBname: string;
};

type UpdatedConfig = {
  Environment?: string;
  URI?: string;
  DBtype?: string;
  DBhost?: string;
  DBport?: string;
  DBuser?: string;
  DBpwd?: string;
  DBname?: string;
};

const initialConfigs: Config[] = [
  {
    Environment: "QA",
    URI: "www.abjayondatabase.com",
    DBtype: "mysql",
    DBhost: "127.0.0.1",
    DBport: "3306",
    DBuser: "admin",
    DBpwd: "admin@123",
    DBname: "ANTP_web",
  },
  {
    Environment: "Stage",
    URI: "www.abjayondatabase.com",
    DBtype: "postgresql",
    DBhost: "*********",
    DBport: "3307",
    DBuser: "admin",
    DBpwd: "admin@123",
    DBname: "ANTP_web",
  }
];

export default function ConfigPage() {
  const [configs, setConfigs] = useState<Config[]>([]);

  const [loading, setLoading] = useState(false);
  const [updatedConfig,setUpdatedConfig] = useState<UpdatedConfig>({});
  const [allowEditing, setAllowEditing] = useState(true);
  // can cause error here when config array is empty
  const columns: (keyof Config)[] =configs.length > 0 ? (Object.keys(configs[0]) as (keyof Config)[]) : []; 
  const placeholders : string[] = ["Enter Environment","Enter URI","Enter DB type","Enter DB host","Enter DB port","Enter DB user","Enter DB pwd","Enter DB name"];
  const [selectedConfigs, setSelectedConfigs] = useState<boolean[]>(
    Array(initialConfigs.length).fill(false)
  );
  const [originalConfigCount, setOriginalConfigCount] = useState(initialConfigs.length);
  const [isSaving, setIsSaving] = useState(false);

  const [editingCell, setEditingCell] = useState<{
    row: number | null;
    field: string | null;
  }>({ row: null, field: null });

  //fetch configs
  const fetchConfigs = async () => {
     setLoading(true);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/config/read`);

      if (!response.ok) {
        throw new Error(`Failed to fetch users: ${response.status}`);
      }

      const userData = await response.json();
      setConfigs(userData.data);
    } catch (error: any) {
      console.error("Error fetching users:", error);
      // setUsersFetchError(error.message || "Failed to load users");
    } finally {
      // setIsLoadingUsers(false);
        setLoading(false);

    }
  };

  useEffect(() => {
    fetchConfigs();
  }, []);

  const handleCellClick = (rowIndex: number, field: string) => {
    console.log(rowIndex,field);
    setEditingCell({ row: rowIndex, field });
  };

   const handleBlur = () => {
    setEditingCell({ row: null, field: null });
  };

  const handleCellChange = (
      e: React.ChangeEvent<HTMLInputElement>,
      rowIndex: number,
      field: keyof Config
    ) => {
      const updatedConfigs = [...configs];
      updatedConfigs[rowIndex] = {
        ...updatedConfigs[rowIndex],
        [field]: e.target.value,
      };
      setConfigs(updatedConfigs);
    };

  // Create new config template
  function getNewConfig(): Config {
    return {
      Environment: "",
      URI: "",
      DBtype: "",
      DBhost: "",
      DBport: "",
      DBuser: "",
      DBpwd: "",
      DBname: "",
    };
  }
  //Add row logic
  const handleAddRow = () => {
    const newConfig = getNewConfig();
    setConfigs([...configs, newConfig]);
    setSelectedConfigs([...selectedConfigs, false]);
  };

  //toggle logic
  const toggleElement = (index: number) => {
    const updated = [...selectedConfigs];
    updated[index] = !updated[index];
    setSelectedConfigs(updated);
  };

  const handleDeleteSelected = () => {
    const elementsToKeep = configs.filter(
      (_, index) => !selectedConfigs[index]
    );
    console.log(elementsToKeep);

    // Update local state - renumber the remaining elements
    const renumbered = elementsToKeep.map((element) => ({
      ...element,
    }));

    setConfigs(renumbered);
    setSelectedConfigs(Array(renumbered.length).fill(false));

    console.log("Deleted selected elements (static mode)");
  }

  //save logic
  const handleSaveClick = ()=> {
      // Get only the new elements (beyond original count)
    const newElements = configs.slice(originalConfigCount);
    console.log(configs);

    //logic to save all the new values in excel file
    
  }

  return (
    <>
      <header className="bg-background sticky top-0 flex items-center justify-between border-b p-4">
        <h1 className="text-left font-medium text-[24px] leading-[20px] tracking-[0px] text-[#2C2E2E]">
          Project Configurations
        </h1>
        <div className="flex items-center gap-[20px]">
          {/* Add Element Button */}
          <a
            onClick={(e) => {
              e.stopPropagation();
              handleAddRow();
            }}
            title="Add new config"
          >
            <Plus className="w-7 h-8 cursor-pointer text-[#1d5881]" />
          </a>
          {/* save elements */}
          <a
            onClick={(e) => {
              e.stopPropagation();
              handleSaveClick();
            }}
            className="cursor-pointer"
            title="Save new configs"
          >
            <img
              src="/save1.svg"
              className="w-7 h-8 cursor-pointer"
              // style={{ opacity: isSaving ? 0.5 : 1 }}
            />
          </a>
          <a
            onClick={(e) => {
              e.stopPropagation();
              handleDeleteSelected();
            }}
            title="Delete config"
          >
            <img
              src="/Group 21846.svg"
              className="w-6 h-6 cursor-pointer hover:text-[#7b7c7a] cursor-pointer"
            />
          </a>
        </div>
      </header>
      {
        loading &&  <div className="flex h-screen items-center justify-center">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#15537C]"></div>
              <span>Loading test configurations...</span>
            </div>
          </div>
      }
      <div className="p-2">
        { configs.length == 0 ? 
            <div className="p-8 text-center text-gray-500">
              <div className="text-gray-400 mb-4">
                <svg
                  className="w-16 h-16 mx-auto mb-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                  />
                </svg>
                
                <p className="text-lg font-medium text-gray-600">
                  No configurations found
                </p>
                <p className="text-sm text-gray-500">
                  Start by identifying and creating configurations for your project
                </p>
              </div>
            </div> :
            <Table>
              <TableHeader className="bg-[#15537c]">
                <TableRow>
                  <TableHead></TableHead>
                  <TableHead className="text-[#ffffff]">Environment Name</TableHead>
                  <TableHead className="text-[#ffffff]">URI</TableHead>
                  <TableHead className="text-[#ffffff]">Database Type</TableHead>
                  <TableHead className="text-[#ffffff]">Database Host</TableHead>
                  <TableHead className="text-[#ffffff]">Database Port</TableHead>
                  <TableHead className="text-[#ffffff]">Database User</TableHead>
                  <TableHead className="text-[#ffffff]">
                    Database Password
                  </TableHead>
                  <TableHead className="text-[#ffffff]">Database Name</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {configs.map((config : Config, index : number) => (
                  <TableRow
                    key={index}
                    className={index % 2 === 0 ? "bg-[#F7F9FC]" : "bg-[#FFFFFF4D]"}
                  >
                    {/* static checkbox cell */}
                    <TableCell className="w-[5%]">
                      <input
                        type="checkbox"
                        checked={selectedConfigs[index]}
                        onChange={() => toggleElement(index)}
                        className="accent-blue-500"
                      />
                    </TableCell>
                    {columns.map((col, i) => (
                      <TableCell key={i} className="h-13">
                        {allowEditing &&
                        editingCell.row === index &&  editingCell.field === col ? (
                          <input
                            value={config[col]}
                            onChange={(e) => handleCellChange(e, index, col)}
                            onBlur={handleBlur}
                            autoFocus
                            className="w-35 h-7 bg-white border border-gray-300 rounded px-2 py-2"
                            placeholder={placeholders[i]}
                          />
                        ) : (
                          <input
                            onClick={
                              allowEditing
                                ? () => handleCellClick(index,col)
                                : undefined
                            }
                            className="cursor-pointer"
                            value= {config[col]}
                             placeholder={placeholders[i]}
                          />
                           
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
        }
       
      </div>
    </>
  );
}

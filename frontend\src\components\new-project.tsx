"use client";
import api from "@/app/services/api";
import axios from "axios";
import { updateWebuirecorderPath } from "@/lib/webUiRecorder";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ROLES } from "@/data/dropdownOptions";
import { useState, useEffect } from "react";
import { PlusCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Toaster } from "@/components/ui/toaster";
interface User {
  id: string;
  name: string;
  email?: string;
}

interface Role {
  id: string;
  name: string;
}

interface Props {
  open: boolean;
  setOpen: (value: boolean) => void;
}

export default function CreateNewProject({ open, setOpen }: Props) {
  const { toast } = useToast();
  const [projectName, setProjectName] = useState("");
  const [projectDescription, setProjectDescription] = useState("");
  const [projectUrl, setProjectUrl] = useState("");
  const [users, setUsers] = useState<User[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [usersFetchError, setUsersFetchError] = useState("");
  const [roles, setRoles] = useState<Role[]>([]);
  const [isLoadingRoles, setIsLoadingRoles] = useState(false);
  const [rolesFetchError, setRolesFetchError] = useState("");
  const [team, setTeam] = useState([
    { assignee: "", role: "" },
    { assignee: "", role: "" },
    { assignee: "", role: "" },
    { assignee: "", role: "" },
  ]);

  // Check for success message on component mount
  useEffect(() => {
    const successMessage = sessionStorage.getItem("projectCreatedSuccess");
    if (successMessage) {
      setTimeout(() => {
        toast({
          title: "Success",
          description: successMessage,
          className: "bg-green-600 text-white border-green-600",
        });
        sessionStorage.removeItem("projectCreatedSuccess");
      }, 100);
    }
  }, [toast]);

  // Check for success message on component mount
  useEffect(() => {
    const successMessage = sessionStorage.getItem("projectCreatedSuccess");
    if (successMessage) {
      setTimeout(() => {
        toast({
          title: "Success",
          description: successMessage,
          className:
            "bg-green-600 text-white border-green-600 text-xs max-w-xs p-2",
        });
        sessionStorage.removeItem("projectCreatedSuccess");
      }, 100);
    }
  }, [toast]);

  // Fetch users from API
  const fetchUsers = async () => {
    setIsLoadingUsers(true);
    setUsersFetchError("");

try {
 const response = await api.get('/users');
  setUsers(response.data);
} catch (error: any) {
  console.error('Error fetching users:', error);
  setUsersFetchError(
    error.response?.data?.error || error.message || 'Failed to load users'
  );
} finally {
  setIsLoadingUsers(false);
}
  };

  // Fetch roles from API
  const fetchRoles = async () => {
    setIsLoadingRoles(true);
    setRolesFetchError("");

try {
  const response = await api.get('/roles');
  setRoles(response.data);
} catch (error: any) {
  console.error('Error fetching roles:', error);
  setRolesFetchError(
    error.response?.data?.error || error.message || 'Failed to load roles'
  );
} finally {
  setIsLoadingRoles(false);
}

  };

  // Fetch users and roles when component mounts or dialog opens
  useEffect(() => {
    if (open) {
      fetchUsers();
      fetchRoles();
    }
  }, [open]);

  // Adds new team member function
  const handleAddTeamMember = () => {
    setTeam(prev => [...prev, { assignee: "", role: "" }]);
  };

const handleSubmit = (e: React.FormEvent) => {
  e.preventDefault();

  if (!projectName) {
    toast({
      title: "Missing field",
      description: "Project name is required.",
      variant: "destructive",
    });
    return;
  }

  // Step 1: Create project files via internal API (still uses fetch, as it's internal)
  axios
    .post('/api/create-project', { projectName, projectUrl })
    .then((res) => {
      const data = res.data;
      sessionStorage.setItem("projectCreatedSuccess", `${data.message}`);
      return data; // Continue to next step
    })
    .then(() => {
      // Step 2: Add project in DB
      return api.post("/projects", {
        name: projectName,
        description: projectDescription,
        projecturl: projectUrl,
        tenant_id: "1",
        module_id: "1",
        is_enabled: true,
      });
    })
    .then((res) => {
      const projectId = res.data.id;

      const validTeamMembers = team.filter(
        (member) => member.assignee && member.role
      );

      if (validTeamMembers.length === 0) {
        return Promise.resolve(projectId);
      }

      const assignmentPromises = validTeamMembers.map((member) =>
        api.post("/projectassignment", {
          projectid: projectId,
          assignee: member.assignee,
          role: member.role,
        })
      );

      return Promise.all(assignmentPromises).then(() => projectId);
    })
    .then(async (projectId) => {
      try {
        console.log(" Fetching updated config from API...");
        const res = await axios.get('/api/get-config');
        const projectPath = res.data.projectPath;

        console.log(" Got projectPath from dynamic config:", projectPath);

        if (!projectPath) {
          throw new Error("ProjectPath not found in config");
        }

        return updateWebuirecorderPath(projectPath);
      } catch (error) {
        console.error(" Failed to get dynamic config:", error);
        throw error;
      }
    })
    .catch((error) => {
      console.warn(" Intermediate step failed:", error);
    })
    .then(() => {
      setOpen(false);
      window.location.reload();
    })
    .catch((err) => {
      console.error(" Final project creation error:", err);

      const errorMessage =
        err.response?.data?.error || err.message || "Unknown error occurred";

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    });
};

  const handleRemoveMember = (indexToRemove: number) => {
    setTeam((prevTeam) =>
      prevTeam.filter((_, index) => index !== indexToRemove)
    );
  };

  const handleRetryFetchUsers = () => {
    fetchUsers();
  };

  const handleRetryFetchRoles = () => {
    fetchRoles();
  };

  const handleCloseDialog = (open: boolean) => {
    if (!open) {
      window.location.reload();
    }
    setOpen(open);
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleCloseDialog}>
       <DialogContent className={`max-w-6xl h-[95vh] ${team.length > 4 ? 'overflow-y-auto' : ''}`}>
          <form
            onSubmit={handleSubmit}
            className="w-full h-full p-4 space-y-3 flex flex-col"
          >
            <DialogHeader>
              <DialogTitle>Create New Project</DialogTitle>
            </DialogHeader>
            <div className="flex-1 space-y-3">
              <div>
                <label className="block text-sm font-medium">
                  Project Name *
                </label>
                <Input
                  placeholder="Enter Project Name"
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium">
                  Project Description *
                </label>
                <Input
                  placeholder="Enter Project Description"
                  value={projectDescription}
                  onChange={(e) => setProjectDescription(e.target.value)}
                />
              </div>
              <div className="flex items-end justify-between">
                <div>
                  <label className="block text-sm font-medium">
                    Project URL *
                  </label>
                  <Input
                    placeholder="Enter Project url"
                    value={projectUrl}
                    onChange={(e) => setProjectUrl(e.target.value)}
                    className="w-131 h-9 px-2 border border-gray-300 rounded-md bg-white"
                  />
                </div>

                <div className="flex items-center gap-4">
                  <Button
                    type="button"
                    className="bg-blue-700 text-white hover:bg-blue-800 flex items-center gap-2 px-3 py-1.5 rounded shadow"
                    onClick={handleAddTeamMember}
                  >
                    Add
                  </Button>
                </div>
              </div>
              {/* <div className="flex items-end justify-between">
                  <div >
                    <label className="block text-sm font-medium">
                      Project URL *
                    </label>
                    <input
                      placeholder="Enter Project URL"
                      value={projectUrl}  
                      onChange={(e) => setProjectUrl(e.target.value)}
                      className=" text-sm font-medium w-200 h-9 px-2 border border-gray-300 rounded-md bg-white"
                    />
                  </div>
                <div className="flex items-center gap-4">
                  <Button
                    type="submit"
                    className="bg-blue-700 text-white hover:bg-blue-800 flex items-center gap-2 px-3 py-1.5 rounded shadow"
                  >
                    Add
                  </Button>
                </div>
              </div> */}
              {/* Display users fetch error */}
              {usersFetchError && (
                <div className="flex justify-center items-center gap-2 text-red-600">
                  <p>Error loading users: {usersFetchError}</p>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleRetryFetchUsers}
                  >
                    Retry
                  </Button>
                </div>
              )}
              {/* Display roles fetch error */}
              {rolesFetchError && (
                <div className="flex justify-center items-center gap-2 text-red-600">
                  <p>Error loading roles: {rolesFetchError}</p>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleRetryFetchRoles}
                  >
                    Retry
                  </Button>
                </div>
              )}
              {team.map((member, index) => (
                <div
                  key={index}
                  className="grid grid-cols-[1fr_1fr] gap-6 items-end"
                >
                  <div className="relative">
                    <label className="block text-xs font-medium text-gray-600">
                      Assignee
                    </label>
                    <Select
                      defaultValue={member.assignee}
                      onValueChange={(value: string) => {
                        const updated = [...team];
                        updated[index].assignee = value;
                        setTeam(updated);
                      }}
                      disabled={isLoadingUsers}
                    >
                      <SelectTrigger>
                        <SelectValue
                          placeholder={
                            isLoadingUsers
                              ? "Loading users..."
                              : usersFetchError
                              ? "Error loading users"
                              : "Select assignee"
                          }
                        />
                      </SelectTrigger>
                      <SelectContent>
                        {users.map((user) => (
                          <SelectItem key={user.id} value={user.name}>
                            {user.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-end">
                    <div className="flex-1">
                      <label className="block text-xs font-medium text-gray-600">
                        Role
                      </label>
                      <Select
                        defaultValue={member.role}
                        onValueChange={(value: string) => {
                          const updated = [...team];
                          updated[index].role = value;
                          setTeam(updated);
                        }}
                        disabled={isLoadingRoles}
                      >
                        <SelectTrigger>
                          <SelectValue
                            placeholder={
                              isLoadingRoles
                                ? "Loading roles..."
                                : rolesFetchError
                                ? "Error loading roles"
                                : "Select role"
                            }
                          />
                        </SelectTrigger>
                        <SelectContent>
                          {roles.map((role) => (
                            <SelectItem key={role.id} value={role.name}>
                              {role.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <button
                      type="button"
                      onClick={() => handleRemoveMember(index)}
                      className="ml-2 mb-2"
                    >
                      <img
                        src="/Group 21846.svg"
                        alt="Delete"
                        className="w-4 h-4"
                      />
                    </button>
                  </div>
                </div>
              ))}
            </div>
            <DialogFooter className="pt-3 flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                className="bg-white text-blue-600 border border-blue-600 hover:bg-blue-600 hover:text-white"
                onClick={() => handleCloseDialog(false)}
              >
                Cancel
              </Button>
              <Button className="bg-green-600 text-white hover:bg-green-700">
                Create
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      <Toaster />
    </>
  );
}

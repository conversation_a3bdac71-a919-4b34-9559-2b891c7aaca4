"use client";
import { useEffect, useState } from "react";
import { AppSidebar } from "@/components/dashboard-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { ProjectManagementContent } from "@/components/projectmanagement";
import { setModuleNamesGlobal } from "@/services/testApi";
// import { Button } from "@/components/ui/button";
// import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
// import CreateAPI from "@/components/createAPI";

export default function Page() {
  const [selectedSection, setSelectedSection] = useState("");
  // const [dialogOpen, setDialogOpen] = useState(false);
  const [moduleNames, setModuleNames] = useState([]);
  // const [apiNames, setApiNames] = useState<string[]>([]);
  // const [refreshTestAPIs, setRefreshTestAPIs] = useState(false);

  useEffect(()=>{
    const fetchData = async () => {
      const res = await getAllModuleNames();
      const files = res.files;
      setModuleNames(files);
      setModuleNamesGlobal(files);
      setSelectedSection(files[0]);
    }

    fetchData();
  },[])

  const onModuleChange= (moduleName: string) => {
    setSelectedSection("");
  }

  
  return (
    <SidebarProvider
      style={{ "--sidebar-width": "45px" } as React.CSSProperties}
    >
      <AppSidebar />
      
      <SidebarInset>
        <div className="flex h-screen overflow-hidden">
          {/* Sidebar - fixed and non-scrollable */}
          <aside className="w-[15%] border-r bg-muted p-2 space-y-1 bg-[#F7F9FC] font-normal tracking-normal font-roboto">
            <ul className="text-[#37393A] py-2 flex flex-col gap-2 rounded-xs">
              <div className="bg-[#15537C] text-white px-2 py-1 flex items-center space-x-1 hover:brightness-110 rounded-xs justify-start">
              <span className="font-bold text-sm ml-1 truncate whitespace-nowrap overflow-hidden max-w-[160px]">
                List of Modules
              </span>
            </div>
                {moduleNames.map((module)=>(
                  <li className="text-left w-full text-sm hover:bg-[#B1DBEA]" key={module}>
                  <button
                  className={`w-full px-2 py-1 text-left ${
                    selectedSection === module
                      ? "bg-[#B1DBEA] text-[#15537C] font-medium" 
                      : ""
                  }`}
                  onClick={() => setSelectedSection(module)}
                >
                  {module}
                </button>
                </li>
                ))}
            </ul>
          </aside>

          {/* Main Content - flex column with scrollable content area */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Header - fixed */}
            <header className="bg-background flex items-center justify-between border-b p-2">
              <h1 className="text-left font-medium text-[24px] leading-[20px] tracking-[0px] text-[#2C2E2E]">
                Test Cases
              </h1>
            </header>

            {/* Scrollable Content Area */}
            <div className="flex-1 overflow-auto p-4">
              <ProjectManagementContent
                // key={refreshTestAPIs ? "refresh-1" : "refresh-0"}
                section='APIs'
                name={selectedSection}
              />
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}

async function getAllModuleNames() {
  const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/testapi`);
  return await response.json();
}
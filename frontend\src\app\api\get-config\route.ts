import { NextResponse } from "next/server";
import fs from "fs-extra";
import path from "path";

export async function GET() {
  try {
    const configPath = path.join(process.cwd(), "src", "config", "config.json");
    
    if (await fs.pathExists(configPath)) {
      const configContent = await fs.readFile(configPath, "utf-8");
      const config = JSON.parse(configContent);
      return NextResponse.json(config);
    }
    
    return NextResponse.json({ error: "Config file not found" }, { status: 404 });
  } catch (error) {
    console.error("Error reading config:", error);
    return NextResponse.json({ error: "Failed to read config" }, { status: 500 });
  }
}

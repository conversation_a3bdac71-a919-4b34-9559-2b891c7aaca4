import { recordings, users, type User, type InsertUser, type Recording, type InsertRecording } from "@shared/schema";
import { RecordedAction } from "@shared/types";
import fs from "fs";
import path from "path";

// modify the interface with any CRUD methods
// you might need
export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Recording related methods
  getRecordings(): Promise<Recording[]>;
  getRecording(id: number): Promise<Recording | undefined>;
  createRecording(recording: InsertRecording): Promise<Recording>;
  updateRecording(id: number, data: Partial<Recording>): Promise<Recording | undefined>;
  updateRecordingStatus(id: number, status: string): Promise<Recording | undefined>;
  addRecordingAction(id: number, action: RecordedAction): Promise<Recording | undefined>;
  setRecordingFiles(id: number, jsonPath: string, featurePath: string): Promise<Recording | undefined>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private recordingsMap: Map<number, Recording>;
  private recordingsDir: string;
  currentId: number;
  recordingId: number;
  currenttitle:string;

  constructor() {
    this.users = new Map();
    this.recordingsMap = new Map();
    this.currentId = 1;
    this.recordingId = 1;
    
    // Create recordings directory if it doesn't exist
    this.recordingsDir = path.resolve(process.cwd(), "recordings");
    if (!fs.existsSync(this.recordingsDir)) {
      fs.mkdirSync(this.recordingsDir, { recursive: true });
    }
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  // Recording methods
  async getRecordings(): Promise<Recording[]> {
    return Array.from(this.recordingsMap.values())
      .sort((a, b) => {
        // Sort by creation time, newest first
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });
  }

  async getRecording(id: number): Promise<Recording | undefined> {
    return this.recordingsMap.get(id);
  }

  async createRecording(insertRecording: InsertRecording): Promise<Recording> {
    const id = this.recordingId++;
    const now = new Date();
    
    const recording: Recording = {
      id,
      ...insertRecording,
      status: "recording",
      steps: 0,
      createdAt: now,
      actions: [],
      jsonPath: undefined,
      featurePath: undefined,
    };

    this.recordingsMap.set(id, recording);
    console.log("from storage",recording)
    return recording;
  }

  async updateRecording(id: number, data: Partial<Recording>): Promise<Recording | undefined> {
    const recording = this.recordingsMap.get(id);
    if (!recording) return undefined;

    const updatedRecording = { ...recording, ...data };
    this.recordingsMap.set(id, updatedRecording);
    return updatedRecording;
  }

  async updateRecordingStatus(id: number, status: string): Promise<Recording | undefined> {
    return this.updateRecording(id, { status });
  }


 async updateRecordingTitle(id: number, title: string): Promise<Recording | undefined> {
    return this.updateRecording(id, { title });
  }



  async addRecordingAction(id: number, action: RecordedAction): Promise<Recording | undefined> {
  
    const recording = this.recordingsMap.get(id);
    if (!recording) return undefined;

    const actions = Array.isArray(recording.actions) ? [...recording.actions, action] : [action];
    const steps = actions.length;
   
    const updatedRecording = {
      ...recording,
      actions,
      steps,
    };
    
    this.recordingsMap.set(id, updatedRecording);
    this.currenttitle=action.title;
    return updatedRecording;
  }

  async setRecordingFiles(id: number, jsonPath: string, featurePath: string): Promise<Recording | undefined> {
    return this.updateRecording(id, { jsonPath, featurePath });
  }
}

export const storage = new MemStorage();

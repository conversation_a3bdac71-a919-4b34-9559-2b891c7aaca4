import { pgTable, text, serial, timestamp, integer, jsonb } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { title } from "process";
import { z } from "zod";

// Keep the users table as it might be needed for authentication later
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const recordings = pgTable("recordings", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  url: text("url").notNull(),
  scenario: text("scenario").notNull().default("default"), // default, login, form, etc.
  browser: text("browser").notNull(), // chromium, firefox, webkit
  status: text("status").notNull().default("idle"), // idle, recording, completed
  steps: integer("steps").default(0),
  createdAt: timestamp("created_at").defaultNow(),
  actions: jsonb("actions").default([]),
  jsonPath: text("json_path"),
  featurePath: text("feature_path"),
  title: text("title").notNull().default("")
});

// For inserting new recordings
export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

export const insertRecordingSchema = createInsertSchema(recordings).pick({
  name: true,
  url: true,
  browser: true,
  scenario: true,
});

// For playing back recordings
export const playbackSchema = z.object({
  recordingId: z.number(),
  browser: z.string(),
  headless: z.boolean().default(true),
});

// For starting recording
export const startRecordingSchema = z.object({
  name: z.string().min(1, "Name is required"),
  url: z.string().url("Valid URL is required"),
  browser: z.enum(["chromium", "firefox", "webkit"]),
  scenario: z.string().default("default") // default, login, form, etc.
});

// For stopping recording
export const stopRecordingSchema = z.object({
  recordingId: z.number(),
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
export type Recording = typeof recordings.$inferSelect;
export type InsertRecording = z.infer<typeof insertRecordingSchema>;
export type Playback = z.infer<typeof playbackSchema>;
export type StartRecording = z.infer<typeof startRecordingSchema>;
export type StopRecording = z.infer<typeof stopRecordingSchema>;

const excelService = require('../services/apiTestCaseExcelService');
const path = require('path');

const addDataController = async (req, res) => {
  try {
    const requestData = (req.body);
    console.log("requestData", requestData);

    if (
      !requestData.moduleCategory ||
      typeof requestData.moduleCategory !== "string" ||
      requestData.moduleCategory.trim() === ""
    ) {
      return res.status(400).json({
        status: "error",
        message:
          'Bad Request: moduleCategory field must be provided to determine the file name.',
      });
    }

    const moduleName = requestData.moduleCategory;
    const rowData = { ...requestData };
    delete rowData.moduleCategory;

    if (Object.keys(rowData).length === 0) {
      return res.status(400).json({
        status: "error",
        message:
          'Bad Request: No column data provided (e.g., "Name", "Method Type").',
      });
    }

    await excelService.updateOrCreateExcel(moduleName, rowData);

    res.status(200).json({
      status: "success",
      message: `Data has been added successfully to ${path.basename(
        moduleName
      )}.xlsx!`,
    });
  } catch (error) {
    console.error("Error in addDataController:", error);
    res.status(500).json({
      status: "error",
      message: "Internal server error occurred while adding data.",
      error: error.message
    });
  }
};

const deleteDataController = async (req, res) => {
  try {
    const requestData = (req.body);
    console.log("Delete requestData", requestData);

    if (
      !requestData.moduleCategory ||
      typeof requestData.moduleCategory !== "string" ||
      requestData.moduleCategory.trim() === ""
    ) {
      return res.status(400).json({
        status: "error",
        message:
          'Bad Request: A "moduleCategory" field must be provided to determine the file name.',
      });
    }

    const moduleName = requestData.moduleCategory;

    const deleted = await excelService.deleteRowFromExcel(moduleName, requestData.searchCriteria);
    const searchCriteria = { ...requestData };
    delete searchCriteria.moduleCategory;


    if (deleted) {
      res.status(200).json({
        status: "success",
        message: `Row has been deleted successfully from ${path.basename(
          moduleName
        )}.xlsx!`,
      });
    } else {
      res.status(404).json({
        status: "error",
        message: `No matching row found in ${path.basename(
          moduleName
        )}.xlsx with the provided criteria.`,
      });
    }
  } catch (error) {
    console.error("Error in deleteDataController:", error);
    res.status(500).json({
      status: "error",
      message: "Internal server error occurred while deleting data.",
      error: error.message
    });
  }
};

const updateDataController = async (req, res) => {
  try {
    const requestData = (req.body);
    console.log("Update requestData", requestData);

    if (
      !requestData.moduleCategory ||
      typeof requestData.moduleCategory !== "string" ||
      requestData.moduleCategory.trim() === ""
    ) {
      return res.status(400).json({
        status: "error",
        message:
          'Bad Request: A "moduleCategory" field must be provided to determine the file name.',
      });
    }

    if (
      !requestData.updateData ||
      typeof requestData.updateData !== "object" ||
      Object.keys(requestData.updateData).length === 0
    ) {
      return res.status(400).json({
        status: "error",
        message:
          'Bad Request: An "updateData" object must be provided with the data to update.',
      });
    }

    const moduleName = requestData.moduleCategory;
    const { searchCriteria, updateData } = requestData;

    const updated = await excelService.updateRowInExcel(moduleName, searchCriteria, updateData);

    if (updated) {
      res.status(200).json({
        status: "success",
        message: `Row has been updated successfully in ${path.basename(
          moduleName
        )}.xlsx!`,
      });
    } else {
      res.status(404).json({
        status: "error",
        message: `No matching row found in ${path.basename(
          moduleName
        )}.xlsx with the provided search criteria.`,
      });
    }
  } catch (error) {
    console.error("Error in updateDataController:", error);
    res.status(500).json({
      status: "error",
      message: "Internal server error occurred while updating data.",
      error: error.message
    });
  }
};


const readDataController = async (req, res) => {
  try {
    const { moduleCategory } = req.query;
    console.log("Read moduleCategory", moduleCategory);

    if (
      !moduleCategory ||
      typeof moduleCategory !== "string" ||
      moduleCategory.trim() === ""
    ) {
      return res.status(400).json({
        status: "error",
        message:
          'Bad Request: A "moduleCategory" query parameter must be provided to determine the file name.',
      });
    }

    const rows = await excelService.readExcelRows(moduleCategory);

    res.status(200).json({
      status: "success",
      message: `Data retrieved successfully from ${path.basename(
        moduleCategory
      )}.xlsx!`,
      data: rows,
      count: rows.length
    });
  } catch (error) {
    console.error("Error in readDataController:", error);
    
    if (error.message.includes("does not exist")) {
      return res.status(404).json({
        status: "error",
        message: "File not found.",
        error: error.message
      });
    }

    res.status(500).json({
      status: "error",
      message: "Internal server error occurred while reading data.",
      error: error.message
    });
  }
};

module.exports = {
  addDataController,
  deleteDataController,
  updateDataController,
  readDataController,
};
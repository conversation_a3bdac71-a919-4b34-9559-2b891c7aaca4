"use client";

import { useEffect, useState } from "react";
import { AccordionAPITable } from "@/components/table";
import { Button } from "./ui/button";
import Create<PERSON>I from "@/components/createAPI";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { REQUEST_TYPE_OPTIONS_FILTER, RequestTypeOption } from "@/data/dropdownOptions";

interface ProjectManagementContentProps {
  modulename: string;
  onModuleChange?: (moduleName?: any) => void;
}

export function Testapis({ modulename, onModuleChange }: ProjectManagementContentProps) {
  // const [Testapis, setTestapis] = useState<TestApi[]>([]);
  const [allApis, setAllApis] = useState<TestApi[]>([]);
  const [filteredApis, setFilteredApis] = useState<TestApi[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [dialogOpen, setDialogOpen] = useState(false);
  const [filterType, setFilterType] = useState<string>("all");

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      if (modulename) {
        refreshModuleData();
        const data = await getAllTestApisByModule(modulename);
        const testApiData = convertToApiData(data, modulename);

        setAllApis(testApiData);
        setFilteredApis(testApiData);
        setIsLoading(false);
      }
    };

    fetchData();
  }, [modulename]);

  const refreshModuleData = async () => {
    const data = await getAllTestApisByModule(modulename);
    const testApiData = convertToApiData(data, modulename);
    // setApiNames(testApiData.map((api) => api.name));
    setAllApis(testApiData);
    setFilteredApis(testApiData);
    setIsLoading(false);
  };

  // re-run filtering whenever searchTerm or requestType filter changes
  useEffect(() => {
    let result = allApis;

    if (searchTerm.trim() !== "") {
      result = result.filter((api) =>
        api?.name?.toLowerCase().includes(searchTerm?.toLowerCase())
      );
    }

    if (filterType !== "all") {
      result = result.filter((api) => api?.requestType?.toLowerCase() === filterType.toLowerCase());
    }
    setFilteredApis(result);
  }, [searchTerm, filterType, allApis]);

  const handleDelete = async (api: any) => {
    try {
      const result = await deleteApiTestByIdAndName(api.module, api.id);
      console.log("Deleted successfully:", result);
    } catch (err) {
      console.log("Error", err);
    }  
  };

  const handleDialogClose = (open: boolean) => {
    setDialogOpen(open);
    console.log("in delete")
    if (!open) {
      try {
        refreshModuleData();
      } catch (err) {
        console.log("Error", err);
      }
    }
  };

  if (isLoading) return <div className="p-4">Loading test cases...</div>;

  return (
    <div className="relative">
      {/* search bar + filter + button */}
      <div className="flex items-center justify-between mb-2 space-x-3">
        <input
          type="text"
          placeholder="Search Test Case by name..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e?.target?.value)}
          className="border px-3 py-2 rounded w-full"
        />

                            <Select
                              value={filterType}
                              onValueChange={(value) => setFilterType(value)}
                            >
                              <SelectTrigger className={`w-40 border border-gray-300 rounded px-3 py-2`}>
                                <SelectValue placeholder="Select Request Type" />
                              </SelectTrigger>
                              <SelectContent className="rounded-lg shadow-lg border-gray-200">
                                {REQUEST_TYPE_OPTIONS_FILTER.map((option: RequestTypeOption) => (
                                  <SelectItem key={option.value} value={option.value} className="rounded-md">
                                    {option.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          

        <Button
          className="ml-3 bg-[#15537C] text-white hover:brightness-110 rounded-md h-10 px-4"
          onClick={() => setDialogOpen(true)}
        >
          Create Testcase
        </Button>
        <CreateAPIDialog
          open={dialogOpen}
          // onOpenChange={setDialogOpen}
          onOpenChange={handleDialogClose}
          // onRecordingStop={() => setRefreshTestAPIs(prev => !prev)}
        />
      </div>
      <AccordionAPITable TestAPIs={filteredApis} handleDelete={handleDelete} handleDialogClose={handleDialogClose} />
    </div>
  );

  function CreateAPIDialog({
    open,
    onOpenChange,
  }: {
    open: boolean;
    onOpenChange: (open: boolean) => void;
  }) {
    // Get handleApiCreated from window if passed as a prop, or fallback to noop
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-6xl w-full max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="text-xl font-bold">
              Create Test Case
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto">
            <CreateAPI
              onCancel={() => onOpenChange(false)}
              onApiCreated={onModuleChange}
            />
          </div>
        </DialogContent>
      </Dialog>
    );
  }
}

type BodyType = {
  [key: string]: string | number | boolean | null | undefined;
};

export type TestApi = {
  id?: number;
  name: string;
  url: string;
  requestType: "GET" | "POST" | "PUT" | "PATCH" |  "DELETE";
  environment?: string;
  headerParams?: string;
  queryParams?: string;
  body?: BodyType;
  expectedStatusCode?: string;
  exportVariable?: string;
  responseValidation?: string;
  dependent?: string;
  execute?: string;
  query?: string;
  dbExportVariables?: string;
  collectionName?: string;
  module: string;
};

export type ApiData = {
  id?: number;
  Execute: string;
  SamplerProxyName: string;
  SamplerProxyPath: string;
  SamplerProxyMethod: "GET" | "POST" | "PATCH" | "PUT" | "DELETE";
  Environment?: string;
  HeaderParameter?: string;
  QueryParameter?: string;
  SamplerProxyBody?: BodyType;
  SamplerProxyResponseCode?: number;
  ExportVariable?: string;
  ResponseValidation?: string;
  Dependent?: string;
  Query?: string;
  DBExportVariables?: string;
  CollectionName?: string;
};

async function getAllTestApisByModule(modulename: string): Promise<ApiData[]> {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/testapi/${modulename}`
  );
  const jsonData: { file: string; data: ApiData[] } = await response.json();
  return jsonData.data;
}

async function deleteApiTestByIdAndName(
  moduleName: string,
  id: number
) {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/excel/delete`, 
      {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ moduleCategory: moduleName, searchCriteria: id }),
      }
    );

    if (!response.ok) {
      throw new Error(
        `Failed to delete record. Status: ${response.status} ${response.statusText}`
      );
    }

    const jsonData = await response.json();
    return jsonData.data;
  } catch (error) {
    console.error("Error deleting API test:", error);
  }
}

function convertToApiData(data: ApiData[], modulename: string): TestApi[] {
  const allApis: TestApi[] = data.map((item, index) => ({
    id: item.id,
    name: item.SamplerProxyName,
    url: item.SamplerProxyPath,
    requestType: item.SamplerProxyMethod,
    environment: item.Environment,
    headerParams: item.HeaderParameter,
    queryParams: item.QueryParameter,
    body: item.SamplerProxyBody,
    expectedStatusCode: item.SamplerProxyResponseCode?.toString(),
    exportVariable: item.ExportVariable,
    responseValidation: item.ResponseValidation,
    dependent: item.Dependent,
    execute: item.Execute,
    query: item.Query,
    dbExportVariables: item.DBExportVariables,
    collectionName: item.CollectionName,
    module: modulename,
  }));
  return allApis;
}

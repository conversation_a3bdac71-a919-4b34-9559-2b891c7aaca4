'use client';
import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { TestApi } from './testapis';
import CreateAPI from './createAPI';

interface APIDialogProps {
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  apiData?: TestApi | null;
}

const ViewAPIDialog: React.FC<APIDialogProps> = ({ 
  isOpen: externalIsOpen, 
  onOpenChange: externalOnOpenChange,
  apiData, // Added testCaseName prop
}) => {
  const [internalIsOpen, setInternalIsOpen] = useState(false);
  
  const isOpen = externalIsOpen !== undefined ? externalIsOpen : internalIsOpen;
  const setIsOpen = externalOnOpenChange || setInternalIsOpen;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      {/* {externalIsOpen === undefined && (
        <DialogTrigger asChild>
          <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
            Recorded
          </button>
        </DialogTrigger>
      )} */}
      
      <DialogContent className="max-w-6xl w-full max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="text-xl font-bold">
            {apiData?.name || "View API"}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          <CreateAPI
            apiData={apiData}
            isReadOnly={true}
            onCancel={() => {
              setIsOpen(false)}}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default ViewAPIDialog;
Feature: changeswithpause
  Sc<PERSON>rio: test app on scenario
    Given I am on the home page
    Then I load the "elementProperties" module "Index" locators
    Then I click on "div-1754663150732_//div[contains(@_xpath" on "Index" page
    Then I click on "emailidforsignup_id_xpath" on "Index" page
    Then I send "emailidforsignup_id_xpath" as a "234321" in to "Index" page
    Then I click on "img-1754663152921_id_xpath" on "Index" page
    ---> Then I load the "elementProperties" module "Register" locators
    Then I click on "firstname_placeholder_xpath" on "Register" page
    Then I send "firstname_placeholder_xpath" as a "1234312" in to "Register" page
    Then I click on "lastname_placeholder_xpath" on "Register" page
    Then I send "lastname_placeholder_xpath" as a "12344231" in to "Register" page
    Then I click on "address_//textarea[contains(@_xpath" on "Register" page
    Then I send "address_//textarea[contains(@_xpath" as a "2134321" in to "Register" page
    Then I click on "input-1754663156934_type_xpath" on "Register" page
    Then I verify "div-1754663158991_//div[contains(@_xpath" has data "(adsbygoogle = window.adsbygoogle || []).push({}); Full Name* Address Email address* Provide a valid email id for further updates Phone* Gender* Male FeMale Hobbies Cricket Movies Hockey Languages Ara" on "Register" page
    Then I send "input-1754663159203_type_xpath" as a "123432" in to "Register" page

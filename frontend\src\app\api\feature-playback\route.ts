import { NextRequest, NextResponse } from "next/server";
import { exec } from "child_process";
import { promisify } from "util";
import { promises as fs } from "fs";
import path from "path";

const execAsync = promisify(exec);

// Helper function to get project directories
async function getProjectDirectories(mode:string): Promise<{featuresDirectory: string, projectDirectory: string}> {
  const configPath = path.join(process.cwd(), 'src', 'config', 'config.json');
  console.log(`Reading config from: ${configPath}`);
  const configContent = await fs.readFile(configPath, 'utf8');
  const config = JSON.parse(configContent);
  if (!config.currentProjectPath) {
    throw new Error('Config.json is missing currentProjectPath. Please create a project first.');
  }
  
  const projectDirectory = config.currentProjectPath;

  const featuresDirectory = path.join(projectDirectory,mode, 'src', 'test', 'resources', 'features');
  

  return { featuresDirectory, projectDirectory };
}

export async function POST(request: NextRequest) {
  try {
    const { featureFileName, browser,mode } = await request.json();

    if (!featureFileName) {
      return NextResponse.json(
        { error: "Feature file name is required" },
        { status: 400 }
      );
    }



    console.log(`Starting feature file execution: ${featureFileName}`);

    
    // Get both project and features directories from config
    const { featuresDirectory, projectDirectory } = await getProjectDirectories(mode);
    console.log(`Using project directory: ${projectDirectory}`);
    console.log(`Using features directory: ${featuresDirectory}`);

    // Build the command with full path to featurerun.bat and relative feature path

    const batchFilePath = path.join(projectDirectory,mode,'featurerun.bat');
 
    const relativeFeaturePath = `src/test/resources/features/${featureFileName}`;
    
    // Check if batch file exists
    console.log(`Checking if batch file exists: ${batchFilePath}`);
    try {
      await fs.access(batchFilePath);
      console.log(` Batch file found: ${batchFilePath}`);
    } catch (error) {
      console.log(` Batch file NOT found: ${batchFilePath}`);
      return NextResponse.json(
        { error: `Batch file not found at: ${batchFilePath}` },
        { status: 500 }
      );
    }
    
    const cmdCommand = `"${batchFilePath}" "${relativeFeaturePath}"`;
    console.log(`Executing command: ${cmdCommand}`);
    console.log(`Working directory: ${projectDirectory}`);

    try {
      // Try alternative command execution approach
      console.log(`Attempting to execute: ${cmdCommand}`);
      
      const { stdout, stderr } = await execAsync(cmdCommand, {
        cwd: projectDirectory, // Execute from project directory (where pom.xml is)
        shell: "cmd.exe" // Explicitly use shell
      });

      console.log("Feature execution completed successfully");
      console.log("stderr:", stderr);
      
      return NextResponse.json({
        success: true,
        message: `Feature file "${featureFileName}" executed successfully`,
        details: {
          command: cmdCommand,
          featureFile: featureFileName,
          relativeFeaturePath: relativeFeaturePath,
          projectDirectory: projectDirectory,
          workingDirectory: projectDirectory,
          batchFilePath: batchFilePath,
          stdout: stdout,
          stderr: stderr,
        },
      });
    } catch (error) {
      console.error("Feature execution completed with errors:", error);

      return NextResponse.json({
        success: true, // Still success as command executed
        message: `Feature file "${featureFileName}" command executed`,
        details: {
          command: cmdCommand,
          featureFile: featureFileName,
          relativeFeaturePath: relativeFeaturePath,
          projectDirectory: projectDirectory,
          workingDirectory: projectDirectory,
          batchFilePath: batchFilePath,
          note: "Maven executed but encountered issues",
          error: error && typeof error === "object" && "message" in error
            ? (error as { message: string }).message
            : String(error),
        },
      });
    }
  } catch (error) {
    console.error("Error running feature file:", error);
    return NextResponse.json(
      { error: "Failed to run feature file" },
      { status: 500 }
    );
  }
}
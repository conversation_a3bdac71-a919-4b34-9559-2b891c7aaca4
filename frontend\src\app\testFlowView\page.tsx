"use client";
import APITestFlow from "@/components/apiTestFlow";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, Ta<PERSON>Content } from "@/components/ui/tabs";
import WebTestFlow from "@/components/webTtestFlow";
import { useState } from "react";


export default function Page() {
  const [tab, setTab] = useState("home");
  const getDisplayTitle = () => {
    return "Test Flows";
  };
  return (
    <div>
      <header className="bg-background flex items-center justify-between border-b p-2 mx-[60px]">
        <h1 className="text-left font-medium text-[24px] leading-[20px] tracking-[0px] text-[#2C2E2E]">
          {getDisplayTitle()}
        </h1>
      </header>
      <Tabs value={tab} onValueChange={setTab} className="w-full">
        <div className="bg-[#15537c] pt-2">
          <div className="w-full max-w-6xl mx-[60px]">
            <TabsList className="flex h-auto items-end justify-start p-0 bg-transparent rounded-none">
              <TabsTrigger
                value="home"
                className="inline-flex items-center justify-center whitespace-nowrap px-6 py-2 text-base font-medium transition-all duration-200
                           bg-transparent text-white data-[state=active]:bg-white data-[state=active]:text-gray-800
                           data-[state=active]:shadow-none rounded-t-md rounded-b-none focus-visible:ring-0 focus-visible:ring-offset-0 focus:outline-none -mb-[1px]" // -mb-[1px] for overlap
              >
                Web
              </TabsTrigger>
              <TabsTrigger
                value="forms"
                className="inline-flex items-center justify-center whitespace-nowrap px-6 py-2 text-base font-medium transition-all duration-200
                           bg-transparent text-white data-[state=active]:bg-white data-[state=active]:text-gray-800
                           data-[state=active]:shadow-none rounded-t-md rounded-b-none focus-visible:ring-0 focus-visible:ring-offset-0 focus:outline-none -mb-[1px]"
              >
                API
              </TabsTrigger>
            </TabsList>
          </div>
        </div>

        <div className="bg-white border-t border-gray-200 w-full mt-[0] ">
          <TabsContent value="home" className="mt-[0]">
            <div>
              <WebTestFlow />
            </div>
          </TabsContent>
          <TabsContent value="forms" className="mt-[0]">
            <div>
              <APITestFlow />
            </div>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}

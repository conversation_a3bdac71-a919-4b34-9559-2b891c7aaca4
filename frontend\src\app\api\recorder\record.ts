// lib/api.ts
import api from "@/app/services/api";

export async function startRecording({
  name,
  browser,
  url,
  scenario,
  sessionId,
}: {
  name: string;
  url: string;
  browser: string;
  scenario: string; // Optional - for feature files
  sessionId?: number; // Optional - for resuming paused sessions
}) {

  const body = sessionId
    ? { sessionId, name, browser, url, scenario }
    : { name, browser, url, scenario };

    try{
  const response = await api.post(`/start-recording`, 
   
    body
  ,
{
   baseURL:"http://localhost:5000/api"
});

console.log('RESPONSE',response);
 return  response.data;

    }
    catch(error){
      console.log(`Failed to ${sessionId ? "resume" : "start"} recording`);
    }


  // { sessionId, status, startTime }
}

export async function stopRecording(recordingId: number) {

   try{
  const response = await api.post(`/stop-recording`, {
   
   recordingId 
  },
{
   baseURL:"http://localhost:5000/api"
});

 return  response.data();

    }
    catch(error){
      console.log(`Failed to stop recording : ${error}`);
    }



}

export async function pauseRecording(sessionId: number) {
  const response = await fetch("http://localhost:5000/api/pause-recording", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ sessionId }),
  });

  if (!response.ok) {
    throw new Error("Failed to pause recording");
  }

  return await response.json(); // { sessionId, status, pausedAt }
}



// NEW playback method (Feature files) - Maven approach
import axios from 'axios';

export async function playFeatureFile(
  featureFileName: string,
  browser: string,
  mode: string
) {
  try {
    const response = await axios.post('/api/feature-playback', {
      featureFileName,
      browser,
      mode,
    });

    return response.data;
  } catch (error) {
    console.error('Failed to start feature playback:', error);
    throw error;
  }
}


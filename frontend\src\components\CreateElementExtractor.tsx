"use client";
import React, { useState, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import axios from 'axios';

interface CreateElementExtractorProps {
  onCancel?: () => void;
  onSuccess?: () => void;
}

const CreateElementExtractor: React.FC<CreateElementExtractorProps> = ({
  onCancel,
  onSuccess,
}) => {
  const [extractorName, setExtractorName] = useState("");
  const [gherkinContent, setGherkinContent] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Store extractor names in localStorage to ensure uniqueness
  const getSavedExtractors = (): string[] => {
    const stored = localStorage.getItem("element_extractor_names");
    return stored ? JSON.parse(stored) : [];
  };

  const saveExtractorName = (name: string) => {
    const saved = getSavedExtractors();
    saved.push(name);
    localStorage.setItem("element_extractor_names", JSON.stringify(saved));
  };

  const validateGherkin = (gherkin: string): string | null => {
    const lines = gherkin.split("\n");
    const validSteps = ["Given", "When", "Then", "And", "But"];

    let hasFeature = false;
    let hasScenario = false;

    for (let line of lines) {
      line = line.trim();
      if (line.startsWith("Feature:")) hasFeature = true;
      if (line.startsWith("Scenario:")) hasScenario = true;

      // Basic step validation
      if (
        line &&
        !line.startsWith("Feature:") &&
        !line.startsWith("Scenario:") &&
        !line.startsWith("@") && // Allow tags
        !validSteps.some((step) => line.startsWith(step)) &&
        !line.startsWith("#") // Allow comments
      ) {
        return `Invalid Gherkin step: "${line}"`;
      }
    }

    if (!hasFeature) return "Missing 'Feature:' declaration";
    if (!hasScenario) return "Missing 'Scenario:' declaration";

    return null; // valid
  };

  const prettifyGherkin = (gherkin: string): string => {
    return gherkin
      .split("\n")
      .map((line) => {
        let trimmed = line.trim();
        if (trimmed.startsWith("@")) return trimmed; // Keep tags at root level
        if (trimmed.startsWith("Feature:")) return trimmed;
        if (trimmed.startsWith("Scenario:")) return `  ${trimmed}`;
        if (trimmed.startsWith("#")) return `    ${trimmed}`; // Indent comments
        if (
          trimmed &&
          !trimmed.startsWith("Feature:") &&
          !trimmed.startsWith("Scenario:")
        ) {
          return `    ${trimmed}`; // Indent steps
        }
        return trimmed;
      })
      .join("\n");
  };

  // Check if form is valid
  const isFormValid = useMemo(() => {
    const trimmedName = extractorName.trim().replace(/\s+/g, "_");
    if (!trimmedName || !gherkinContent.trim()) return false;
    if (getSavedExtractors().includes(trimmedName)) return false;
    if (validateGherkin(gherkinContent) !== null) return false;
    return true;
  }, [extractorName, gherkinContent]);

  const handleSave = async () => {
    setIsLoading(true);

    const trimmedName = extractorName.trim().replace(/\s+/g, "_");

    if (!trimmedName || !gherkinContent.trim()) {
      toast({
        title: "Missing fields",
        description: "Both extractor name and Gherkin content are required.",
        variant: "destructive",
      });
      setIsLoading(false);
      return;
    }

    const existing = getSavedExtractors();
    if (existing.includes(trimmedName)) {
      toast({
        title: "Duplicate name",
        description: "An element extractor with this name already exists.",
        variant: "destructive",
      });
      setIsLoading(false);
      return;
    }

    const validationError = validateGherkin(gherkinContent);
    if (validationError) {
      toast({
        title: "Invalid Gherkin",
        description: validationError,
        variant: "destructive",
      });
      setIsLoading(false);
      return;
    }

    const prettyContent = prettifyGherkin(gherkinContent);


try {
  const response = await axios.post('/api/element-extractor', {
    fileName: trimmedName,
    gherkinContent: prettyContent,
  });

  const data = response.data;

  if (data.success) {
    saveExtractorName(trimmedName);
    toast({
      title: "Created Successfully",
      description: `Element extractor '${trimmedName}' has been created and saved.`,
      variant: "default",
    });

    // Reset form
    setExtractorName("");
    setGherkinContent("");

    // Close dialog and trigger refresh
    onCancel?.();
    onSuccess?.();
  } else {
    toast({
      title: "Save failed",
      description: data.error || "Failed to save extractor.",
      variant: "destructive",
    });
  }
} catch (error: any) {
  toast({
    title: "Network error",
    description:
      error.response?.data?.error ||
      error.message ||
      "Failed to connect to server.",
    variant: "destructive",
  });
} finally {
  setIsLoading(false);
}

  };

  return (
    <div className="w-full text-sm text-gray-800 space-y-7">
      <div className="flex items-center gap-2">
        <span className="font-medium text-gray-700 whitespace-nowrap">
          Element Extractor Name:
        </span>
        <input
          type="text"
          placeholder="Enter unique element extractor name"
          value={extractorName}
          onChange={(e) => setExtractorName(e.target.value)}
          className="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900"
          disabled={isLoading}
        />
      </div>

      <div className="relative w-full">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Element Extractor Definition (Gherkin Format)
        </label>
        <textarea
          placeholder={`Feature: capture webelements


  Scenario: capture current page elements
    Given I Navigate to the page "https://www.example.com/"
    And capture current page web element and generate properties file as "Home"
    Then I copy UI element properties files from extractor to modules
"`}
          value={gherkinContent}
          onChange={(e) => setGherkinContent(e.target.value)}
          className="w-full h-64 font-mono text-sm bg-transparent border border-gray-300 rounded-md p-4 resize-none overflow-auto relative z-10"
          style={{ color: "black", position: "relative" }}
          disabled={isLoading}
        />
      </div>

      <div className="flex justify-end gap-3">
        <button
          onClick={() => {
            setExtractorName("");
            setGherkinContent("");
            onCancel?.();
          }}
          className="flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200 text-gray-700 font-medium shadow-sm"
          disabled={isLoading}
        >
          Cancel
        </button>
        <Button
          onClick={handleSave}
          disabled={!isFormValid || isLoading}
          className={`bg-green-600 text-white hover:bg-green-700 ${
            !isFormValid || isLoading
              ? "opacity-50 cursor-not-allowed hover:bg-green-600"
              : ""
          }`}
        >
          {isLoading ? "Creating..." : "Create Element Extractor"}
        </Button>
      </div>
    </div>
  );
};

export default CreateElementExtractor;

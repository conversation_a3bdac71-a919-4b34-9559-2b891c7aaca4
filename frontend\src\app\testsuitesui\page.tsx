"use client";

import { AppSidebar } from "@/components/dashboard-sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import TestSuiteCreatorDialog from "@/components/TestSuite";
import { AccordionTable } from "@/components/table";
import { useState, useEffect, useMemo } from "react";
import { Edit } from "lucide-react";
import TestFlowUICreatorDialog from "@/components/TestFlowUI";
import { setModuleNamesGlobal } from "@/services/testApi";

interface TestFlow {
  name: string;
  description: string;
  testcases: string[];
}

interface TestCase {
  id: number;
  name: string;
  filePath: string;
  data: any;
}

// Fetch test flows
// async function getTestFlowsFromAPI(): Promise<TestFlow[]> {
//   try {
//     const response = await fetch("/api/test-flows");
//     if (!response.ok) throw new Error("Failed to fetch test flows");
//     return response.json();
//   } catch (error) {
//     console.error(error);
//     return [];
//   }
// }

// Fetch test cases (all at once)
// async function getTestCasesFromAPI(): Promise<TestCase[]> {
//   try {
//     const response = await fetch("/api/test-cases");
//     if (!response.ok) throw new Error("Failed to fetch test cases");
//     return response.json();
//   } catch (error) {
//     console.error(error);
//     return [];
//   }
// }

// function TestFlowContent({
//   testFlowName,
//   testFlows,
//   allTestCases,
// }: {
//   testFlowName: string | null;
//   testFlows: TestFlow[];
//   allTestCases: TestCase[];
// }) {
//   // Filter test cases only once using memo
//   const filteredTestCases = useMemo(() => {
//     if (!testFlowName) return [];
//     const testFlow = testFlows.find((flow) => flow.name === testFlowName);
//     if (!testFlow) return [];
//     return allTestCases.filter((tc) => testFlow.testcases.includes(tc.name));
//   }, [testFlowName, testFlows, allTestCases]);

//   if (!testFlowName) {
//     return (
//       <div className="p-8 text-center text-gray-500">
//         <div className="text-gray-400 mb-4">
//           <svg
//             className="w-16 h-16 mx-auto mb-4"
//             fill="none"
//             stroke="currentColor"
//             viewBox="0 0 24 24"
//           >
//             <path
//               strokeLinecap="round"
//               strokeLinejoin="round"
//               strokeWidth={1.5}
//               d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
//             />
//           </svg>
//           <p className="text-lg font-medium text-gray-600">
//             Select a test flow to view test cases
//           </p>
//           <p className="text-sm text-gray-500">
//             Choose a test flow from the sidebar to see its associated test cases
//           </p>
//         </div>
//       </div>
//     );
//   }

//   if (filteredTestCases.length === 0) {
//     return (
//       <div className="p-8 text-center">
//         <div className="text-gray-500 mb-4">
//           <svg
//             className="w-12 h-12 mx-auto mb-2"
//             fill="none"
//             stroke="currentColor"
//             viewBox="0 0 24 24"
//           >
//             <path
//               strokeLinecap="round"
//               strokeLinejoin="round"
//               strokeWidth={2}
//               d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
//             />
//           </svg>
//           <p className="text-lg font-medium">No test cases found</p>
//           <p className="text-sm">
//             This test flow doesn't have any test cases yet.
//           </p>
//         </div>
//       </div>
//     );
//   }

//   return (
//     <div className="relative">
//       <AccordionTable
//         testCases={filteredTestCases}
//         showActionButtons={true}
//         showCheckboxes={true}
//         allowEditing={true}
//         showCucumberButton={false}
//         showPlayButton={false}
//       />
//     </div>
//   );
// }

export default function Page() {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [editingTestFlow, setEditingTestFlow] = useState<TestFlow | null>(null);
  const [testFlows, setTestFlows] = useState<TestFlow[]>([]);
  const [allTestCases, setAllTestCases] = useState<TestCase[]>([]);
  const [selectedTestFlow, setSelectedTestFlow] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [testFlowNames, setTestFlowNames] = useState<string[]>([]);
  const [moduleNames, setModuleNames] = useState([]);

  // useEffect(() => {
  //   // Fetch both concurrently
  //   async function fetchData() {
  //     setLoading(true);
  //     setError(null);
  //     try {
  //       const [flows, testCases] = await Promise.all([
  //         getTestFlowsFromAPI(),
  //         getTestCasesFromAPI(),
  //       ]);
  //       setTestFlows(flows);
  //       setAllTestCases(testCases);

  //       // Set default test flow
  //       if (!selectedTestFlow) {
  //         const defaultFlow = flows.find((flow) => flow.name === "testflow1");
  //         setSelectedTestFlow(
  //           defaultFlow ? "testflow1" : flows.length > 0 ? flows[0].name : null
  //         );
  //       }
  //     } catch (err) {
  //       console.error(err);
  //       setError("Failed to load test flows or test cases");
  //     } finally {
  //       setLoading(false);
  //     }
  //   }
  //   fetchData();
  // }, []);

    useEffect(()=>{
    const fetchData = async () => {
      try{
      const res = await getAllTestFlowNames(); 
      const files = res.files;
      setTestFlowNames(files);
      setSelectedTestFlow(files[0]);
      getAndSetAllModuleNames();
      }      
      catch (err) {
        console.error(err);
        setError("Failed to load test flows or test cases");
      } finally {
        setLoading(false);
      }

    }

    fetchData();
  },[])

  async function getAndSetAllModuleNames(){
    const res = await getAllModuleNames(); 
    const files = res.files;
    setModuleNames(files);
    setModuleNamesGlobal(files);
  }

  const handleDialogChange = async (open: boolean) => {
    setDialogOpen(open);
    getAndSetAllModuleNames();

    if (!open) {
      // Reset edit mode when closing
      setEditMode(false);
      setEditingTestFlow(null);

      // Refresh test flows and test cases after dialog closes
      setLoading(true);
      setError(null);
      // try {
      //   const [flows, testCases] = await Promise.all([
      //     getTestFlowsFromAPI(),
      //     getTestCasesFromAPI(),
      //   ]);
      //   setTestFlows(flows);
      //   setAllTestCases(testCases);
      // } catch (err) {
      //   console.error(err);
      //   setError("Failed to refresh test flows or test cases");
      // } finally {
      //   setLoading(false);
      // }
      try{
      const res = await getAllTestFlowNames(); 
      const files = res.files;
      setTestFlowNames(files);
      setSelectedTestFlow(files[0]);
      }      
      catch (err) {
        console.error(err);
        setError("Failed to load test flows or test cases");
      } finally {
        setLoading(false);
      }
    }
  };

  // Handle edit button click
  const handleEditTestFlow = () => {
    if (selectedTestFlow) {
      const flowToEdit = testFlows.find(
        (flow) => flow.name === selectedTestFlow
      );
      if (flowToEdit) {
        setEditingTestFlow(flowToEdit);
        setEditMode(true);
        setDialogOpen(true);
      }
    }
  };

  if (loading) {
    return (
      <SidebarProvider
        style={{ "--sidebar-width": "45px" } as React.CSSProperties}
      >
        <AppSidebar />
        <SidebarInset>
          <div className="flex h-screen items-center justify-center">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#15537C]"></div>
              <span>Loading test flows and test cases...</span>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  if (error) {
    return (
      <SidebarProvider
        style={{ "--sidebar-width": "45px" } as React.CSSProperties}
      >
        <AppSidebar />
        <SidebarInset>
          <div className="flex h-screen items-center justify-center">
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600 font-medium">Error loading data</p>
              <p className="text-red-500 text-sm">{error}</p>
              <Button
                onClick={() => window.location.reload()}
                className="mt-2 text-sm"
                variant="outline"
              >
                Retry
              </Button>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider
      style={{ "--sidebar-width": "45px" } as React.CSSProperties}
    >
      <AppSidebar />
      <SidebarInset>
        <div className="flex h-screen overflow-hidden">
          <aside className="w-[15%] border-r bg-muted p-2 space-y-1 bg-[#F7F9FC] font-normal tracking-normal font-roboto">
            <div className="bg-[#15537C] text-white px-2 py-1 flex items-center space-x-1 hover:brightness-110 rounded-xs justify-start">
              <span className="font-bold text-sm ml-1 truncate whitespace-nowrap overflow-hidden max-w-[160px]">
                Test Flows
              </span>
            </div>
            <ul className="text-[#37393A] py-2 flex flex-col gap-2 rounded-xs">
              {testFlowNames.map((flowName) => (
                <li
                  key={flowName}
                  className="text-left w-full text-sm hover:bg-[#B1DBEA]"
                >
                  <button
                    className={`w-full px-2 py-1 text-left ${
                      selectedTestFlow === flowName
                        ? "bg-[#B1DBEA] text-[#15537C] font-medium"
                        : ""
                    }`}
                    onClick={() => setSelectedTestFlow(flowName)}
                    // title={flow.description}
                  >
                    <div className="truncate">{flowName}</div>
                  </button>
                </li>
              ))}
            </ul>
          </aside>

          <div className="flex-1 flex flex-col overflow-hidden">
            <header className="bg-background flex items-center justify-between border-b p-2">
              <div className="flex items-center space-x-3">
                <h1 className="text-left font-medium text-[24px] leading-[20px] tracking-[0px] text-[#2C2E2E]">
                  {selectedTestFlow
                    ? `Test Cases - ${selectedTestFlow}`
                    : "Test Flows"}
                </h1>
                {/* {selectedTestFlow && (
                  <button
                    onClick={handleEditTestFlow}
                    className="p-2 hover:bg-gray-200 rounded transition-colors"
                    title={`Edit ${selectedTestFlow}`}
                  >
                    <Edit className="h-4 w-4 text-gray-600" />
                  </button>
                )} */}
              </div>
              <Button
                className="bg-[#15537C] text-white hover:brightness-110 rounded-md w-34 h-6"
                onClick={() => {
                  setEditMode(false);
                  setEditingTestFlow(null);
                  setDialogOpen(true);
                }}
              >
                Create Test Flow
              </Button>
            </header>
            <div className="flex-1 overflow-auto p-4">
              {/* <TestFlowContent
                testFlowName={selectedTestFlow}
                testFlows={testFlows}
                allTestCases={allTestCases}
              /> */}
            </div>
          </div>
        </div>
      </SidebarInset>
      <TestSuiteUICreatorDialog
        open={dialogOpen}
        onOpenChange={handleDialogChange}
        editMode={editMode}
        editingTestFlow={editingTestFlow}
        moduleNames={moduleNames}
      />
    </SidebarProvider>
  );
}

async function getAllTestFlowNames() {
  const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/testflowui`);
  return await response.json();
}

async function getAllModuleNames() {
  const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/testflowui`);
  return await response.json();
}

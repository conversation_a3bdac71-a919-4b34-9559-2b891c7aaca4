{"name": "antp-ui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.4", "adm-zip": "^0.5.16", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "fs-extra": "^11.3.0", "glob": "^11.0.3", "lucide-react": "^0.503.0", "next": "15.3.1", "pnpm": "^10.13.1", "properties-reader": "^2.3.0", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^2.15.3", "tailwind-merge": "^3.2.0", "xml2js": "^0.6.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/adm-zip": "^0.5.7", "@types/fs-extra": "^11.0.4", "@types/glob": "^9.0.0", "@types/next": "^8.0.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/xml2js": "^0.4.14", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "tw-animate-css": "^1.2.8", "typescript": "^5"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}
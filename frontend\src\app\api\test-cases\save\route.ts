import { promises as fs } from "fs";
import { NextResponse } from "next/server";
import config from "@/config/config.json";
const RECORDINGS_PATH = config.recordingsPath;

// NEW: Extract default module from existing feature file
function extractDefaultModule(featureContent: string): string | null {
  // Look for: Then I load the "elementProperties" module "" locators
  const modulePattern =
    /Then I load the "elementProperties" module "([^"]*)" locators/;
  const match = featureContent.match(modulePattern);

  return match ? match[1] : null; 
}

// NEW: Smart module management - detect which modules are needed
function analyzeModuleUsage(
  steps: any[],
  defaultModule: string | null
): {
  usedModules: Set<string>;
  moduleInsertions: Array<{ afterStepIndex: number; module: string }>;
} {
  const usedModules = new Set<string>();
  const moduleInsertions: Array<{ afterStepIndex: number; module: string }> =
    [];

  // Add default module to used modules if it exists
  if (defaultModule) {
    usedModules.add(defaultModule);
  }

  steps.forEach((step, index) => {
    const stepModule = step.elementModule;

    // If step uses a different module than default and we haven't loaded it yet
    if (
      stepModule &&
      stepModule !== defaultModule &&
      !usedModules.has(stepModule)
    ) {
      usedModules.add(stepModule);
      moduleInsertions.push({
        afterStepIndex: index,
        module: stepModule,
      });
    }
  });

  return { usedModules, moduleInsertions };
}

// NEW: Remove unused module load statements
function removeUnusedModules(
  featureLines: string[],
  usedModules: Set<string>,
  defaultModule: string | null
): string[] {
  return featureLines.filter((line) => {
    const moduleMatch = line.match(
      /Then I load the "elementProperties" module "([^"]*)" locators/
    );
    if (moduleMatch) {
      const moduleInLine = moduleMatch[1];
      // Keep default module and currently used modules
      return moduleInLine === defaultModule || usedModules.has(moduleInLine);
    }
    return true; // Keep non-module lines
  });
}

// UPDATED: Convert single step to Gherkin format with module insertion support
function convertStepToGherkin(
  step: any,
  scenarioUrl: string,
  insertModuleAfter?: string
): string {
  let gherkinStep = "";

  // First, add the actual step
  switch (step.action) {
    case "click":
      if (step.label || step.selector) {
        const label = step.label || step.selector;
        gherkinStep = `    Then I click on "${label}" on "${scenarioUrl}" page\n`;
      }
      break;

    case "type":
      if ((step.label || step.selector) && step.value) {
        const label = step.label || step.selector;
        gherkinStep = `    Then I send "${label}" as a "${step.value}" in to "${scenarioUrl}" page\n`;
      }
      break;

    case "navigate":
      if (step.url) {
        gherkinStep = `    Then I should be navigated to "${step.url}"\n`;
      }
      break;

    case "assertion":
      if ((step.label || step.selector) && step.value) {
        const label = step.label || step.selector;
        const pageUrl = step.url || scenarioUrl; // Use step URL if available, otherwise scenario URL
        gherkinStep = `    Then I verify "${label}" has data "${step.value}" on "${pageUrl}" page\n`;
      }
      break;

    case "select":
      if ((step.label || step.selector) && step.value) {
        const label = step.label || step.selector;
        gherkinStep = `    Then I select "${label}" with data "${step.value}" on "${scenarioUrl}" page\n`;
      }
      break;
  }

  // NEW: Add module load statement above the step if needed
  if (insertModuleAfter && gherkinStep) {
    gherkinStep += `    Then I load the "elementProperties" module "${insertModuleAfter}" locators\n`;
  }

  return gherkinStep;
}

// Convert steps to Gherkin step format
function convertStepsToGherkin(steps: any[], scenarioUrl: string): string {
  let gherkinSteps = "";

  steps.forEach((step) => {
    gherkinSteps += convertStepToGherkin(step, scenarioUrl);
  });

  return gherkinSteps;
}

// NEW: Enhanced convert steps with smart module management
function convertStepsToGherkinWithModules(
  steps: any[],
  scenarioUrl: string,
  defaultModule: string | null
): string {
  let gherkinSteps = "";

  // Analyze module usage
  const { usedModules, moduleInsertions } = analyzeModuleUsage(
    steps,
    defaultModule
  );

  steps.forEach((step, index) => {
    // Check if we need to insert a module load after this step
    const moduleToInsert = moduleInsertions.find(
      (insertion) => insertion.afterStepIndex === index
    );

    gherkinSteps += convertStepToGherkin(
      step,
      scenarioUrl,
      moduleToInsert ? moduleToInsert.module : undefined
    );
  });

  return gherkinSteps;
}

// Get the scenario URL from existing feature file content
function extractScenarioUrl(featureContent: string): string {
  const stepUrlMatch = featureContent.match(/on "([^"]*)" page/);
  if (stepUrlMatch && stepUrlMatch[1] !== "https://example.com") {
    return stepUrlMatch[1].trim();
  }

  const scenarioMatch = featureContent.match(
    /Scenario: User performs actions on (.+)/
  );
  if (scenarioMatch) {
    return scenarioMatch[1].trim();
  }

  // Try to find it in the Given statement instead
  const givenMatch = featureContent.match(/Given I open the browser at "(.+)"/);
  if (givenMatch) {
    return givenMatch[1].trim();
  }

  // No fallback - return empty string if no URL found
  return "";
}

// Main handler for saving test case data (Feature files only)
export async function POST(request: Request) {
  try {
    const { fileName, newSteps, featureChanges } = await request.json();

    // Basic input validation
    if (!fileName || !fileName.trim()) {
      return NextResponse.json(
        { error: "File name is required" },
        { status: 400 }
      );
    }

    // Determine what steps to append to feature file
    let stepsToAppendToFeature = [];
    if (newSteps && Array.isArray(newSteps)) {
      stepsToAppendToFeature = newSteps;
    }

    const featureFilePath = `${RECORDINGS_PATH}/${fileName}.feature`;

    try {
      // Smart feature file updates
      if (
        featureChanges &&
        Array.isArray(featureChanges) &&
        featureChanges.length > 0
      ) {
        try {
          // Read current feature file
          const existingFeatureContent = await fs.readFile(
            featureFilePath,
            "utf-8"
          );

          // NEW: Extract default module from existing feature file
          const defaultModule = extractDefaultModule(existingFeatureContent);
          console.log(`Default module detected: ${defaultModule}`);

          // Split into individual lines for line-by-line updates
          let featureLines = existingFeatureContent.split("\n");

          // Find the scenario URL we need
          const scenarioUrl = extractScenarioUrl(existingFeatureContent);

          // NEW: Collect all steps (existing + new) for module analysis
          const allSteps: any[] = [];

          // Add existing steps (from feature changes)
          featureChanges.forEach((change) => {
            if (change.type === "update" || change.type === "insert") {
              allSteps.push(change.newStep);
            }
          });

          // NEW: Analyze module usage across all steps
          const { usedModules } = analyzeModuleUsage(allSteps, defaultModule);
          console.log(`Used modules: ${Array.from(usedModules)}`);

          // NEW: Remove unused module load statements first
          featureLines = removeUnusedModules(
            featureLines,
            usedModules,
            defaultModule
          );

          // Find where test steps actually start in the feature file
          function findStepsStartLine(featureLines: string[]): number {
            // Look for the line that contains "Given I am on the home page"
            const givenLineIndex = featureLines.findIndex((line) =>
              line.trim().includes("Given I am on the home page")
            );

            if (givenLineIndex >= 0) {
              // Steps start 2 lines after "Given I am on the home page" (after the locators line)
              return givenLineIndex + 2;
            }

            // Fallback: look for any line starting with "Then"
            const firstThenIndex = featureLines.findIndex((line) =>
              line.trim().startsWith("Then ")
            );

            if (firstThenIndex >= 0) {
              return firstThenIndex;
            }

            // Final fallback to default offset calculation
            return featureLines[0] && featureLines[0].trim().startsWith("@")
              ? 4
              : 3;
          }

          const stepsStartLine = findStepsStartLine(featureLines);

          // Adjust feature line indices based on actual steps start position
          const adjustedChanges = featureChanges.map((change) => ({
            ...change,
            featureLineIndex: change.jsonIndex + stepsStartLine,
          }));

          // Apply changes in reverse order to maintain line numbers
          const sortedChanges = adjustedChanges.sort(
            (a, b) => (b.featureLineIndex || 0) - (a.featureLineIndex || 0)
          );

          // NEW: Track which modules need to be inserted after which steps
          const moduleInsertionPoints: Array<{
            beforeLineIndex: number;
            module: string;
          }> = [];

          sortedChanges.forEach((change) => {
            switch (change.type) {
              case "update":
                // Replace existing line with updated step
                const updatedGherkinLine = convertStepToGherkin(
                  change.newStep,
                  scenarioUrl
                );
                if (
                  updatedGherkinLine &&
                  change.featureLineIndex < featureLines.length
                ) {
                  featureLines[change.featureLineIndex] =
                    updatedGherkinLine.trimEnd();

                  // NEW: Check if this step needs a module load after it
                  const stepModule = change.newStep.elementModule;
                  if (stepModule && stepModule !== defaultModule) {
                    const needsModuleLoad = !moduleInsertionPoints.some(
                      (point) => point.module === stepModule
                    );
                    if (needsModuleLoad) {
                      moduleInsertionPoints.push({
                        beforeLineIndex: change.featureLineIndex,
                        module: stepModule,
                      });
                    }
                  }
                }
                break;

              case "insert":
                // Insert new line at specific position
                const insertGherkinLine = convertStepToGherkin(
                  change.newStep,
                  scenarioUrl
                );
                if (insertGherkinLine) {
                  featureLines.splice(
                    change.featureLineIndex,
                    0,
                    insertGherkinLine.trimEnd()
                  );

                  // NEW: Check if this step needs a module load after it
                  const stepModule = change.newStep.elementModule;
                  if (stepModule && stepModule !== defaultModule) {
                    const needsModuleLoad = !moduleInsertionPoints.some(
                      (point) => point.module === stepModule
                    );
                    if (needsModuleLoad) {
                      moduleInsertionPoints.push({
                        beforeLineIndex: change.featureLineIndex,
                        module: stepModule,
                      });
                    }
                  }
                }
                break;

              case "delete":
                // Remove line at specific position
                if (change.featureLineIndex < featureLines.length) {
                  featureLines.splice(change.featureLineIndex, 1);
                }
                break;
            }
          });

          // NEW: Insert module load statements after their corresponding steps
          moduleInsertionPoints
            .sort((a, b) => b.beforeLineIndex - a.beforeLineIndex) // Process in reverse order
            .forEach((insertion) => {
              const moduleLoadLine = `    Then I load the "elementProperties" module "${insertion.module}" locators`;
              featureLines.splice(insertion.beforeLineIndex, 0, moduleLoadLine);
            });

          // Write updated feature file
          await fs.writeFile(featureFilePath, featureLines.join("\n"), "utf-8");

          console.log(
            `Successfully applied ${featureChanges.length} smart changes to ${fileName}.feature with smart module management`
          );
        } catch (featureError) {
          console.error(
            `Failed to apply smart updates to feature file ${fileName}.feature:`,
            featureError
          );

          // Feature file should already exist
          if ((featureError as any).code === "ENOENT") {
            return NextResponse.json(
              {
                error: `Feature file ${fileName}.feature not found. Feature file should exist for this test case.`,
              },
              { status: 404 }
            );
          } else {
            // Some other error with feature file
            return NextResponse.json(
              {
                error: `Failed to update feature file: ${fileName}.feature`,
              },
              { status: 500 }
            );
          }
        }
      } else if (stepsToAppendToFeature.length > 0) {
        // Fallback to append-only mode if no smart changes provided (backward compatibility)
        try {
          // Read current feature file
          const existingFeatureContent = await fs.readFile(
            featureFilePath,
            "utf-8"
          );

          // NEW: Extract default module for append mode too
          const defaultModule = extractDefaultModule(existingFeatureContent);

          // Find the scenario URL we need
          const scenarioUrl = extractScenarioUrl(existingFeatureContent);

          // NEW: Convert steps to Gherkin format with smart module management
          const newGherkinSteps = convertStepsToGherkinWithModules(
            stepsToAppendToFeature,
            scenarioUrl,
            defaultModule
          );

          // Add new steps to feature file (remove trailing newline to avoid double spacing)
          const updatedFeatureContent =
            existingFeatureContent.trimEnd() + "\n" + newGherkinSteps.trimEnd();

          // Save feature file
          await fs.writeFile(featureFilePath, updatedFeatureContent, "utf-8");

          console.log(
            `Successfully appended ${stepsToAppendToFeature.length} new steps to ${fileName}.feature with smart module management`
          );
        } catch (featureError) {
          console.error(
            `Failed to update feature file ${fileName}.feature:`,
            featureError
          );

          // Feature file should already exist
          if ((featureError as any).code === "ENOENT") {
            return NextResponse.json(
              {
                error: `Feature file ${fileName}.feature not found. Feature file should exist for this test case.`,
              },
              { status: 404 }
            );
          } else {
            // Some other error with feature file
            return NextResponse.json(
              {
                error: `Failed to update feature file: ${fileName}.feature`,
              },
              { status: 500 }
            );
          }
        }
      }

      // Build success message
      let message = "";
      if (featureChanges && featureChanges.length > 0) {
        message = `Successfully applied ${featureChanges.length} smart changes to ${fileName}.feature with smart module management`;
      } else if (stepsToAppendToFeature.length > 0) {
        message = `Successfully appended ${stepsToAppendToFeature.length} new steps to ${fileName}.feature with smart module management`;
      } else {
        message = `No changes to apply to ${fileName}.feature`;
      }

      return NextResponse.json({
        success: true,
        message: message,
        newStepsAdded: stepsToAppendToFeature.length,
        featureChangesApplied: featureChanges ? featureChanges.length : 0,
      });
    } catch (fileError) {
      console.error(
        `Failed to read/write feature file ${fileName}.feature:`,
        fileError
      );

      // Feature file should exist already
      if ((fileError as any).code === "ENOENT") {
        return NextResponse.json(
          {
            error: `Feature file ${fileName}.feature not found. Feature file should exist for this test case.`,
          },
          { status: 404 }
        );
      } else {
        return NextResponse.json(
          {
            error: `Failed to read/write feature file: ${fileName}.feature`,
          },
          { status: 500 }
        );
      }
    }
  } catch (error) {
    console.error("Error saving steps:", error);
    return NextResponse.json(
      { error: "Failed to save steps" },
      { status: 500 }
    );
  }
}

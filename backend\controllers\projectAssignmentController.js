const db = require('../db');
exports.projectAssignment = async (req, res) => {
  const [users] = await db.execute('SELECT * FROM users WHERE name = ?', [req.body.assignee]);
  const [role] = await db.execute('SELECT * FROM roles WHERE name = ?', [req.body.role]);
  await db.execute(
    'INSERT INTO projectassigment (projectid, userid, role_id) VALUES (?, ?, ?)',
    [req.body.projectid, users[0].id, role[0].id])

  // FIX: Send response instead of return null
  return res.json({ 
    success: true, 
    message: 'Assignment created successfully' 
  });
}
exports.getProjectAssignments = async (req, res) => {
  try {
   const [rows] = await db.execute('SELECT p.name as project_name, u.name as user_name, r.name as role_name FROM (SELECT * FROM antp_web.projectassigment WHERE projectid = ?) a JOIN antp_web.users u ON a.userid = u.id JOIN antp_web.projects p ON a.projectid = p.id JOIN antp_web.roles r ON a.role_id = r.id;', [req.params.id]);
    if (rows.length === 0) return res.status(404).json({ error: 'No assignments found for this project' });
    res.json(rows);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
}
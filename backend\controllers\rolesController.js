const db = require('../db');

// GET All roles
exports.getroles = async (_req, res) => {
  try {
    const [rows] = await db.execute('SELECT * FROM roles');
    res.json(rows);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};
exports.getRoleById = async (req, res) => {
  try {
    const [rows] = await db.execute('SELECT * FROM roles WHERE id = ?', [req.params.id]);
    if (rows.length === 0) return res.status(404).json({ error: 'Role not found' });
    res.json(rows[0]);
  } catch (error) {
    res.status(500).json({ error: error.message });
  } 
};
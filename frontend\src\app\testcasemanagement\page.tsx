"use client";
import { useState, useEffect } from "react";
import { AppSidebar } from "@/components/dashboard-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { Projectdropdown } from "@/components/projectdropdown";
import { ProjectManagementContent } from "@/components/projectmanagement";

import CreateTestCaseForm from "@/components/Newtestcaseform";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Record from "@/components/Record";
import SaveValidatedGherkin from "@/components/SaveValidatedGherkin";
import CreateElementExtractor from "@/components/CreateElementExtractor";
import ElementExtractorCapture from "@/components/ElementExtractorCapture";
import axios from 'axios';
export default function Page() {
  const [selectedSection, setSelectedSection] = useState("Testcases");

  const [dialogOpen, setDialogOpen] = useState(false);
  const [refreshTestCases, setRefreshTestCases] = useState(false);
  const [elementExtractorDialogOpen, setElementExtractorDialogOpen] =
    useState(false);
  const [refreshElementExtractors, setRefreshElementExtractors] = useState(0);
  const [projectName, setProjectName] = useState("Loading...");
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {


const fetchProjectName = async () => {
  try {
    const response = await axios.get('/api/get-config');
    const config = response.data;

    setProjectName(config.projectName || "No Project Selected");
  } catch (error) {
    console.error("Error fetching project name:", error);
    setProjectName("Error Loading Project");
  }
};


    fetchProjectName();
  }, []);

  const getDisplayTitle = () => {
    if (selectedSection === "Testcases") {
      return `${projectName} - Test cases`;
    }
    if (selectedSection === "ElementExtractor") {
      return `${projectName} - Element Extractor`;
    }
    return selectedSection;
  };
  const handleElementExtractorSuccess = () => {
    setRefreshElementExtractors((prev) => prev + 1);
  };

  return (
    <SidebarProvider
      style={{ "--sidebar-width": "45px" } as React.CSSProperties}
    >
      <AppSidebar />

      <SidebarInset>
        <div className="flex h-screen overflow-hidden">
          {/* Sidebar - fixed and non-scrollable */}
          <aside className="w-[15%] border-r bg-muted p-2 space-y-1 bg-[#F7F9FC] font-normal tracking-normal font-roboto">
            <ul className="text-[#37393A] py-2 flex flex-col gap-2 rounded-xs">
              <li className="text-left w-full text-sm hover:bg-[#B1DBEA]">
                <button
                  className={`w-full px-2 py-1 text-left ${
                    selectedSection === "Testcases"
                      ? "bg-[#B1DBEA] text-[#15537C] font-medium"
                      : ""
                  }`}
                  onClick={() => setSelectedSection("Testcases")}
                >
                  Testcases
                </button>
              </li>

              <li className="text-left w-full text-sm hover:bg-[#B1DBEA]">
                <button
                  className={`w-full flex items-center justify-between px-2 py-1 text-left ${
                    selectedSection === "Elements"
                      ? "bg-[#B1DBEA] text-[#15537C] font-medium"
                      : ""
                  }`}
                  onClick={() => setSelectedSection("Elements")}
                >
                  <span>Elements</span>
                </button>
              </li>
              <li className="text-left w-full text-sm hover:bg-[#B1DBEA]">
                <button
                  className={`w-full flex items-center justify-between px-2 py-1 text-left ${
                    selectedSection === "ElementExtractor"
                      ? "bg-[#B1DBEA] text-[#15537C] font-medium"
                      : ""
                  }`}
                  onClick={() => setSelectedSection("ElementExtractor")}
                >
                  <span>ElementExtractor</span>
                </button>
              </li>
            </ul>
          </aside>

          {/* Main Content - flex column with scrollable content area */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Header - fixed */}
            <header className="bg-background flex items-center justify-between border-b p-2">
              <h1 className="text-left font-medium text-[24px] leading-[20px] tracking-[0px] text-[#2C2E2E]">
                {getDisplayTitle()}
              </h1>

              {selectedSection === "Testcases" && (
                <Button
                  className="bg-[#15537C] text-white hover:brightness-110 rounded-md w-34 h-6"
                  onClick={() => setDialogOpen(true)}
                >
                  Create Test Case
                </Button>
              )}
              {selectedSection === "ElementExtractor" && (
                <Button
                  className="bg-[#15537C] text-white hover:brightness-110 rounded-md w-45 h-6"
                  onClick={() => setElementExtractorDialogOpen(true)}
                >
                  Create Element Extractor
                </Button>
              )}
            </header>

            {/*  NEW: Sticky Search Row - ONLY search input, no buttons */}
            {selectedSection === "Testcases" && (
              <div className="bg-white border-b p-3 sticky top-0 z-10">
                <Input
                  type="text"
                  placeholder="Search Test Case by name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full h-9"
                />
              </div>
            )}

            {/* Scrollable Content Area */}
            <div className="flex-1 overflow-auto p-4">
              <ProjectManagementContent
                key={
                  selectedSection === "ElementExtractor"
                    ? `element-extractor-${refreshElementExtractors}`
                    : refreshTestCases
                    ? "refresh-1"
                    : "refresh-0"
                }
                section={selectedSection}
                name=""
                searchTerm={searchTerm}
              />
            </div>
          </div>
        </div>
      </SidebarInset>

      {/* Add the dialog component outside the layout */}
      <TestCaseDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        onRecordingStop={() => setRefreshTestCases((prev) => !prev)}
      />

      <ElementExtractorDialog
        open={elementExtractorDialogOpen}
        onOpenChange={setElementExtractorDialogOpen}
        onSuccess={handleElementExtractorSuccess}
      />
    </SidebarProvider>
  );
}

// TestCaseDialog component defined at the bottom of the file
function TestCaseDialog({
  open,
  onOpenChange,
  onRecordingStop,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onRecordingStop: () => void;
}) {
  const [recordDialogOpen, setRecordDialogOpen] = useState(false);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="max-w-6xl w-full"
        onInteractOutside={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">
            Create New Test Case
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="recorded" className="w-full mt-4">
          <TabsList className="w-full justify-start">
            <TabsTrigger value="recorded">Record</TabsTrigger>
            <TabsTrigger value="manual">Manual</TabsTrigger>
          </TabsList>

          <TabsContent value="manual" className="pt-4">
            <div className="flex justify-end">
              {/* <button
                onClick={() => setRecordDialogOpen(true)}
                title="Launch Cucumber Recorder"
                className="flex items-center gap-2 px-3 py-1 border rounded-md hover:bg-gray-100"
              >
                <img src="/Cucumber.svg" className="w-5 h-5" />
                <span>Record with Cucumber</span>
              </button> */}
            </div>
            <SaveValidatedGherkin onCancel={() => onOpenChange(false)} />
          </TabsContent>

          <TabsContent value="recorded" className="pt-4">
            <CreateTestCaseForm
              mode="recorded"
              projectUrl=""
              onRecordingStop={onRecordingStop}
              onCancel={() => onOpenChange(false)}
            />
          </TabsContent>
        </Tabs>

        <Record
          isOpen={recordDialogOpen}
          onOpenChange={setRecordDialogOpen}
          testCaseName="" // optionally bind this from form if needed
        />

        {/*<CreateTestCaseForm
          onRecordingStop={onRecordingStop}
          onCancel={() => onOpenChange(false)}
        />*/}
      </DialogContent>
    </Dialog>
  );
}

// Updated ElementExtractorDialog function in page.tsx
function ElementExtractorDialog({
  open,
  onOpenChange,
  onSuccess,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">
            Create New Element Extractor
          </DialogTitle>
        </DialogHeader>

        {/* Tabs for Manual and Extractor */}
        <Tabs defaultValue="manual" className="w-full mt-4">
          <TabsList className="w-full justify-start">
            <TabsTrigger value="manual">Manual</TabsTrigger>
            <TabsTrigger value="extractor">Extractor</TabsTrigger>{" "}
            {/*  Changed to "Extractor" */}
          </TabsList>

          {/*  EXISTING: Manual Tab - No changes */}
          <TabsContent value="manual" className="pt-4">
            <CreateElementExtractor
              onCancel={() => onOpenChange(false)}
              onSuccess={onSuccess}
            />
          </TabsContent>

          {/*  NEW: Extractor Tab */}
          <TabsContent value="extractor" className="pt-4">
            {" "}
            {/*  Changed to "extractor" */}
            <ElementExtractorCapture
              onCancel={() => onOpenChange(false)}
              onSuccess={onSuccess}
            />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}

import { NextResponse } from "next/server";
import fs from "fs-extra";
import path from "path";

// GET - Read current config
export async function GET() {
  try {
    const configPath = path.join(process.cwd(), "src", "config", "config.json");
    
    if (!(await fs.pathExists(configPath))) {
      return NextResponse.json(
        { error: "Config file not found" },
        { status: 404 }
      );
    }

    const configContent = await fs.readFile(configPath, "utf-8");
    const config = JSON.parse(configContent);

    return NextResponse.json(config);

  } catch (error) {
    console.error("Error reading config:", error);
    return NextResponse.json(
      { error: "Failed to read config" },
      { status: 500 }
    );
  }
}

// POST - Update config (switch active project)
export async function POST(req: Request) {
  try {
    const { projectName } = await req.json();

    if (!projectName) {
      return NextResponse.json(
        { error: "Project name is required" },
        { status: 400 }
      );
    }

    const configPath = path.join(process.cwd(), "src", "config", "config.json");
    
    let config = {};

    if (await fs.pathExists(configPath)) {
      const configContent = await fs.readFile(configPath, "utf-8");
      config = JSON.parse(configContent);
    }

    const currentProjectPath = `D:/TargetCode/${projectName}`;
    const updatedConfig = {
      ...config,
      projectName: projectName,
      currentProjectPath: currentProjectPath,
      recordingsPath: `${currentProjectPath}/antp-web/src/test/resources/features`,
      testflowpath: `${currentProjectPath}/antp-web/src/testflows.json`,
      testsuitepath: `${currentProjectPath}/antp-web/src/testsuites.json`,
      projectPath: `${currentProjectPath}/antp-web`,
      elementsPath: `${currentProjectPath}/antp-web/src/main/resources/elementProperties`, 
      elementextractorpath: `${currentProjectPath}/webElementExtractor/src/test/resources/features`,
      projectConfigurations: `${currentProjectPath}/antp-web/src/main/resources/config/url.properties`
    };

    await fs.writeFile(
      configPath,
      JSON.stringify(updatedConfig, null, 2),
      "utf-8"
    );

    console.log(` Active project switched to: ${projectName}`);

    return NextResponse.json({
      message: `Active project switched to '${projectName}'`,
      activeProject: projectName,
      config: updatedConfig
    });

  } catch (error) {
    console.error("Error switching active project:", error);
    return NextResponse.json(
      { error: "Failed to switch active project" },
      { status: 500 }
    );
  }
}

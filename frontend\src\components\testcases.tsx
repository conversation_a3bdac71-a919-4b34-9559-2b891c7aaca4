"use client";

import { useEffect, useState } from "react";
import { AccordionTable } from "@/components/table";
import axios from 'axios';
interface ProjectManagementContentProps {
  projectname: string;
  searchTerm?: string; //searchTerm prop
}

export function Testcases({ projectname, searchTerm = "" }: ProjectManagementContentProps) {
  const [testCases, setTestCases] = useState<
    Array<{ id: number; name: string; filePath: string }>
  >([]);
  const [isLoading, setIsLoading] = useState(true);

  // Filters test cases based on search term
  const filteredTestCases = testCases.filter(testCase =>
    testCase.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // NEW: Check dependencies before deletion
  const checkTestCaseDependencies = async (testCaseId: number) => {
    try {
  console.log(`Checking dependencies for test case ID: ${testCaseId}`);

  const response = await axios.get(`/api/getdependencies`, {
    params: { id: testCaseId },
  });

  const result = response.data;
  console.log('Dependencies check result:', result);

  return result;
} catch (error: any) {
  console.error('Error checking dependencies:', error);
  return { 
    testFlowUsage: [], 
    testSuiteUsage: { direct: [], indirect: [] },
    hasDependencies: false,
    error: true 
  };
}

  };

  // NEW: Validation with user-friendly warnings
  const validateTestCaseDeletion = async (testCaseId: number, testCaseName: string) => {
    try {
      const dependencies = await checkTestCaseDependencies(testCaseId);
      
      // Check if API call failed
      if (dependencies.error) {
        alert(
          ` Validation Error\n\nCannot verify dependencies for "${testCaseName}" due to a network error.\n\nDeletion cancelled for safety.`
        );
        return false;
      }
      
      // Check if test case has dependencies
      if (dependencies.hasDependencies) {
        let message = ` CANNOT DELETE TEST CASE\n\n`;
        message += `Test case "${testCaseName}" is currently being used in:\n\n`;
        
        if (dependencies.testFlowUsage?.length > 0) {
          message += ` TEST FLOWS:\n`;
          dependencies.testFlowUsage.forEach((flowName: string) => {
            message += `  • ${flowName}\n`;
          });
          message += '\n';
        }
        
        if (dependencies.testSuiteUsage?.direct?.length > 0) {
          message += ` TEST SUITES (Direct Usage):\n`;
          dependencies.testSuiteUsage.direct.forEach((suiteName: string) => {
            message += `  • ${suiteName}\n`;
          });
          message += '\n';
        }
        
        if (dependencies.testSuiteUsage?.indirect?.length > 0) {
          message += ` TEST SUITES (Via Test Flows):\n`;
          dependencies.testSuiteUsage.indirect.forEach((suiteName: string) => {
            message += `  • ${suiteName}\n`;
          });
          message += '\n';
        }
        
        message += ` DELETION BLOCKED\n\n`;
        message += `To delete this test case:\n`;
        message += `1. Remove it from the test flows listed above\n`;
        message += `2. Remove it from the test suites listed above\n`;
        message += `3. Try deleting again`;
        
        alert(message);
        return false; // Block deletion
      }
      
      // No dependencies - show final confirmation
      const confirmDelete = window.confirm(
        `Delete test case "${testCaseName}"?\n\n No dependencies found - safe to delete.\n\nThis action cannot be undone.`
      );
      
      return confirmDelete;
      
    } catch (error) {
      console.error('Error validating test case deletion:', error);
      
      alert(
        ` Validation Failed\n\nCannot verify dependencies for "${testCaseName}".\n\nDeletion cancelled for safety.`
      );
      
      return false; // Block deletion on error
    }
  };

  // NEW: Handle deletion with validation
  const handleDeleteTestCase = async (testCaseId: number, testCaseName: string) => {
    console.log(`Attempting to delete test case: ${testCaseName} (ID: ${testCaseId})`);
    
    // Step 1: Run cross-page validation
    const canDelete = await validateTestCaseDeletion(testCaseId, testCaseName);
    
    if (!canDelete) {
      console.log('Test case deletion blocked due to dependencies or user cancellation.');
      return;
    }

 try {
  console.log(`Proceeding with deletion of test case ID: ${testCaseId}`);

  // If your API expects POST for deletion, keep method as post; else, change to delete
  const response = await axios.post('/api/deletetestcase', null, {
    params: { id: testCaseId },
  });

  const result = response.data;
  console.log('Delete successful:', result);

  // Remove from local state
  setTestCases(prev => prev.filter(tc => tc.id !== testCaseId));

  alert(` Success\n\nTest case "${testCaseName}" deleted successfully.`);
} catch (error: any) {
  const errorMessage =
    error.response?.data?.error || 'Unknown error occurred';
  console.error('Delete failed:', errorMessage);

  alert(` Deletion Failed\n\n${errorMessage}\n\nPlease try again.`);
}

  };

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      const data = await getTestCasesFromAPI();
      setTestCases(data);
      setIsLoading(false);
    };

    fetchData();
  }, []);

  if (isLoading) return <div className="p-4">Loading test cases...</div>;

  return (
    <div className="relative">
      <AccordionTable 
        testCases={filteredTestCases} 
        onDeleteTestCase={handleDeleteTestCase} // NEW: Pass validation-enabled handler
      /> {/* Pass filtered data */}
    </div>
  );
}

async function getTestCasesFromAPI(): Promise<Array<{id: number; name: string; filePath: string}>> {
try {
  const response = await axios.get('/api/test-cases');
  const testCases = response.data;
  // console.log('from testcases', testCases);
  return testCases;
} catch (error) {
  console.error('Error fetching test cases:', error);
  return [];
}

}

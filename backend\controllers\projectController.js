const db = require('../db');



// CREATE Project
exports.createProject = async (req, res) => {
  console.log('Project creation request received:', req.body);
  const { name, description, tenant_id, module_id, is_enabled,projecturl } = req.body;
  console.log('Project details:', { name, description, tenant_id, module_id, is_enabled,projecturl });
  try {
    const [result] = await db.execute(
      'INSERT INTO projects (name, description, tenant_id, is_enabled,projecturl) VALUES (?, ?, ?, ?,?)',
      [name, description, tenant_id, is_enabled ?? true,projecturl]
    );
    console.log('Project creation request received:', req.body);
    res.status(201).json({ id: result.insertId, name });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// GET All Projects
exports.getProjects = async (_req, res) => {
  try {
    const [rows] = await db.execute('SELECT * FROM projects');
    res.json(rows);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// GET Project by ID
exports.getProjectById = async (req, res) => {
  try {
    const [rows] = await db.execute('SELECT * FROM projects WHERE id = ?', [req.params.id]);
    if (rows.length === 0) return res.status(404).json({ error: 'Project not found' });
    res.json(rows[0]);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// UPDATE Project
exports.updateProject = async (req, res) => {
  const { name, description, is_enabled } = req.body;
  try {
    await db.execute(
      'UPDATE projects SET name = ?, description = ?, is_enabled = ? WHERE id = ?',
      [name, description, is_enabled, req.params.id]
    );
    res.json({ message: 'Project updated' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// DELETE Project
exports.deleteProject = async (req, res) => {
  try {
    await db.execute('DELETE FROM projects WHERE id = ?', [req.params.id]);
    res.json({ message: 'Project deleted' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

Feature: demo
  Scenario: User performs actions on https://demo.automationtesting.in/Index.html
    Given I am on the home page
    Then I load the "elementProperties" module "Index" locators
    Then I click on "emailidforsignup_@id="email"]" on "https://demo.automationtesting.in/Index.html" page
    Then I send "hello" as a "emailidforsignup_id_xpath" in to "https://demo.automationtesting.in/Index.html" page
    Then I click on "img-1753368763386_@id="enterimg"]" on "https://demo.automationtesting.in/Index.html" page
    Then I click on "firstname_@placeholder="First Name"]" on "https://demo.automationtesting.in/Index.html" page
    Then I send "syed" as a "firstname_placeholder_xpath" in to "https://demo.automationtesting.in/Index.html" page
    Then I send "nauman" as a "lastname_placeholder_xpath" in to "https://demo.automationtesting.in/Index.html" page
    Then I click on "address_@class, "form-control")]" on "https://demo.automationtesting.in/Index.html" page
    Then I send "abc " as a "address_//textarea[contains(@_xpath" in to "https://demo.automationtesting.in/Index.html" page
    Then I click on "input-1753368772427_@type="email"]" on "https://demo.automationtesting.in/Index.html" page
    Then I send "<EMAIL>" as a "input-1753368776694_type_xpath" in to "https://demo.automationtesting.in/Index.html" page
    Then I click on "phone*_@type="tel"]" on "https://demo.automationtesting.in/Index.html" page
    Then I send "1112223334" as a "phone*_type_xpath" in to "https://demo.automationtesting.in/Index.html" page
    Then I click on "male_@class, "form-group")]/div[1]/label[1]" on "https://demo.automationtesting.in/Index.html" page
    Then I click on "radiooptions_@class, "col-md-4")]/label[1]/input[1]" on "https://demo.automationtesting.in/Index.html" page
    Then I send "Male" as a "radiooptions_//div[contains(@_xpath" in to "https://demo.automationtesting.in/Index.html" page

"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function Home() {
  const router = useRouter();

  useEffect(() => {
    // This is a placeholder for actual authentication check
    const isAuthenticated = localStorage.getItem("authToken");

    if (isAuthenticated) {
      router.push("/projectmanagement"); // Redirect to projects page if authenticated
    } else {
      router.push("/login");
    }
  }, [router]);

  return null; // Render nothing while redirecting
}

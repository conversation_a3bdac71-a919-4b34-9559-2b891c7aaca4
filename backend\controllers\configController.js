const express = require("express");
const XLSX = require("xlsx");
const path = require('path');

const app = express();
app.use(express.json());

const filePath = path.join(__dirname, '../files/config.xlsx');

// READ Config from Excel file
exports.readConfig = async (req, res) => {
  try {
    
    // Read the Excel file
    const workbook = XLSX.readFile(filePath);
    const sheetName = workbook.SheetNames[0]; // Get first sheet
    const worksheet = workbook.Sheets[sheetName];
    
    // Convert to JSON array
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    
    if (jsonData.length < 2) {
      return res.status(400).json({ error: 'File must have at least 2 rows (header and data)' });
    }
    
    const headers = jsonData[0]; // First row as keys
    const result = [];
    
    // Process each data row (starting from index 1)
    for (let i = 1; i < jsonData.length; i++) {
      const row = jsonData[i];
      const rowObject = {};
      
      headers.forEach((header, index) => {
        rowObject[header] = row[index] || null;
      });
      
      result.push(rowObject);
    }
    
    res.json({
      success: true,
      data: result,
      totalRecords: result.length
    });
    
  } catch (error) {
    res.status(500).json({ 
      error: 'Failed to read config file',
      details: error.message 
    });
  }
};


exports.saveConfig = async (req, res) => {
  try {
    const configs = req.body;

    if (!Array.isArray(configs) || configs.length === 0) {
      return res.status(400).json({ error: "Request body must be a non-empty array" });
    }

    // Headers (fixed order)
    const headers = [
      "Environment",
      "URI",
      "DBtype",
      "DBhost",
      "DBport",
      "DBuser",
      "DBpwd",
      "DBname",
      "BasicAuth",
      "Username",
      "Password"
    ];

    // 🔹 Prepare data: first row headers + new configs
    const data = [headers];
    configs.forEach(obj => {
      const row = headers.map(h => obj[h] || "");
      data.push(row);
    });

    // 🔹 Create or overwrite workbook
    const worksheet = XLSX.utils.aoa_to_sheet(data);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

    // 🔹 Save to file
    XLSX.writeFile(workbook, filePath);

    res.status(201).json({
      success: true,
      message: "Configs added successfully",
      totalRecords: configs.length
    });
  } catch (err) {
    res.status(500).json({
      error: "Failed to save config",
      details: err.message
    });
  }
};

exports.getEnvNames = async (req,res) => {
   // Get the first worksheet
const workbook = XLSX.readFile(filePath);
const sheetName = workbook.SheetNames[0];
const worksheet = workbook.Sheets[sheetName];

// Convert worksheet to JSON
const data = XLSX.utils.sheet_to_json(worksheet);

const envNames = data
  .map(row => row.Environment || row.environment) 
  .filter(env => env && env.trim() !== '') 
  .filter((env, index, self) => self.indexOf(env) === index); 


res.json(envNames); 
}

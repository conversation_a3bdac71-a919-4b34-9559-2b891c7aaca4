"use client";

import * as React from "react";
import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";
import { NavUser } from "@/components/nav-user";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";

// This is sample data
const data = {
  user: {
    name: "User",
    email: "<EMAIL>",
    avatar: "", // Added for NavUser component
  },
  navMain: [
    {
      title: "ProjectManagement",
      url: "/projectmanagement",
      icon: "/dashboard-icons/newproject.svg",
      isActive: false,
    },
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: "/dashboard-icons/dashboard.svg",
      isActive: true,
    },
    {
      title: "TestCaseManagement",
      url: "/testcase",
      icon: "/dashboard-icons/projectmanagement.svg",
      isActive: true,
    },
    {
      title: "TestFlows",
      url: "/testFlowView",
      icon: "/dashboard-icons/testflows.svg",
      isActive: false,
    },
    {
      title: "TestSuites",
      url: "/testsuites",
      icon: "/dashboard-icons/testsuite.svg",
      isActive: false,
    },
     {
      title: "Test Suites",
      url: "/testsuitesapi",
      icon: "/dashboard-icons/testsuite.svg",
      isActive: false,
    },
    // {
    //   title: "projectconfigurations",
    //   url: "/projectconfigurations",
    //   icon: "/dashboard-icons/testsuite.svg",
    //   isActive: false,
    // },
    // {
    //   title: "APIs",
    //   url: "/testapis",
    //   icon: "/dashboard-icons/testapi.svg",
    //   isActive: false,
    // },
    // {
    //   title: "Test Flows",
    //   url: "/testflowsui",
    //   icon: "/dashboard-icons/testflowsui.svg",
    //   isActive: false,
    // },
    {
      title: "Configs",
      url: "/config",
      icon: "/dashboard-icons/config.svg",
      isActive: true,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  // Note: I'm using state to show active item.
  // IRL you should use the url/router.
  const router = useRouter();
  const pathname = usePathname();
  const { setOpen } = useSidebar();

  const [activeItem, setActiveItem] = React.useState(
    () => data.navMain.find((item) => item.url === pathname) || data.navMain[0]
  );

  React.useEffect(() => {
    const currentItem = data.navMain.find((item) => item.url === pathname);
    if (currentItem) {
      setActiveItem(currentItem);
    }
  }, [pathname]);

  const handleNavClick = (item: typeof data.navMain[0]) => {
    setOpen(true);
    
    // If clicking on ProjectManagement, force a refresh
    if (item.title === "ProjectManagement") {
      // Add a timestamp to force route refresh
      const currentPath = item.url;
      router.push(`${currentPath}?refresh=${Date.now()}`);
    } else {
      router.push(item.url);
    }
  };

  return (
    <Sidebar {...props}>
      {/* This is the first sidebar */}
      {/* We disable collapsible and adjust width to icon. */}
      {/* This will make the sidebar appear as icons. */}
      <Sidebar
        collapsible="none"
        className="bg-[#15537C] 
        bg-no-repeat 
        shadow-[0px_1px_2px_#00000029] 
        w-[calc(var(--sidebar-width-icon))]! border-r"
      >
        <SidebarContent>
          <SidebarGroup className="w-full p-0">
            <SidebarGroupContent className="">
              <SidebarMenu>
                {data.navMain.map((item) => (
                  <SidebarMenuItem
                    key={item.title}
                    onClick={() => handleNavClick(item)}
                  >
                    <SidebarMenuButton
                      tooltip={{
                        children: item.title,
                        hidden: false,
                      }}
                      isActive={activeItem?.title === item.title}
                      className={`
                         w-full flex items-center rounded-none py-5
                        data-[active=true]:bg-[#00AB6A] data-[active=true]:text-white
                        data-[active=true]:border-l-3
                        hover:bg-[#00AB6A] hover:text-white
                      `}
                    >
                      <Image
                        src={item.icon}
                        alt={item.title}
                        width={120}
                        height={120}
                        className="w-30 h-30"
                      />
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
        <SidebarFooter>
          <NavUser user={data.user} />
        </SidebarFooter>
      </Sidebar>

      {/* This is the second sidebar */}
      {/* We disable collapsible and let it fill remaining space */}
    </Sidebar>
  );
}
const express = require('express');
const router = express.Router();
const controller = require('../controllers/testSuiteUiController');



router.get('/testsuiteapi', controller.getTestSuites);

router.get('/', controller.getTestSuites);
router.get('/:name', controller.getTestSuiteByName);

router.get('/read', con.readConfig);
router.post('/save',configController.saveConfig);


module.exports = router;

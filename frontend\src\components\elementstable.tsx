"use client";

import { useState, useCallback } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown, ChevronUp, Plus } from "lucide-react";
import { UIElement, ElementGroup } from "./elements"; // Import both interfaces

// Element types for dropdown
const ELEMENT_TYPES = [
  { value: "button", label: "Button" },
  { value: "input", label: "Input" },
  { value: "xpath", label: "XPath" },
  { value: "css", label: "CSS" },
  { value: "id", label: "ID" },
  { value: "class", label: "Class" },
] as const;

// Create new element template
function getNewElement(id: number): UIElement {
  return {
    id,
    name: "",
    fullKey: "", // Full key for AccordionTable dropdown
    type: "",
    value: "",
    screenshot: "",
    moduleName: "", // NEW field for module name
  };
}

// Component for a single element group accordion
function SingleElementGroup({
  groupNumber,
  groupName,
  initialElements,
  projectName,
  onRefresh,
  showActionButtons = true,
  showCheckboxes = true,
  allowEditing = true,
}: {
  groupNumber: number;
  groupName?: string;
  initialElements: UIElement[];
  projectName: string;
  onRefresh?: () => void;
  showActionButtons?: boolean;
  showCheckboxes?: boolean;
  allowEditing?: boolean;
}) {
  const [elements, setElements] = useState<UIElement[]>(initialElements);
  const [selectedElements, setSelectedElements] = useState<boolean[]>(
    Array(initialElements.length).fill(false)
  );
  const [selectAll, setSelectAll] = useState(false);
  const [editingCell, setEditingCell] = useState<{
    row: number | null;
    field: string | null;
  }>({ row: null, field: null });

  const [originalElementsCount, setOriginalElementsCount] = useState(
    initialElements.length
  );
  const [isSaving, setIsSaving] = useState(false);
  const [isAccordionOpen, setIsAccordionOpen] = useState(false);

  const toggleSelectAll = () => {
    const newVal = !selectAll;
    setSelectAll(newVal);
    setSelectedElements(Array(elements.length).fill(newVal));
  };

  const toggleElement = (index: number) => {
    const updated = [...selectedElements];
    updated[index] = !updated[index];
    setSelectedElements(updated);
    setSelectAll(updated.every((v) => v));
  };

  const handleAddRow = () => {
    const newElement = getNewElement(elements.length + 1);
    setElements([...elements, newElement]);
    setSelectedElements([...selectedElements, false]);
  };

  const handleDeleteSelected = () => {
    const elementsToKeep = elements.filter(
      (_, index) => !selectedElements[index]
    );

    // Update local state - renumber the remaining elements
    const renumbered = elementsToKeep.map((element, i) => ({
      ...element,
      id: i + 1,
    }));

    setElements(renumbered);
    setSelectedElements(Array(renumbered.length).fill(false));
    setSelectAll(false);

    console.log("Deleted selected elements (static mode)");
  };

  const handleCellClick = (rowIndex: number, field: string) => {
    setEditingCell({ row: rowIndex, field });
  };

  const handleCellChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    rowIndex: number,
    field: keyof UIElement
  ) => {
    const updatedElements = [...elements];
    updatedElements[rowIndex] = {
      ...updatedElements[rowIndex],
      [field]: e.target.value,
    };
    setElements(updatedElements);
  };

  const handleTypeChange = (rowIndex: number, newType: string) => {
    const updatedElements = [...elements];
    updatedElements[rowIndex] = {
      ...updatedElements[rowIndex],
      type: newType,
    };
    setElements(updatedElements);
    setEditingCell({ row: null, field: null });
  };

  const handleBlur = () => {
    setEditingCell({ row: null, field: null });
  };

  const handleSaveClick = () => {
    if (!groupName) {
      console.error("Group name is required for saving");
      return;
    }

    // Get only the new elements (beyond original count)
    const newElements = elements.slice(originalElementsCount);

    // Filter out empty new elements
    const validNewElements = newElements.filter(
      (element) =>
        element.name &&
        element.name.trim() !== "" &&
        element.value &&
        element.value.trim() !== ""
    );

    if (validNewElements.length === 0) {
      console.log("No new valid elements to save");
      return;
    }

    setIsSaving(true);

    // Simulate save operation
    setTimeout(() => {
      console.log("Save successful (static mode):", validNewElements);

      // Update original elements count to include the saved elements
      setOriginalElementsCount(elements.length);
      setIsSaving(false);

      if (onRefresh) onRefresh();
    }, 1000);
  };

  const handleValidateElement = (elementId: number) => {
    const element = elements.find((el) => el.id === elementId);
    if (!element) return;

    // Simple static validation - just check if selector is not empty
    const isValid = element.value && element.value.trim() !== "";

    console.log("Validation result (static mode):", {
      selector: element.value,
      isValid,
    });

    // Update element status
    const updatedElements = [...elements];
    const elementIndex = updatedElements.findIndex((el) => el.id === elementId);
    if (elementIndex !== -1) {
      setElements(updatedElements);
    }
  };

  return (
    <AccordionItem value={`element-group-${groupNumber}`}>
      <AccordionTrigger
        className="hover:no-underline p-0 [&>svg]:hidden"
        onClick={() => setIsAccordionOpen(!isAccordionOpen)}
      >
        <div className="flex items-center h-[45px] px-4 w-full">
          <div className="flex items-center flex-1">
            {showCheckboxes && (
              <input
                type="checkbox"
                checked={selectAll}
                onChange={toggleSelectAll}
                onClick={(e) => e.stopPropagation()}
                className="mr-3"
              />
            )}
            <span className="text-left text-base font-medium">
              {groupName || `Element Group ${groupNumber}`}
            </span>
          </div>
          <div className="flex items-center gap-3 pr-2">
            {showActionButtons && (
              <>
                {/* Save Button */}
                <a
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSaveClick();
                  }}
                  className="cursor-pointer"
                  title="Save new elements"
                >
                  <img
                    src="/file.svg"
                    className="w-[16px] h-[14px] cursor-pointer"
                    style={{ opacity: isSaving ? 0.5 : 1 }}
                  />
                </a>

                {/* Add Element Button */}
                <a
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAddRow();
                  }}
                  title="Add new element"
                >
                  <Plus className="w-4 h-4" />
                </a>

                {/* Delete Selected Button */}
                <a
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteSelected();
                  }}
                  title="Delete selected elements"
                >
                  <img
                    src="/Group 21846.svg"
                    className="w-[16px] h-[14px] cursor-pointer"
                  />
                </a>
              </>
            )}
            {/* Custom Chevron Arrow */}
            {isAccordionOpen ? (
              <ChevronUp className="h-4 w-4 shrink-0 text-gray-600" />
            ) : (
              <ChevronDown className="h-4 w-4 shrink-0 text-gray-600" />
            )}
          </div>
        </div>
      </AccordionTrigger>

      <AccordionContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow>
              {showCheckboxes && <TableHead className="w-[5%]"></TableHead>}
              <TableHead className="w-[5%]">ID</TableHead>
              <TableHead className="w-[25%]">Name</TableHead>
              <TableHead className="w-[20%]">Type</TableHead>
              <TableHead className="w-[30%]">Value</TableHead>
              <TableHead className="w-[15%]">Screenshot</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {elements.map((element, index) => (
              <TableRow
                key={element.id}
                className={index % 2 === 0 ? "bg-[#F7F9FC]" : "bg-[#FFFFFF4D]"}
              >
                {showCheckboxes && (
                  <TableCell className="w-[5%]">
                    <input
                      type="checkbox"
                      checked={selectedElements[index]}
                      onChange={() => toggleElement(index)}
                      className="accent-blue-500"
                    />
                  </TableCell>
                )}
                <TableCell className="w-[5%]">{element.id}</TableCell>

                {/* Name - editable */}
                <TableCell className="w-[25%]">
                  {allowEditing &&
                  editingCell.row === index &&
                  editingCell.field === "name" ? (
                    <input
                      value={element.name}
                      onChange={(e) => handleCellChange(e, index, "name")}
                      onBlur={handleBlur}
                      autoFocus
                      className="w-full bg-white border border-gray-300 rounded px-2 py-1"
                      placeholder="Element name"
                    />
                  ) : (
                    <span
                      onClick={
                        allowEditing
                          ? () => handleCellClick(index, "name")
                          : undefined
                      }
                      className="cursor-pointer"
                    >
                      {element.name || "—"}
                    </span>
                  )}
                </TableCell>

                {/* Type - dropdown */}
                <TableCell className="w-[20%]">
                  {allowEditing &&
                  editingCell.row === index &&
                  editingCell.field === "type" ? (
                    <DropdownMenu
                      open={true}
                      onOpenChange={(open) => {
                        if (!open) {
                          setEditingCell({ row: null, field: null });
                        }
                      }}
                    >
                      <DropdownMenuTrigger className="w-full text-left px-2 py-1 border border-gray-300 rounded bg-white">
                        {element.type || "Select type"}
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="w-[var(--radix-dropdown-menu-trigger-width)]">
                        {ELEMENT_TYPES.map((type) => (
                          <DropdownMenuItem
                            key={type.value}
                            onClick={() => handleTypeChange(index, type.value)}
                          >
                            {type.label}
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  ) : (
                    <span
                      onClick={
                        allowEditing
                          ? () => handleCellClick(index, "type")
                          : undefined
                      }
                      className="cursor-pointer"
                    >
                      {element.type || "—"}
                    </span>
                  )}
                </TableCell>

                {/* Value - editable */}
                <TableCell className="w-[30%]">
                  {allowEditing &&
                  editingCell.row === index &&
                  editingCell.field === "value" ? (
                    <input
                      value={element.value}
                      onChange={(e) => handleCellChange(e, index, "value")}
                      onBlur={handleBlur}
                      autoFocus
                      className="w-full bg-white border border-gray-300 rounded px-2 py-1"
                      placeholder="CSS selector or XPath"
                    />
                  ) : (
                    <span
                      onClick={
                        allowEditing
                          ? () => handleCellClick(index, "value")
                          : undefined
                      }
                      className="cursor-pointer font-mono text-sm"
                    >
                      {element.value || "—"}
                    </span>
                  )}
                </TableCell>

                {/* Screenshot - editable */}
                <TableCell className="w-[15%]">
                  {allowEditing &&
                  editingCell.row === index &&
                  editingCell.field === "screenshot" ? (
                    <input
                      value={element.screenshot}
                      onChange={(e) => handleCellChange(e, index, "screenshot")}
                      onBlur={handleBlur}
                      autoFocus
                      className="w-full bg-white border border-gray-300 rounded px-2 py-1"
                      placeholder="screenshot.png"
                    />
                  ) : (
                    <span
                      onClick={
                        allowEditing
                          ? () => handleCellClick(index, "screenshot")
                          : undefined
                      }
                      className="cursor-pointer text-blue-500"
                    >
                      {element.screenshot ? (
                        <div className="flex items-center">
                          <img
                            src="/Group 23576.svg"
                            className="w-[16px] h-[19px] inline-block mr-2"
                          />
                          {element.screenshot}
                        </div>
                      ) : (
                        "—"
                      )}
                    </span>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </AccordionContent>
    </AccordionItem>
  );
}

// Main component that renders all element groups
export function ElementsTable({
  elementGroups = [],
  projectName,
  onRefresh,
  showActionButtons = true,
  showCheckboxes = true,
  allowEditing = true,
}: {
  elementGroups: ElementGroup[];
  projectName: string;
  onRefresh?: () => void;
  showActionButtons?: boolean;
  showCheckboxes?: boolean;
  allowEditing?: boolean;
}) {
  if (elementGroups.length === 0) {
    return (
      <div className="p-8 text-center text-gray-500">
        <div className="text-gray-400 mb-4">
          <svg
            className="w-16 h-16 mx-auto mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
            />
          </svg>
          <p className="text-lg font-medium text-gray-600">
            No element groups found
          </p>
          <p className="text-sm text-gray-500">
            No property files found in the element properties directory
          </p>
        </div>
      </div>
    );
  }

  // Use actual groups from API instead of hardcoded grouping
  return (
    <Accordion type="multiple" className="w-full h-full space-y-0.5">
      {elementGroups.map((group) => (
        <SingleElementGroup
          key={group.id}
          groupNumber={group.id}
          groupName={group.name}
          initialElements={group.elements}
          projectName={projectName}
          onRefresh={onRefresh}
          showActionButtons={showActionButtons}
          showCheckboxes={showCheckboxes}
          allowEditing={allowEditing}
        />
      ))}
    </Accordion>
  );
}

export default ElementsTable;

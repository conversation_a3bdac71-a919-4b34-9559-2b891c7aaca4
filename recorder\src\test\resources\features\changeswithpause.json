[{"action": "navigate", "url": "about:blank", "timestamp": "2025-08-08T14:25:34.194Z"}, {"action": "click", "selector": "//div[contains(@class, \"col-xs-7\")]", "url": "https://demo.automationtesting.in/", "title": "Index", "label": "div-1754663150732_//div[contains(@_xpath", "timestamp": "2025-08-08T14:25:50.736Z"}, {"action": "click", "selector": "//*[@id=\"email\"]", "url": "https://demo.automationtesting.in/", "title": "Index", "label": "emailidforsignup_id_xpath", "timestamp": "2025-08-08T14:25:51.108Z"}, {"action": "type", "selector": "//*[@id=\"email\"]", "url": "https://demo.automationtesting.in/", "title": "Index", "value": "234321", "label": "emailidforsignup_id_xpath", "timestamp": "2025-08-08T14:25:52.802Z"}, {"action": "click", "selector": "//*[@id=\"enterimg\"]", "url": "https://demo.automationtesting.in/Register.html", "title": "Index", "label": "img-1754663152921_id_xpath", "timestamp": "2025-08-08T14:25:52.980Z"}, {"action": "click", "selector": "//input[@placeholder=\"First Name\"]", "url": "https://demo.automationtesting.in/Register.html", "title": "Register", "label": "firstname_placeholder_xpath", "timestamp": "2025-08-08T14:25:53.971Z"}, {"action": "type", "selector": "//input[@placeholder=\"First Name\"]", "url": "https://demo.automationtesting.in/Register.html", "title": "Register", "value": "1234312", "label": "firstname_placeholder_xpath", "timestamp": "2025-08-08T14:25:55.145Z"}, {"action": "click", "selector": "//input[@placeholder=\"Last Name\"]", "url": "https://demo.automationtesting.in/Register.html", "title": "Register", "label": "lastname_placeholder_xpath", "timestamp": "2025-08-08T14:25:55.236Z"}, {"action": "type", "selector": "//input[@placeholder=\"Last Name\"]", "url": "https://demo.automationtesting.in/Register.html", "title": "Register", "value": "12344231", "label": "lastname_placeholder_xpath", "timestamp": "2025-08-08T14:25:56.100Z"}, {"action": "click", "selector": "//textarea[contains(@class, \"form-control\")]", "url": "https://demo.automationtesting.in/Register.html", "title": "Register", "label": "address_//textarea[contains(@_xpath", "timestamp": "2025-08-08T14:25:56.216Z"}, {"action": "type", "selector": "//textarea[contains(@class, \"form-control\")]", "url": "https://demo.automationtesting.in/Register.html", "title": "Register", "value": "2134321", "label": "address_//textarea[contains(@_xpath", "timestamp": "2025-08-08T14:25:56.827Z"}, {"action": "click", "selector": "//input[@type=\"email\"]", "url": "https://demo.automationtesting.in/Register.html", "title": "Register", "label": "input-1754663156934_type_xpath", "timestamp": "2025-08-08T14:25:56.937Z"}, {"action": "assertion", "selector": "//div[contains(@class, \"center\")]/div[1]", "label": "div-1754663158991_//div[contains(@_xpath", "url": "https://demo.automationtesting.in/Register.html", "title": "Register", "assertionText": "(adsbygoogle = window.adsbygoogle || []).push({}); Full Name* Address Email address* Provide a valid email id for further updates Phone* Gender* Male FeMale Hobbies Cricket Movies Hockey Languages Ara", "assertionType": "text", "timestamp": "2025-08-08T14:25:58.994Z"}, {"action": "type", "selector": "//input[@type=\"email\"]", "url": "https://demo.automationtesting.in/Register.html", "title": "Register", "value": "123432", "label": "input-1754663159203_type_xpath", "timestamp": "2025-08-08T14:25:59.213Z"}]
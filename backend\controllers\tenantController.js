const db = require('../db');

exports.getAllTenants = async (req, res) => {
  const [rows] = await db.query('SELECT * FROM tenants');
  res.json(rows);
};

exports.getTenantById = async (req, res) => {
  const [rows] = await db.query('SELECT * FROM tenants WHERE id = ?', [req.params.id]);
  res.json(rows[0]);
};

exports.createTenant = async (req, res) => {
  const { name, is_enabled = true } = req.body;
  const [result] = await db.query(
    'INSERT INTO tenants (name, is_enabled) VALUES (?, ?)',
    [name, is_enabled]
  );
  console.log('Tenant creation request received:', req.body);
  res.status(201).json({ id: result.insertId, name, is_enabled });
};

exports.updateTenant = async (req, res) => {
  const { name, is_enabled } = req.body;
  await db.query(
    'UPDATE tenants SET name = ?, is_enabled = ? WHERE id = ?',
    [name, is_enabled, req.params.id]
  );
  res.json({ id: req.params.id, name, is_enabled });
};

exports.deleteTenant = async (req, res) => {
  await db.query('DELETE FROM tenants WHERE id = ?', [req.params.id]);
  res.status(204).send();
};

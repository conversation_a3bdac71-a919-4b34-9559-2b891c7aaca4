Feature: nnn
  Sc<PERSON>rio: User performs actions on https://demo.automationtesting.in/
    Given I am on the home page
    Then I load the "elementProperties" module "Index" locators
    Then I should be navigated to "undefined"
    Then I click on "emailidforsignup_id_xpath" on "undefined" page
    Then I send "emailidforsignup_id_xpath" as a "34" in to "undefined" page
    Then I click on "img-1754466517694_id_xpath" on "undefined" page
    Then I click on "firstname_placeholder_xpath" on "undefined" page
    Then I send "firstname_placeholder_xpath" as a "hfjghj" in to "undefined" page
    Then I click on "lastname_placeholder_xpath" on "undefined" page
    Then I send "lastname_placeholder_xpath" as a "fghjk" in to "undefined" page
    Then I click on "address_//textarea[contains(@_xpath" on "undefined" page
    Then I send "address_//textarea[contains(@_xpath" as a "gfhjkl" in to "undefined" page
    Then I click on "form-1754466525943_id_xpath" on "undefined" page
    Then I click on "input-1754466526563_type_xpath" on "undefined" page
    Then I send "input-1754466532036_type_xpath" as a "ertyuiokl," in to "undefined" page

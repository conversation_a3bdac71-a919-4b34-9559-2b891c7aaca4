let moduleNames: string[] = [];

// Setter
export const setModuleNamesGlobal = (names: string[]) => {
  moduleNames = names;
};

// Getter
export const getModuleNamesGlobal = () => moduleNames;


//Common functions
export const formatXML = (xml: string) => {
  try {
    const PADDING = "  "; // 2 spaces
    const reg = /(>)(<)(\/*)/g;
    let formatted = "";
    let pad = 0;

    xml = xml.replace(reg, "$1\r\n$2$3"); // add newlines
    xml.split("\r\n").forEach((node) => {
      let indent = 0;
      if (node.match(/.+<\/\w[^>]*>$/)) {
        indent = 0;
      } else if (node.match(/^<\/\w/)) {
        if (pad !== 0) {
          pad -= 1;
        }
      } else if (node.match(/^<\w([^>]*[^/])?>.*$/)) {
        indent = 1;
      } else {
        indent = 0;
      }

      const padding = new Array(pad + 1).join(PADDING);
      formatted += padding + node + "\r\n";
      pad += indent;
    });

    return formatted.trim();
  } catch (err) {
    console.error("Error formatting XML:", err);
    return xml; // fallback
  }
};

export const normalizeBody = (rawBody: string) => {
  if (!rawBody) return "";

  let content = typeof rawBody === "string" ? rawBody : JSON.stringify(rawBody);

  // unescape if needed
  if (content.startsWith('"') && content.endsWith('"')) {
    content = JSON.parse(content);
  }

  // try JSON pretty print
  try {
    return JSON.stringify(JSON.parse(content), null, 2);
  } catch {
    // fallback to XML formatting if it starts with '<'
    if (content.trim().startsWith("<")) {
      return formatXML(content);
    }
    return content;
  }
};

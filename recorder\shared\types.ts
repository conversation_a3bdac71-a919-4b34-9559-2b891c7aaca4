export interface RecordedAction {
  action: "click" | "type" | "navigate" | "select" | "scroll" | "rightclick" | "assertion";
  selector?: string;
  value?: string;
  url?: string;
  title?: string; // Optional title for the action
  label?: string;
  timestamp: string;
  
  // Properties for scroll action
  scrollX?: number;
  scrollY?: number;
  scrollDirection?: string;
  
  // Properties for assertion action
  assertionText?: string;
  assertionType?: string;
}

export interface RecordingSession {
  id: number;
  browser: string;
  isActive: boolean;
  actions: RecordedAction[];
}

export interface PlaybackResult {
  success: boolean;
  message: string;
  stepCount: number;
  duration: number;
}

export interface BrowserInstance {
  browser: any; // Puppeteer Browser
  page: any; // Puppeteer Page
}

export interface RecordingResponse {
  sessionId: number;
  status: string;
  startTime: string;
}

export interface StopRecordingResponse {
  sessionId: number;
  status: string;
  recordingTime: number;
  stepsCount: number;
  files: {
    json: string;
    feature: string;
  };
}

export interface PlaybackResponse {
  playbackId: string;
  status: string;
}
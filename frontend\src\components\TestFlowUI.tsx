"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  DialogDescription,
} from "@/components/ui/dialog";
import React, { useState, useEffect, useRef } from "react";
import { Button } from "./ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { TestFlowUILabels } from "@/data/label";
import { PortalAwareDraggable } from "./PortalAwareDraggable"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import axios from "axios";
import { DragDropContext, Droppable } from "@hello-pangea/dnd";
import {
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  Info,
  Loader2,
  Search,
} from "lucide-react";
import { fetchConfigs } from "@/services/config";

let environments: string[] = [];

interface TestCase {
  id: number | string;
  name: string;
  environment?: string;
  module?: string;
}

interface TestFlow {
  name: string;
  description: string;
  testcases: string[];
}

type TestFlowCreatorDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  editMode?: boolean;
  editingTestFlow?: TestFlow | null;
  moduleNames: string[];
  testFlowNames: string[]
};

export default function TestFlowUICreatorDialog({
  open,
  onOpenChange,
  editMode = false,
  editingTestFlow = null,
  moduleNames,
  testFlowNames
}: TestFlowCreatorDialogProps) {
  const [testFlowName, setTestFlowName] = useState("");
  const [description, setDescription] = useState("");

  // Module state
  const [isModuleDropdownOpen, setIsModuleDropdownOpen] = useState(false);
  const [moduleSearchTerm, setModuleSearchTerm] = useState("");
  const [selectedModule, setSelectedModule] = useState("");
  const [nameError, setNameError] = useState<string>("");
  const nameInputRef = useRef<HTMLInputElement>(null);
  const moduleDropdownRef = useRef<HTMLDivElement>(null);

  const [leftSelectedIds, setLeftSelectedIds] = useState<(number | string)[]>(
    []
  );
  const [rightSelectedIds, setRightSelectedIds] = useState<(number | string)[]>(
    []
  );
  const [selectedTestCases, setSelectedTestCases] = useState<TestCase[]>([]);

  const [leftSearchTerm, setLeftSearchTerm] = useState("");
  const [rightSearchTerm, setRightSearchTerm] = useState("");

  const [allTestCases, setAllTestCases] = useState<TestCase[]>([]);
  const [loading, setLoading] = useState(false);
  const [isCreatingUpdating, setIsCreatingUpdating] = useState(false);

  const [envDropdownOpen, setEnvDropdownOpen] = useState<
    Record<string, boolean>
  >({});
  const [envSearchTerm, setEnvSearchTerm] = useState<Record<string, string>>(
    {}
  );

  const [allTestModuleData, setAllTestModuleData] = useState([]);

  // const moduleNames = getModuleNamesGlobal();

  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [confirmingChange, setConfirmingChange] = useState<{
    testCaseId: number | string;
    newEnv: string;
  } | null>(null);

  const { toast } = useToast();

  useEffect(() => {
    fetchAllModuleData().then((data) => setAllTestModuleData(data));
    fetchConfigs().then((data) =>setEnvironmentArray(data));
    
  }, []);

  useEffect(() => {
    const style = document.createElement("style");
    style.innerHTML = `
      .checkbox-row {
        cursor: pointer;
      }
      .checkbox-row:hover {
        background-color: #f9fafb;
      }
      .success-toast {
        backdrop-filter: blur(8px);
        animation: slideInFromRight 0.3s ease-out, pulse 0.6s ease-in-out;
      }
      @keyframes slideInFromRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
      @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.02); }
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  useEffect(() => {
    if (open) {
      // fetchTestCases();
    }
  }, [open]);

  useEffect(() => {
    if (editMode && editingTestFlow && open) {
      setTestFlowName(editingTestFlow.name);
      setDescription(editingTestFlow.description || "");
      const timer = setTimeout(() => {
        const selectedCases: TestCase[] = editingTestFlow.testcases.map(
          (caseName, index) => {
            const foundCase = allTestCases.find((tc) => tc.name === caseName);
            return foundCase || { id: index + 1000, name: caseName };
          }
        );
        setSelectedTestCases(selectedCases);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [editMode, editingTestFlow, open, allTestCases]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        moduleDropdownRef.current &&
        !moduleDropdownRef.current.contains(event.target as Node)
      ) {
        setIsModuleDropdownOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    if (selectedModule && !isModuleDropdownOpen) {
      setModuleSearchTerm(selectedModule);
    }
  }, [selectedModule, isModuleDropdownOpen]);

  const fetchTestCases = (module: string) => {
    setLoading(true);
    if (module && allTestModuleData) {
      const moduleData = allTestModuleData[module];
      if (moduleData && Array.isArray(moduleData)) {
        const testCases: TestCase[] = moduleData.map(
          (item: any, index: number) => ({
            id: `${module}_${index + 1}`,
            name: item.SamplerProxyName,
            module: module,
            // You can add other properties from your sample data here if needed
          })
        );
        setAllTestCases(testCases);
      } else {
        setAllTestCases([]);
      }
    } else if (module === "" && allTestModuleData) {
      // Handle "All" case
      const allCases: TestCase[] = [];
      Object.keys(allTestModuleData).forEach((modName) => {
        const moduleData = allTestModuleData[modName];
        if (Array.isArray(moduleData)) {
          moduleData.forEach((item: any, index: number) => {
            allCases.push({
              id: `${modName}_${index + 1}`,
              name: item.SamplerProxyName,
              module: modName, // <--- Add module name here
            });
          });
        }
      });
      setAllTestCases(allCases);
    } else {
      setAllTestCases([]);
    }
    setLoading(false);
  };

  const setEnvironmentArray = (data: any) => {
    environments = [];
    data.map((config) => {
      environments.push(config.Environment)
    })
  }

  const fetchAllModuleData = async () => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/testapi/getalltestcases`
      );
      const jsonData = await response.json();
      return jsonData;
    } catch (error) {
      console.error("Error fetching module data:", error);
      return [];
    }
  };

  const handleModuleSelect = (module: string) => {
    if (module === "All") {
      setSelectedModule(""); // Set to empty string to indicate "All"
      setModuleSearchTerm("All");
      setIsModuleDropdownOpen(false);
      fetchTestCases(""); // Fetch all test cases
    } else {
      setSelectedModule(module);
      setModuleSearchTerm(module);
      setIsModuleDropdownOpen(false);
      fetchTestCases(module);
    }
  };

  const handleModuleInputChange = (value: string) => {
    setModuleSearchTerm(value);
    setIsModuleDropdownOpen(true);
    if (value === "") {
      setSelectedModule("");
      // fetchTestCases();
    }
  };

  //This filters the allTestCases to show only those that are not present in the selectedTestCases state(right side). This ensures no duplicates.
  const getAvailableTestCases = () => {
    return allTestCases.filter(
      (tc) => !selectedTestCases.some((sel) => sel.id === tc.id)
    );
  };

  const filteredAvailableTestCases = getAvailableTestCases().filter(
    (testCase) =>
      testCase.name.toLowerCase().includes(leftSearchTerm.toLowerCase())
  );

  const filteredSelectedTestCases = selectedTestCases.filter((testCase) =>
    testCase.name.toLowerCase().includes(rightSearchTerm.toLowerCase())
  );

  const areAllFilteredAvailableSelected = () => {
    return (
      filteredAvailableTestCases.length > 0 &&
      filteredAvailableTestCases.every((tc) => leftSelectedIds.includes(tc.id))
    );
  };

  const areAllFilteredSelectedSelected = () => {
    return (
      filteredSelectedTestCases.length > 0 &&
      filteredSelectedTestCases.every((tc) => rightSelectedIds.includes(tc.id))
    );
  };

  const handleLeftSelectAll = (checked: boolean) => {
    if (checked) {
      const filteredIds = filteredAvailableTestCases.map((tc) => tc.id);
      setLeftSelectedIds((prev) => [...new Set([...prev, ...filteredIds])]);
    } else {
      const filteredIds = filteredAvailableTestCases.map((tc) => tc.id);
      setLeftSelectedIds((prev) =>
        prev.filter((id) => !filteredIds.includes(id))
      );
    }
  };

  const handleRightSelectAll = (checked: boolean) => {
    if (checked) {
      const filteredIds = filteredSelectedTestCases.map((tc) => tc.id);
      setRightSelectedIds((prev) => [...new Set([...prev, ...filteredIds])]);
    } else {
      const filteredIds = filteredSelectedTestCases.map((tc) => tc.id);
      setRightSelectedIds((prev) =>
        prev.filter((id) => !filteredIds.includes(id))
      );
    }
  };

  const handleLeftTestCaseSelect = (id: number | string, checked: boolean) => {
    setLeftSelectedIds((prev) =>
      checked ? [...prev, id] : prev.filter((tcId) => tcId !== id)
    );
  };

  const handleRightTestCaseSelect = (id: number | string, checked: boolean) => {
    setRightSelectedIds((prev) =>
      checked ? [...prev, id] : prev.filter((tcId) => tcId !== id)
    );
  };

  const toggleLeftCheckbox = (id: number | string) => {
    setLeftSelectedIds((prev) =>
      prev.includes(id) ? prev.filter((tcId) => tcId !== id) : [...prev, id]
    );
  };

  const toggleRightCheckbox = (id: number | string) => {
    setRightSelectedIds((prev) =>
      prev.includes(id) ? prev.filter((tcId) => tcId !== id) : [...prev, id]
    );
  };

  const moveToSelected = () => {
    if (leftSelectedIds.length === 0) return;

    const casesToAdd = leftSelectedIds
      .map((id) => allTestCases.find((tc) => tc.id === id)!)
      .filter(Boolean)
      .filter((tc) => !selectedTestCases.some((sel) => sel.id === tc.id));

    const updated = [...selectedTestCases, ...casesToAdd];
    const newIds = casesToAdd.map((tc) => tc.id);

    setRightSelectedIds((prev) => [...new Set([...prev, ...newIds])]);
    setSelectedTestCases(updated);
    setLeftSelectedIds([]);
  };

  const removeFromSelected = () => {
    if (rightSelectedIds.length === 0) return;
    setSelectedTestCases((prev) =>
      prev.filter((tc) => !rightSelectedIds.includes(tc.id))
    );
    setRightSelectedIds([]);
  };

  const handleEnvironmentChange = (
    testCaseId: number | string,
    newEnv: string
  ) => {
    const isCurrentlySelected = rightSelectedIds.includes(testCaseId);

    if (isCurrentlySelected && rightSelectedIds.length > 1) {
      // If the changed test case is part of a multiple selection, update all selected test cases
      setSelectedTestCases((prev) =>
        prev.map((tc) =>
          rightSelectedIds.includes(tc.id) ? { ...tc, environment: newEnv } : tc
        )
      );
    } else {
      // Otherwise, update only the single test case
      setSelectedTestCases((prev) =>
        prev.map((tc) =>
          tc.id === testCaseId ? { ...tc, environment: newEnv } : tc
        )
      );
    }
  };

  const handleCreateOrUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!testFlowName.trim()) {
      alert("Test Flow Name is required");
      return;
    }
    if (selectedTestCases.length === 0) {
      alert("At least one test case must be selected");
      return;
    }

    if (selectedTestCases.some((tc) => !tc.environment)) {
      alert("Please select an environment for all selected test cases.");
      return;
    }

    setNameError("");
    if(isTestFlowExist(testFlowName)){
      setNameError("Test Flow name already exists.");
      nameInputRef.current?.focus();
      return;
    }

    setIsCreatingUpdating(true);

    try {
      if (editMode) {
        const testFlowData = {
          originalName: editingTestFlow?.name,
          name: testFlowName.trim(),
          description:
            description.trim() || `${testFlowName.trim()} description`,
          testcases: selectedTestCases.map((tc) => tc.name),
        };

        const testFlowResponse = await fetch("/api/test-flows", {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(testFlowData),
        });

        const testFlowResult = await testFlowResponse.json();

        if (!testFlowResponse.ok) {
          alert(
            `Failed to update test flow: ${
              testFlowResult.error || "Unknown error"
            }`
          );
          return;
        }

        toast({
          title: "Success",
          description: `Test flow "${testFlowName}" updated successfully!`,
          duration: 1500,
          style: {
            backgroundColor: "#00AB6A",
            borderColor: "#00966A",
            color: "white",
            borderRadius: "12px",
            boxShadow:
              "0 10px 25px rgba(0, 171, 106, 0.3), 0 4px 10px rgba(0, 0, 0, 0.1)",
            border: "1px solid #00966A",
          },
          className:
            "top-4 right-4 w-96 h-16 text-sm p-4 flex items-center gap-3 success-toast",
        });
      } else {
        const getSelectedTestCasesData = () => {
          return selectedTestCases
            .map((tc) => {
              const { id, environment } = tc;
              if (typeof id !== "string" || !id.includes("_")) {
                return null;
              }
              const [moduleName, testCaseIndexStr] = id.split("_");
              const testCaseIndex = parseInt(testCaseIndexStr, 10) - 1;

              const moduleData = allTestModuleData[moduleName];
              if (moduleData && moduleData[testCaseIndex]) {
                const testData = moduleData[testCaseIndex];
                return { ...testData, environment, moduleName };
              }
              return null;
            })
            .filter(Boolean);
        };

        const selectedTestCasesWithData = getSelectedTestCasesData();
        const testFlowData = setDataForCreateTestFlow(
          selectedTestCasesWithData
        );

        const response = await axios.post(
          "http://localhost:3001/api/testflowui/addtestflow",
          testFlowData,
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        // const response = await fetch("http://localhost:3001/api/testflowui/addtestflow", {
        //   method: "POST",
        //   headers: {
        //     "Content-Type": "application/json",
        //   },
        //   body: JSON.stringify(testFlowData),
        // });

        if (response.statusText != "OK") {
          throw new Error(`API error: ${response.status}`);
        }

        // const result = await response.json();

        // const featureFilesResponse = await fetch('/api/test-cases', {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify({
        //     testFlowName: testFlowName.trim(),
        //     selectedTestCases: selectedTestCasesWithData,
        //   }),
        // });

        // const featureFilesResult = await featureFilesResponse.json();

        // if (!featureFilesResponse.ok) {
        //   alert(`Failed to update feature files: ${featureFilesResult.error || 'Unknown error'}`);
        //   return;
        // }

        // // const testFlowData = {
        // //   name: testFlowName.trim(),
        // //   description: description.trim() || `${testFlowName.trim()} description`,
        // //   testcases: selectedTestCases.map(tc => tc.name)
        // // };

        // const testFlowResponse = await fetch('/api/test-flows', {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify(testFlowData),
        // });

        // const testFlowResult = await testFlowResponse.json();

        // if (!testFlowResponse.ok) {
        //   console.error('Failed to create test flow in JSON:', testFlowResult.error);
        //   alert(`Feature files updated successfully, but failed to save test flow configuration: ${testFlowResult.error || 'Unknown error'}`);
        // } else {
        //   let successMessage = "";
        //   if (featureFilesResult.updatedFiles === 0) {
        //     successMessage = `Test flow "${testFlowName}" created successfully! ${featureFilesResult.message}`;
        //   } else if (featureFilesResult.skippedFiles > 0) {
        //     successMessage = `Test flow "${testFlowName}" created successfully! ${featureFilesResult.message}`;
        //   } else {
        //     successMessage = `Test flow "${testFlowName}" created successfully! Updated ${featureFilesResult.updatedFiles} feature files.`;
        //   }

        const successMessage = `Test flow "${testFlowData.testFlowName}" created successfully!`;

        toast({
          title: "Success",
          description: successMessage,
          duration: 3000,
          style: {
            backgroundColor: "#00AB6A",
            borderColor: "#00966A",
            color: "white",
            borderRadius: "12px",
            boxShadow:
              "0 10px 25px rgba(0, 171, 106, 0.3), 0 4px 10px rgba(0, 0, 0, 0.1)",
            border: "1px solid #00966A",
          },
          className:
            "top-4 right-4 w-96 h-16 text-sm p-4 flex items-center gap-3 success-toast",
        });
      }

      onOpenChange(false);
      handleCancel();
    } catch (error) {
      console.error(
        `Error ${editMode ? "updating" : "creating"} test flow:`,
        error
      );
      alert(
        `Error ${
          editMode ? "updating" : "creating"
        } test flow. Please try again.`
      );
    } finally {
      setIsCreatingUpdating(false);
    }
  };

  const handleCancel = () => {
    setTestFlowName("");
    setDescription("");
    setSelectedTestCases([]);
    setLeftSelectedIds([]);
    setRightSelectedIds([]);
    setLeftSearchTerm("");
    setRightSearchTerm("");
    onOpenChange(false);
    setSelectedModule("");
    setModuleSearchTerm("");
  };

  const filteredModules = [
    "All",
    ...moduleNames.filter((m) =>
      m.toLowerCase().includes(moduleSearchTerm.toLowerCase())
    ),
  ];

  const setDataForCreateTestFlow = (dataArr) => {
    dataArr.map((data) => {});
    const testCasesData = dataArr.map((data) => ({
      Execute: rightSelectedIds.includes(data.moduleName + "_" + data.id)
        ? "Yes"
        : "No",
      SamplerProxyName: data.SamplerProxyName || "",
      SamplerProxyPath: data.SamplerProxyPath || "",
      Environment: data.environment || "",
      SamplerProxyMethod: data.SamplerProxyMethod?.toLocaleLowerCase() || "",
      HeaderParameter: data.HeaderParameter || "Content-Type= application/json",
      PathParameter: "",
      QueryParameter: data.QueryParameter || "",
      SamplerProxyBody: data.SamplerProxyBody
        ? JSON.stringify(data.SamplerProxyBody, null, 2)
        : "",
      SamplerProxyResponseCode: data.SamplerProxyResponseCode || "",
      ExportVariable: data.ExportVariable || "",
      ResponseValidation: data.ResponseValidation || "",
      Child:"",
      PostScript:"",
      Dependent: data.Dependent || "",
      Query: data.Query || "",
      DBExportVariables: data.DBExportVariables || "",
      CollectionName: data.CollectionName || "",
      Module: data.moduleName || "",
    }));
    const testFlowData = {
      testFlowName: testFlowName,
      testCases: testCasesData,
    };
    return testFlowData;
  };

  const handleConfirmEnvironmentChange = () => {
    if (confirmingChange) {
      handleEnvironmentChange(
        confirmingChange.testCaseId,
        confirmingChange.newEnv
      );
      if (
        rightSelectedIds.includes(confirmingChange.testCaseId) &&
        rightSelectedIds.length > 1
      ) {
        const newEnvSearchTerm = { ...envSearchTerm };
        rightSelectedIds.forEach((id) => {
          newEnvSearchTerm[id] = confirmingChange.newEnv;
        });
        setEnvSearchTerm(newEnvSearchTerm);
      } else {
        setEnvSearchTerm({
          ...envSearchTerm,
          [confirmingChange.testCaseId]: confirmingChange.newEnv,
        });
      }
    }
    setConfirmDialogOpen(false);
    setConfirmingChange(null);
  };

  const onDragEnd = (result: any) => {
    if (!result.destination) {
      return;
    }

    const reorderedTestCases = Array.from(selectedTestCases);
    const [removed] = reorderedTestCases.splice(result.source.index, 1);
    reorderedTestCases.splice(result.destination.index, 0, removed);

    setSelectedTestCases(reorderedTestCases);
  };

  const isTestFlowExist = (name) => {
    if(testFlowNames.includes(name)){
      return true
    }
    else{
      return false
    }
  }

  const isFormValid =
    testFlowName.trim() !== "" &&
    selectedTestCases.length > 0 &&
    selectedTestCases.every((tc) => tc.environment);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl h-[90vh] flex flex-col p-0">
        <TooltipProvider>
          {/* Confirmation Dialog */}
          <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Confirm Environment Change</DialogTitle>
                <DialogDescription>
                  Are you sure you want to update the environment to &quot;
                  {confirmingChange?.newEnv}&quot; for all selected test cases?
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button
                  variant="outline"
                  className="cursor-pointer"
                  onClick={() => {
                    setConfirmDialogOpen(false);
                    setConfirmingChange(null);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  className="bg-[#15537C] hover:bg-[#15537C] hover:brightness-110 cursor-pointer"
                  onClick={handleConfirmEnvironmentChange}
                >
                  Confirm
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          <DialogHeader className="p-4">
            <DialogTitle>
              {editMode
                ? `Edit Test Flow: ${editingTestFlow?.name}`
                : "Create Test Flow"}
            </DialogTitle>
          </DialogHeader>

          <div className="flex-grow overflow-y-auto px-4">
            <form
              id="test-flow-form"
              onSubmit={handleCreateOrUpdate}
              className="space-y-6"
            >
              <div className="relative mt-5">
                <label className="absolute -top-3 left-2 bg-white px-1 text-xs font-medium text-gray-700">
                  Test Flow Name <span className="text-red-500">*</span>
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="h-2.5 w-2.5 inline-block ml-1" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{TestFlowUILabels["Test Flow Name"]}</p>
                    </TooltipContent>
                  </Tooltip>
                </label>
                <input
                  type="text"
                  ref={nameInputRef}
                  value={testFlowName}
                  onChange={(e) => setTestFlowName(e.target.value)}
                  placeholder="Provide a Test Flow Name"
                  className={`w-full border border-gray-300 rounded p-2.5 pt-3.5 ${nameError ? 'border-red-500' : 'border-gray-300'}`}
                />
                {nameError && <p className="text-red-500 text-xs mt-1">{nameError}</p>}
              </div>

              <div className="relative mt-5">
                <label className="absolute -top-3 left-2 bg-white px-1 text-xs font-medium text-gray-700">
                  Test Flow Description
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="h-2.5 w-2.5 inline-block ml-1" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{TestFlowUILabels["Test Flow Description"]}</p>
                    </TooltipContent>
                  </Tooltip>
                </label>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Provide a Test Flow Description"
                  className="w-full border border-gray-300 rounded p-2.5 pt-3.5 resize-none"
                  rows={1}
                />
              </div>

              <div className="relative mt-5" ref={moduleDropdownRef}>
                <label className="absolute -top-2.5 left-3 bg-white px-1 text-xs font-medium text-gray-700 z-10">
                  Module
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="h-2.5 w-2.5 inline-block ml-1" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{TestFlowUILabels["Module"]}</p>
                    </TooltipContent>
                  </Tooltip>
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={moduleSearchTerm}
                    onChange={(e) => handleModuleInputChange(e.target.value)}
                    onFocus={() => {
                      if (!isModuleDropdownOpen) {
                        setModuleSearchTerm("");
                      }
                      setIsModuleDropdownOpen(true);
                    }}
                    placeholder="Type to search or select a module"
                    className="w-full border border-gray-300 rounded p-2.5 pt-3.5 pr-10"
                  />
                  <ChevronDown
                    className={`absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 cursor-pointer transition-transform ${
                      isModuleDropdownOpen ? "rotate-180" : ""
                    }`}
                    onClick={() => {
                      const nextIsOpen = !isModuleDropdownOpen;
                      if (nextIsOpen) {
                        setModuleSearchTerm("");
                      }
                      setIsModuleDropdownOpen(nextIsOpen);
                    }}
                  />
                  {isModuleDropdownOpen && (
                    <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                      {filteredModules.length > 0 ? (
                        filteredModules.map((module) => (
                          <div
                            key={module}
                            className="px-3 py-2 hover:bg-blue-50 cursor-pointer text-sm"
                            onClick={() => handleModuleSelect(module)}
                          >
                            {module}
                          </div>
                        ))
                      ) : (
                        <div className="px-3 py-2 text-sm text-gray-500">
                          No matching modules found.
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              <div className="flex gap-4 pb-4">
                <div className="w-1/2 min-w-[46%]">
                  <p className="font-medium mb-2">
                    List of Test Cases (
                    {loading ? "..." : filteredAvailableTestCases.length})
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-2.5 w-2.5 inline-block ml-1" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{TestFlowUILabels["List of Test Cases"]}</p>
                      </TooltipContent>
                    </Tooltip>
                  </p>
                  <div className="border border-[#A9ACAE] rounded overflow-hidden h-64">
                    <div className="h-full overflow-auto">
                      <div className="p-2 border-b border-[#A9ACAE] top-0 bg-white z-10">
                        <div className="flex items-center justify-between">
                          <div
                            className="flex items-center gap-2 checkbox-row px-1 py-0.5 rounded"
                            onClick={() =>
                              handleLeftSelectAll(
                                !areAllFilteredAvailableSelected()
                              )
                            }
                          >
                            <input
                              type="checkbox"
                              checked={areAllFilteredAvailableSelected()}
                              onChange={(e) => {
                                e.stopPropagation();
                                handleLeftSelectAll(e.target.checked);
                              }}
                              onClick={(e) => e.stopPropagation()}
                              disabled={loading}
                              className="mr-2 h-4 w-4 accent-blue-900 rounded text-white focus:ring-0"
                            />
                            <span className="text-sm font-medium">
                              Select All
                            </span>
                          </div>
                          <div className="relative w-50">
                            <Input
                              placeholder="Search Testcase Name"
                              value={leftSearchTerm}
                              onChange={(e) =>
                                setLeftSearchTerm(e.target.value)
                              }
                              className="pr-8 h-8 border border-[#A9ACAE]"
                              disabled={loading}
                            />
                            <Search className="h-4 w-4 absolute right-3 top-2 text-gray-400" />
                          </div>
                        </div>
                      </div>
                      <div className="p-2 space-y-2">
                        {loading ? (
                          <div className="text-center text-gray-500">
                            Loading test cases...
                          </div>
                        ) : filteredAvailableTestCases.length === 0 ? (
                          <div className="text-center text-gray-500">
                            No test cases found
                          </div>
                        ) : (
                          filteredAvailableTestCases.map((testCase) => {
                            const isChecked = leftSelectedIds.includes(
                              testCase.id
                            );
                            return (
                              <div
                                key={testCase.id}
                                className="flex items-center gap-2 p-1 checkbox-row"
                                onClick={() => toggleLeftCheckbox(testCase.id)}
                              >
                                <input
                                  type="checkbox"
                                  checked={isChecked}
                                  onChange={(e) => {
                                    e.stopPropagation();
                                    handleLeftTestCaseSelect(
                                      testCase.id,
                                      e.target.checked
                                    );
                                  }}
                                  onClick={(e) => e.stopPropagation()}
                                  className="mr-2 h-4 w-4 accent-blue-900 rounded text-white focus:ring-0"
                                />
                                <span className="text-sm">{testCase.name}</span>
                              </div>
                            );
                          })
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col justify-center gap-2">
                  <button
                    onClick={moveToSelected}
                    disabled={leftSelectedIds.length === 0}
                    className="bg-[#15537C] text-white w-12 h-8 rounded-md flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:brightness-110"
                  >
                    <ChevronRight size={20} />
                  </button>
                  <button
                    onClick={removeFromSelected}
                    disabled={rightSelectedIds.length === 0}
                    className="bg-white border border-[#A9ACAE] w-12 h-8 rounded-md flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    <ChevronLeft size={20} color="#15537C" />
                  </button>
                </div>

                <div className="w-1/2 min-w-[46%]">
                  <p className="font-medium mb-2">
                    Selected Test Cases ({filteredSelectedTestCases.length})
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-2.5 w-2.5 inline-block ml-1" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{TestFlowUILabels["Selected Test Cases"]}</p>
                      </TooltipContent>
                    </Tooltip>
                  </p>
                  <div className="border border-[#A9ACAE] rounded h-64 overflow-hidden">
                    <div className="h-full overflow-auto">
                      <div className="p-2 border-b border-[#A9ACAE] top-0 bg-white z-10">
                        <div className="flex items-center justify-between">
                          <div
                            className="flex items-center gap-2 checkbox-row px-1 py-0.5 rounded"
                            onClick={() =>
                              handleRightSelectAll(
                                !areAllFilteredSelectedSelected()
                              )
                            }
                          >
                            <input
                              type="checkbox"
                              checked={areAllFilteredSelectedSelected()}
                              onChange={(e) => {
                                e.stopPropagation();
                                handleRightSelectAll(e.target.checked);
                              }}
                              onClick={(e) => e.stopPropagation()}
                              className="mr-2 h-4 w-4 accent-blue-900 rounded text-white focus:ring-0"
                            />
                            <span className="text-sm font-medium">
                              Select All
                            </span>
                          </div>
                          <div className="relative w-50">
                            <Input
                              placeholder="Search Testcase Name"
                              value={rightSearchTerm}
                              onChange={(e) =>
                                setRightSearchTerm(e.target.value)
                              }
                              className="pr-8 h-8 border border-[#A9ACAE]"
                            />
                            <Search className="h-4 w-4 absolute right-3 top-2 text-gray-400" />
                          </div>
                        </div>
                      </div>
                      <DragDropContext onDragEnd={onDragEnd}>
                        <Droppable droppableId="selected-test-cases">
                          {(provided) => (
                            <div
                              className="p-2 space-y-2"
                              {...provided.droppableProps}
                              ref={provided.innerRef}
                            >
                              {filteredSelectedTestCases.map(
                                (testCase, index) => {
                                  const isChecked = rightSelectedIds.includes(
                                    testCase.id
                                  );
                                  return (
                                    <PortalAwareDraggable
                                      key={testCase.id}
                                      draggableId={String(testCase.id)}
                                      index={index}
                                    >
                                      {(provided, snapshot) => (
                                        <div
                                          className={`flex items-center justify-between gap-2 p-1 transition-all duration-150 w-[100%]
          ${snapshot.isDragging ? "bg-gray-100 shadow-md" : ""}`}
                                        >
                                          <div
                                            className="flex items-center gap-2 min-w-0 flex-1"
                                            onClick={() =>
                                              toggleRightCheckbox(testCase.id)
                                            }
                                          >
                                            <input
                                              type="checkbox"
                                              checked={isChecked}
                                              onChange={(e) => {
                                                e.stopPropagation();
                                                handleRightTestCaseSelect(
                                                  testCase.id,
                                                  e.target.checked
                                                );
                                              }}
                                              onClick={(e) =>
                                                e.stopPropagation()
                                              }
                                              className="mr-2 h-4 w-4 accent-blue-900 rounded text-white focus:ring-0"
                                            />
                                            <div
                                              className="text-sm truncate cursor-pointer"
                                              title={testCase.name}
                                            >
                                              {testCase.module
                                                ? `${testCase.module} - ${testCase.name}`
                                                : testCase.name}
                                            </div>
                                          </div>
                                          <div className="relative w-[30%]">
                                            <Input
                                              value={
                                                envSearchTerm[testCase.id] ??
                                                testCase.environment ??
                                                ""
                                              }
                                              placeholder="Select Env"
                                              onChange={(e) =>
                                                setEnvSearchTerm({
                                                  ...envSearchTerm,
                                                  [testCase.id]: e.target.value,
                                                })
                                              }
                                              onFocus={() => {
                                                if (!envDropdownOpen[testCase.id]) {
                                                  setEnvSearchTerm({
                                                    ...envSearchTerm,
                                                    [testCase.id]: "",
                                                  });
                                                }
                                                setEnvDropdownOpen({
                                                  ...envDropdownOpen,
                                                  [testCase.id]: true,
                                                });
                                              }}
                                              onBlur={() =>
                                                setTimeout(
                                                  () => {
                                                    setEnvDropdownOpen({
                                                      ...envDropdownOpen,
                                                      [testCase.id]: false,
                                                    });
                                                    // If the current search term is not the selected environment, revert it
                                                    if (envSearchTerm[testCase.id] !== testCase.environment) {
                                                      setEnvSearchTerm((prev) => ({
                                                        ...prev,
                                                        [testCase.id]: testCase.environment || "", // Revert to actual environment or empty string
                                                      }));
                                                    }
                                                  },
                                                  150
                                                )
                                              }
                                              className="h-8 pr-8"
                                            />
                                            <ChevronDown
                                              className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 cursor-pointer"
                                              onClick={() => {
                                                const nextIsOpen = !envDropdownOpen[testCase.id];
                                                if (nextIsOpen) {
                                                  setEnvSearchTerm({
                                                    ...envSearchTerm,
                                                    [testCase.id]: "",
                                                  });
                                                }
                                                setEnvDropdownOpen({
                                                  ...envDropdownOpen,
                                                  [testCase.id]: nextIsOpen,
                                                });
                                              }}
                                            />
                                            {envDropdownOpen[testCase.id] && (
                                              <div className="absolute z-10 w-full bg-white border rounded-md mt-1 shadow-lg">
                                                {environments
                                                  .filter((env) =>
                                                    env
                                                      .toLowerCase()
                                                      .includes(
                                                        (
                                                          envSearchTerm[
                                                            testCase.id
                                                          ] ?? ""
                                                        ).toLowerCase()
                                                      )
                                                  )
                                                  .map((env) => (
                                                    <div
                                                      key={env}
                                                      className="p-2 text-sm hover:bg-gray-100 cursor-pointer"
                                                      onMouseDown={() => {
                                                        setConfirmingChange({
                                                          testCaseId:
                                                            testCase.id,
                                                          newEnv: env,
                                                        });
                                                        setConfirmDialogOpen(
                                                          true
                                                        );
                                                        setEnvDropdownOpen({
                                                          ...envDropdownOpen,
                                                          [testCase.id]: false,
                                                        });
                                                      }}
                                                    >
                                                      {env}
                                                    </div>
                                                  ))}
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      )}
                                    </PortalAwareDraggable>
                                  );
                                }
                              )}
                              {provided.placeholder}
                            </div>
                          )}
                        </Droppable>
                      </DragDropContext>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>

          <DialogFooter className="flex justify-end gap-4 p-4 border-t border-gray-300">
            <Button
              type="button"
              onClick={handleCancel}
              disabled={isCreatingUpdating}
              className="px-5 py-2.5 border border-gray-300 rounded bg-white text-gray-700 font-medium hover:bg-gray-100 transition-colors duration-200"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              form="test-flow-form"
              disabled={loading || isCreatingUpdating || !isFormValid}
              className="px-5 py-2.5 bg-green-600 hover:bg-green-700 text-white rounded font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              {isCreatingUpdating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {editMode ? "Updating..." : "Creating..."}
                </>
              ) : editMode ? (
                "Update"
              ) : (
                "Create"
              )}
            </Button>
          </DialogFooter>
        </TooltipProvider>
      </DialogContent>
    </Dialog>
  );
}
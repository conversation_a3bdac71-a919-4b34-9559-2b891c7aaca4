import puppeteer from 'puppeteer';
import { storage } from '../storage';
import { RecordedAction } from '@shared/types';
import fs from 'fs';
import path from 'path';
import { getCurrentProjectPath } from '../setCurrentProjectPath/route';

export class RecorderService {
  private activeSessions: Map<number, { browser: any, page: any }>;
  private locatorMap: Map<string, string>;
  private sessionTitles: Map<number, string>;
  
  constructor() {
    this.activeSessions = new Map();
    this.locatorMap = new Map();
    this.sessionTitles = new Map();
  }

  async startRecording(sessionId: number, url: string, browserType: string): Promise<number> {
    try {
      console.log(`Starting recording session ${sessionId} for URL: ${url}`);

      const isCloudEnvironment = process.env.REPL_ID || process.env.NODE_ENV === 'production';

      const browser = await puppeteer.launch({
        headless: isCloudEnvironment ? true : false,
        defaultViewport: { width: 1280, height: 720 },
        args: [
          '--window-size=1280,720',
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-gpu',
          '--no-zygote',
          '--deterministic-fetch'
        ],
        timeout: 60000
      });

      const [page] = await browser.pages();

      await page.setDefaultNavigationTimeout(60000);
      await this.setupEventListeners(page, sessionId);

      try {
        await storage.addRecordingAction(sessionId, {
          action: 'navigate',
          url:page.url(),
          timestamp: new Date().toISOString(),
       
        });
        await page.goto(url, {
          waitUntil: ['load', 'domcontentloaded', 'networkidle2'],
          timeout: 60000
        });

        const rawTitle = await page.title();
        const sanitizedTitle = rawTitle
          .replace(/[<>:"/\\|?*\x00-\x1F]/g, '')
          .replace(/\s+/g, '_');
        this.sessionTitles.set(sessionId, sanitizedTitle);
      } catch (navError) {
        console.warn(`Navigation warning for session ${sessionId}:`, navError);
      }

      this.activeSessions.set(sessionId, { browser, page });
      return sessionId;
    } catch (error) {
      console.error('Error starting recording:', error);
      throw error;
    }
  }

  async stopRecording(sessionId: number): Promise<void> {
    console.log(`Stopping recording session ${sessionId}`);

    const session = this.activeSessions.get(sessionId);
    if (!session) {
      console.warn(`No active recording session found for ID: ${sessionId}`);
      return;
    }

    try {
      const { browser, page } = session;

      if (page && !page.isClosed()) {
        console.log(`Closing page for session ${sessionId}`);
        await page.close().catch(e => console.warn(`Error closing page: ${e.message}`));
      }

      if (browser) {
        console.log(`Closing browser for session ${sessionId}`);
        await browser.close().catch(e => console.warn(`Error closing browser: ${e.message}`));
      }

      this.activeSessions.delete(sessionId);
      this.sessionTitles.delete(sessionId);
      this.locatorMap.clear();
      console.log(`Recording session ${sessionId} stopped successfully`);
    } catch (error) {
      console.error(`Error stopping recording session ${sessionId}:`, error);
      this.activeSessions.delete(sessionId);
      this.sessionTitles.delete(sessionId);
    }
  }


  private async setupEventListeners(page: any, sessionId: number): Promise<void> {
  

    console.log(`Setting up event listeners for session ${sessionId}`);
    await page.exposeFunction('recordClick', async (selector: string, label: string) => {
   
      console.table([{ Action: 'Click', Label: label, XPath: selector }]);
    
      console.log(`from server/services/recorder.service.ts: recordClick`);
      // CREATE CLEANER KEY FORMAT
      const cleanKey = this.createReadableKey(label, selector);
      const cleanXPath = this.createReadableXPath(selector);
      
      this.locatorMap.set(label, selector);
      label = label + "_" + selector.substring(selector.indexOf("@") + 1, selector.indexOf("=")) + "_xpath";
      //await this.writeLocatorsToFile(sessionId);
   try{
      var title = await page.title();
   } catch (error) {
      console.error(`Error getting page title: ${error}`);
      title =storage.currenttitle || 'Untitled';
   }
      const action: RecordedAction = {
        action: 'click',
        selector,
        url:page.url(),
        title: title,
        label,
        
        timestamp: new Date().toISOString()
      };      
      await storage.addRecordingAction(sessionId, action);
      await this.writeLocatorsToFile(sessionId);  
    });

    await page.exposeFunction('recordInput', async (selector: string, value: string, label: string) => {
      console.table([{ Action: 'Input', Label: label, XPath: selector, Value: value }]);
      
      // CREATE CLEANER KEY FORMAT
      const cleanKey = this.createReadableKey(label, selector);
      const cleanXPath = this.createReadableXPath(selector);
      
      this.locatorMap.set(label, selector);
      label = label + "_" + selector.substring(selector.indexOf("@") + 1, selector.indexOf("=")) + "_xpath";
      

      const action: RecordedAction = {
        action: 'type',
        selector,
        url:page.url(),
         title: await page.title(),
        value,
        label,
        timestamp: new Date().toISOString()
      };
      await storage.addRecordingAction(sessionId, action);
      await this.writeLocatorsToFile(sessionId);
    });

    await page.exposeFunction('recordScroll', async (scrollX: number, scrollY: number, scrollDirection: string) => {
      console.table([{ Action: 'Scroll', Direction: scrollDirection, X: scrollX, Y: scrollY }]);
      var title=page.title();
      const action: RecordedAction = {
        action: 'scroll',
        scrollX,
        scrollY,
         title: await page.title(),
        scrollDirection,
        timestamp: new Date().toISOString()
      };
      await storage.addRecordingAction(sessionId, action);
    });

    await page.exposeFunction('recordRightClick', async (selector: string, label: string) => {
      console.table([{ Action: 'Right Click', Label: label, XPath: selector }]);
      var title=page.title();
      // CREATE CLEANER KEY FORMAT
      const cleanKey = this.createReadableKey(label, selector);
      const cleanXPath = this.createReadableXPath(selector);
      
      this.locatorMap.set(cleanKey, cleanXPath);
      await this.writeLocatorsToFile(sessionId);

      const action: RecordedAction = {
        action: 'rightclick',
        selector,
         title: await page.title(),
        label,
        timestamp: new Date().toISOString()
      };
      await storage.addRecordingAction(sessionId, action);
    });

    await page.exposeFunction('recordAssertion', async (selector: string, label: string, assertionText: string, assertionType: string) => {
      console.table([{ Action: 'Assertion', Label: label, XPath: selector, Text: assertionText, Type: assertionType }]);
      var title=page.title();
      // CREATE CLEANER KEY FORMAT
      const cleanKey = this.createReadableKey(label, selector);
      const cleanXPath = this.createReadableXPath(selector);
      
      this.locatorMap.set(label, selector);
       label = label + "_" + selector.substring(selector.indexOf("@") + 1, selector.indexOf("=")) + "_xpath";
     
      const action: RecordedAction = {
        action: 'assertion',
        selector,
        label,
        url:page.url(),
         title: await page.title(),
        assertionText,
        assertionType,
        timestamp: new Date().toISOString()
      };
      await storage.addRecordingAction(sessionId, action);
      await this.writeLocatorsToFile(sessionId);
     
    });

    await page.evaluateOnNewDocument(new Function(`
      function normalize(text) {
        return text.trim().replace(/\\s+/g, '').toLowerCase();
      }

      function getReadableLabel(element) {
        try {
          if (element.id) {
            var labelElem = document.querySelector('label[for="' + element.id + '"]');
            if (labelElem && labelElem.textContent && labelElem.textContent.trim()) {
              return normalize(labelElem.textContent);
            }
          }

          var attrs = ['aria-label', 'placeholder', 'name', 'title','id'];
          for (var i = 0; i < attrs.length; i++) {
            var val = element.getAttribute(attrs[i]);
            if (val && val.trim()) {
              return normalize(val);
            }
          }

          var wrapperLabel = element.closest('label');
          if (wrapperLabel && wrapperLabel.textContent && wrapperLabel.textContent.trim()) {
            return normalize(wrapperLabel.textContent);
          }

          var parent = element.parentElement;
          while (parent && parent !== document.body) {
            var text = parent.textContent && parent.textContent.trim();
            if (text && text.length <= 50) {
              return normalize(text);
            }
            parent = parent.parentElement;
          }

          return element.tagName.toLowerCase() + '-field';
        } catch (e) {
          return element.tagName.toLowerCase() + '-field';
        }
      }

      function getUniqueXPath(element) {
        // Strategy 1: Try ID first (most reliable)
        if (element.id) {
          return '//*[@id="' + element.id + '"]';
        }

        // Strategy 2: Try single attributes
        var ATTRS = ['name', 'data-testid', 'data-test', 'data-cy', 'aria-label', 'placeholder', 'type', 'role', 'title'];
        for (var i = 0; i < ATTRS.length; i++) {
          var val = element.getAttribute(ATTRS[i]);
          if (val && val.trim()) {
            var tag = element.tagName.toLowerCase();
            var xpath = '//' + tag + '[@' + ATTRS[i] + '="' + val + '"]';
            if (isUniqueXPath(xpath, element)) return xpath;
          }
        }

        // Strategy 3: Try class-based selectors (single class)
        if (element.className && typeof element.className === 'string') {
          var classes = element.className.trim().split(/\\s+/);
          for (var j = 0; j < classes.length; j++) {
            if (classes[j]) {
              var tag = element.tagName.toLowerCase();
              var xpath = '//' + tag + '[@class="' + classes[j] + '"]';
              if (isUniqueXPath(xpath, element)) return xpath;
              
              // Try contains for class
              xpath = '//' + tag + '[contains(@class, "' + classes[j] + '")]';
              if (isUniqueXPath(xpath, element)) return xpath;
            }
          }
        }

        // Strategy 4: Try text content (for elements with unique text)
        var text = (element.textContent || element.innerText || '').trim();
        if (text && text.length > 0 && text.length < 50) {
          var tag = element.tagName.toLowerCase();
          var xpath = '//' + tag + '[text()="' + text.replace(/"/g, '\\\\"') + '"]';
          if (isUniqueXPath(xpath, element)) return xpath;
          
          // Try contains for partial text match
          xpath = '//' + tag + '[contains(text(), "' + text.substring(0, 20).replace(/"/g, '\\\\"') + '")]';
          if (isUniqueXPath(xpath, element)) return xpath;
        }

        // Strategy 5: Try combination of attributes
        var attrs = [];
        var COMBO_ATTRS = ['name', 'type', 'class', 'role'];
        for (var k = 0; k < COMBO_ATTRS.length; k++) {
          var val = element.getAttribute(COMBO_ATTRS[k]);
          if (val && val.trim()) {
            attrs.push('@' + COMBO_ATTRS[k] + '="' + val + '"');
          }
        }
        
        if (attrs.length >= 2) {
          var tag = element.tagName.toLowerCase();
          var xpath = '//' + tag + '[' + attrs.slice(0, 2).join(' and ') + ']';
          if (isUniqueXPath(xpath, element)) return xpath;
        }

        // Strategy 6: Try using parent element with attribute + child position
        var parent = element.parentElement;
        if (parent) {
          // Try parent with ID
          if (parent.id) {
            var childIndex = getChildIndex(element);
            var tag = element.tagName.toLowerCase();
            var xpath = '//*[@id="' + parent.id + '"]/' + tag + '[' + childIndex + ']';
            if (isUniqueXPath(xpath, element)) return xpath;
          }

          // Try parent with class
          if (parent.className && typeof parent.className === 'string') {
            var parentClasses = parent.className.trim().split(/\\s+/);
            for (var m = 0; m < parentClasses.length; m++) {
              if (parentClasses[m]) {
                var childIndex = getChildIndex(element);
                var tag = element.tagName.toLowerCase();
                var parentTag = parent.tagName.toLowerCase();
                var xpath = '//' + parentTag + '[contains(@class, "' + parentClasses[m] + '")]/' + tag + '[' + childIndex + ']';
                if (isUniqueXPath(xpath, element)) return xpath;
              }
            }
          }
        }

        // Strategy 7: Try using form context (for form elements)
        if (element.form) {
          var form = element.form;
          if (form.id || form.name || form.className) {
            var formSelector = '';
            if (form.id) {
              formSelector = '//form[@id="' + form.id + '"]';
            } else if (form.name) {
              formSelector = '//form[@name="' + form.name + '"]';
            } else if (form.className) {
              var formClass = form.className.trim().split(/\\s+/)[0];
              formSelector = '//form[contains(@class, "' + formClass + '")]';
            }
            
            if (formSelector) {
              var tag = element.tagName.toLowerCase();
              var xpath = formSelector + '//' + tag;
              
              // Add any available attribute
              for (var n = 0; n < ATTRS.length; n++) {
                var val = element.getAttribute(ATTRS[n]);
                if (val) {
                  xpath += '[@' + ATTRS[n] + '="' + val + '"]';
                  if (isUniqueXPath(xpath, element)) return xpath;
                  break;
                }
              }
            }
          }
        }

        // Strategy 8: Try using landmark elements (nav, header, footer, main)
        var landmark = element.closest('nav, header, footer, main, aside, section[id], div[id]');
        if (landmark && landmark !== element) {
          var landmarkSelector = '';
          if (landmark.id) {
            landmarkSelector = '//' + landmark.tagName.toLowerCase() + '[@id="' + landmark.id + '"]';
          } else if (landmark.tagName.toLowerCase() !== 'div') {
            landmarkSelector = '//' + landmark.tagName.toLowerCase();
          }
          
          if (landmarkSelector) {
            var tag = element.tagName.toLowerCase();
            var xpath = landmarkSelector + '//' + tag;
            
            // Add distinguishing attribute if available
            for (var p = 0; p < ATTRS.length; p++) {
              var val = element.getAttribute(ATTRS[p]);
              if (val) {
                xpath += '[@' + ATTRS[p] + '="' + val + '"]';
                if (isUniqueXPath(xpath, element)) return xpath;
                break;
              }
            }
          }
        }

        // Strategy 9: Last resort - create shortest possible absolute path
        return getShortestAbsolutePath(element);
      }

      // Helper function to test if XPath is unique
      function isUniqueXPath(xpath, element) {
        try {
          var result = document.evaluate(xpath, document, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
          return result.snapshotLength === 1 && result.snapshotItem(0) === element;
        } catch (e) {
          return false;
        }
      }

      // Helper function to get child index
      function getChildIndex(element) {
        var index = 1;
        var sibling = element.previousElementSibling;
        while (sibling) {
          if (sibling.tagName === element.tagName) {
            index++;
          }
          sibling = sibling.previousElementSibling;
        }
        return index;
      }

      // Helper function to create shortest absolute path by finding a good starting point
      function getShortestAbsolutePath(element) {
        // Try to find a parent with ID to start from
        var current = element;
        var pathParts = [];
        
        while (current && current.nodeType === 1) {
          var tag = current.tagName.toLowerCase();
          var index = getChildIndex(current);
          pathParts.unshift(tag + '[' + index + ']');
          
          // If we find a parent with ID, start from there
          if (current.parentElement && current.parentElement.id) {
            return '//*[@id="' + current.parentElement.id + '"]/' + pathParts.join('/');
          }
          
          // If we find a parent with a unique class, start from there
          if (current.parentElement && current.parentElement.className) {
            var parentClass = current.parentElement.className.trim().split(/\\s+/)[0];
            if (parentClass) {
              var parentTag = current.parentElement.tagName.toLowerCase();
              var xpath = '//' + parentTag + '[contains(@class, "' + parentClass + '")]/' + pathParts.join('/');
              if (isUniqueXPath(xpath, element)) {
                return xpath;
              }
            }
          }
          
          current = current.parentElement;
          
          // Limit depth to avoid extremely long paths
          if (pathParts.length > 8) break;
        }
        
        // Fallback to traditional absolute path
        return '/' + pathParts.join('/');
      }

      function getElementText(element) {
        try {
          var text = element.textContent || element.innerText || '';
          
          if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
            text = element.value || element.placeholder || '';
          }
          
          if (element.tagName === 'IMG') {
            text = element.alt || element.title || '';
          }
          
          return text.trim().replace(/\\s+/g, ' ').substring(0, 200);
        } catch (e) {
          return '';
        }
      }

      function determineAssertionType(element) {
        try {
          var tagName = element.tagName.toLowerCase();
          
          if (tagName === 'h1' || tagName === 'h2' || tagName === 'h3' || 
              tagName === 'h4' || tagName === 'h5' || tagName === 'h6') {
            return 'heading';
          }
          
          if (tagName === 'p' || tagName === 'span' || tagName === 'div') {
            return 'text';
          }
          
          if (tagName === 'button' || tagName === 'a') {
            return 'clickable';
          }
          
          if (tagName === 'input' || tagName === 'textarea') {
            return 'input';
          }
          
          if (tagName === 'img') {
            return 'image';
          }
          
          if (element.getAttribute('role') === 'alert' || 
              element.className.includes('alert') ||
              element.className.includes('error') ||
              element.className.includes('success')) {
            return 'alert';
          }
          
          return 'element';
        } catch (e) {
          return 'element';
        }
      }

      function highlightAssertionElement(element) {
        var originalStyle = element.style.cssText;
        element.style.border = '2px solid #00ff00';
        element.style.backgroundColor = 'rgba(0, 255, 0, 0.1)';
        
        setTimeout(() => {
          element.style.cssText = originalStyle;
        }, 1000);
      }

// add assertion on hover+ press a

// Track the element under mouse
document.addEventListener('mouseover', function (event) {
  hoveredElement = event.target;
});

document.addEventListener('mouseout', function (event) {
  // Optional: clear on leaving element
  hoveredElement = null;
});

// Listen for "a" key press to assert hovered element
document.addEventListener('keydown', function (event) {
  if (event.altKey && hoveredElement) {
    try {
      var el = hoveredElement;
      var xpath = getUniqueXPath(el);
      var label = getReadableLabel(el);
      var assertionText = getElementText(el);
      var assertionType = determineAssertionType(el);

      highlightAssertionElement(el);

      if (window.recordAssertion) {
        window.recordAssertion(xpath, label, assertionText, assertionType);
      }

      event.preventDefault();
    } catch (err) {
      console.error('[DEBUG] Assertion error:', err);
    }
  }
});


      document.addEventListener('click', function (event) {
        var el = event.target;
        try {
          var xpath = getUniqueXPath(el);
          var label = getReadableLabel(el);
          
          if (event.altKey) {
            var assertionText = getElementText(el);
            var assertionType = determineAssertionType(el);
            
            highlightAssertionElement(el);
            
            if (window.recordAssertion) {
              window.recordAssertion(xpath, label, assertionText, assertionType);
            }
            
            event.preventDefault();
          } else {
            if (window.recordClick) {
              window.recordClick(xpath, label);
            }
          }
        } catch (err) {
          console.error('[DEBUG] Click error:', err);
        }
      }, true);

      document.addEventListener('change', function (event) {
        var el = event.target;
        if (el && (
  (el.tagName === 'INPUT' && el.type !== 'radio' && el.type !== 'checkbox') ||
  el.tagName === 'TEXTAREA' ||
  el.tagName === 'SELECT'
)) {
          try {
            var xpath = getUniqueXPath(el);
            var label = getReadableLabel(el);
            var value = el.value;
            if (window.recordInput) window.recordInput(xpath, value, label);
          } catch (err) {
            console.error('[DEBUG] Input error:', err);
          }
        }
      }, true);

      let lastScrollTime = 0;
      let scrollTimeout;

      document.addEventListener('scroll', function(event) {
        const now = Date.now();
        if (now - lastScrollTime < 250) return;
        lastScrollTime = now;
        
        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(() => {
          try {
            const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
            const scrollY = window.pageYOffset || document.documentElement.scrollTop;
            
            let direction = 'down';
            if (scrollY < (window.lastScrollY || 0)) {
              direction = 'up';
            } else if (scrollX > (window.lastScrollX || 0)) {
              direction = 'right';
            } else if (scrollX < (window.lastScrollX || 0)) {
              direction = 'left';
            }
            
            window.lastScrollX = scrollX;
            window.lastScrollY = scrollY;
            
            if (window.recordScroll) {
              window.recordScroll(scrollX, scrollY, direction);
            }
          } catch (err) {
            console.error('[DEBUG] Scroll error:', err);
          }
        }, 100);
      }, true);

      document.addEventListener('contextmenu', function(event) {
        var el = event.target;
        try {
          var xpath = getUniqueXPath(el);
          var label = getReadableLabel(el);
          
          if (window.recordRightClick) {
            window.recordRightClick(xpath, label);
          }
        } catch (err) {
          console.error('[DEBUG] Right-click error:', err);
        }
      }, true);
    `));
  }

  // NEW: Helper functions for readable formatting
  private createReadableKey(label: string, selector: string): string {
    // Remove timestamp and technical suffixes
    let cleanLabel = label
      .replace(/_\d+/g, '')                    // Remove timestamps like _1751220214861
      .replace(/_id_xpath$/, '')               // Remove _id_xpath suffix
      .replace(/_class_xpath$/, '')            // Remove _class_xpath suffix  
      .replace(/_placeholder_xpath$/, '')      // Remove _placeholder_xpath suffix
      .replace(/_name_xpath$/, '')             // Remove _name_xpath suffix
      .replace(/_xpath$/, '')                  // Remove _xpath suffix
      .replace(/[-\s]+/g, '_')                 // Replace spaces/hyphens with underscore
      .toLowerCase();

    // If label is generic, try to infer from selector
    if (cleanLabel.startsWith('div-') || cleanLabel.startsWith('input-') || cleanLabel.startsWith('button-')) {
      const inferredName = this.inferNameFromSelector(selector);
      if (inferredName) {
        cleanLabel = inferredName;
      }
    }

    return cleanLabel;
  }

  private createReadableXPath(selector: string): string {
    // Clean up class-based selectors to use contains()
    if (selector.includes('@class=') && selector.includes(' ')) {
      const match = selector.match(/\/\/(\w+)\[@class="([^"]+)"\]/);
      if (match) {
        const [, tagName, className] = match;
        // Use first class or most meaningful class
        const classes = className.split(/\s+/);
        const mainClass = classes.find((cls: string) => 
          !cls.includes('ng-') && !cls.includes('untouched') && !cls.includes('valid') && !cls.includes('dirty')
        ) || classes[0];
        
        return `//${tagName}[contains(@class, "${mainClass}")]`;
      }
    }

    // Clean up long class strings
    if (selector.includes('ng-untouched ng-valid ng-dirty')) {
      return selector.replace(/ng-untouched ng-valid ng-dirty ng-valid-parse/, 'form-control')
                    .replace(/@class="([^"]*)"/, (match, fullClass) => {
                      const classes = fullClass.split(/\s+/);
                      const mainClass = classes.find((cls: string) => cls === 'form-control') || classes[0];
                      return `contains(@class, "${mainClass}")`;
                    });
    }

    return selector;
  }

  private inferNameFromSelector(selector: string): string | null {
    // Try to extract meaningful names from selectors
    
    // From ID
    const idMatch = selector.match(/\[@id="([^"]+)"\]/);
    if (idMatch) {
      return idMatch[1].replace(/[-_]/g, '').toLowerCase();
    }

    // From name attribute
    const nameMatch = selector.match(/\[@name="([^"]+)"\]/);
    if (nameMatch) {
      return nameMatch[1].toLowerCase();
    }

    // From placeholder
    const placeholderMatch = selector.match(/\[@placeholder="([^"]+)"\]/);
    if (placeholderMatch) {
      return placeholderMatch[1].replace(/\s+/g, '').toLowerCase();
    }

    return null;
  }

  private async writeLocatorsToFile(sessionId: number): Promise<void> {
  try {
    const title = this.sessionTitles.get(sessionId) || `session_${sessionId}`;
    await storage.updateRecordingTitle(sessionId, title);

    const propertiesDir = path.join(
      getCurrentProjectPath(),
      "/src/main/resources/elementProperties"
    );
    if (!fs.existsSync(propertiesDir)) {
      fs.mkdirSync(propertiesDir, { recursive: true });
    }

    const outputPath = path.resolve(propertiesDir, `${title}.properties`);

    // Load existing locators if file exists
    let locatorsMap = new Map<string, string>();
    if (fs.existsSync(outputPath)) {
      const existingContent = fs.readFileSync(outputPath, "utf-8");
      existingContent.split('\n').forEach(line => {
        const trimmed = line.trim();
        if (trimmed && trimmed.includes("=")) {
          const idx = trimmed.indexOf("=");
          const key = trimmed.substring(0, idx).trim();
          const value = trimmed.substring(idx + 1).trim();
          locatorsMap.set(key, value);
        }
      });
    }

    // Add/override with current session's locators - FIXED
    this.locatorMap.forEach((xpath: string, key: string) => {
      const propertyKey =
        `${key}_${xpath.substring(xpath.indexOf("@") + 1, xpath.indexOf("="))}_xpath`;
      locatorsMap.set(propertyKey, xpath);
    });

    // Write back all unique locators - FIXED
    const entries: string[] = [];
    locatorsMap.forEach((value: string, key: string) => {
      entries.push(`${key}=${value}`);
    });
    const finalContent = entries.join("\n");

    fs.writeFileSync(outputPath, finalContent, "utf-8");
  } catch (error) {
    console.error("[Recorder] Failed to write locators file:", error);
  }
}

}
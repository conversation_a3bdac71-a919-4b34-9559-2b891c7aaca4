"use client"

import React from "react"

import Pie<PERSON>hartCard from "./Piechart"
import BarChartCard from "./BarChart"
import StatCard from "./StatCard"
import { statsData } from "@/data/statcarddata"

export default function Dashboard() {

  return (
    <div className="p-2 space-y-5">
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {
          statsData.map((stat, index) => (
            <StatCard key={index} title={stat.title} value={stat.value} />
          ))  
        }
        
        
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        
        <PieChartCard />
        <BarChartCard />
       </div>
    </div>
  )
}

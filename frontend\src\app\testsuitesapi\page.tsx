"use client";

import { AppSidebar } from "@/components/dashboard-sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { useState, useEffect, useMemo } from "react";
import TestFlowUICreatorDialog from "@/components/TestFlowUI";
import { setModuleNamesGlobal } from "@/services/testApi";
import TestSuiteUiList from "@/components/TestSuiteUiList";

interface TestFlow {
  name: string;
  description: string;
  testcases: string[];
}



// Fetch test flows
// async function getTestFlowsFromAPI(): Promise<TestFlow[]> {
//   try {
//     const response = await fetch("/api/test-flows");
//     if (!response.ok) throw new Error("Failed to fetch test flows");
//     return response.json();
//   } catch (error) {
//     console.error(error);
//     return [];
//   }
// }

// Fetch test cases (all at once)
// async function getTestCasesFromAPI(): Promise<TestCase[]> {
//   try {
//     const response = await fetch("/api/test-cases");
//     if (!response.ok) throw new Error("Failed to fetch test cases");
//     return response.json();
//   } catch (error) {
//     console.error(error);
//     return [];
//   }
// }

// function TestFlowContent({
//   testFlowName,
//   testFlows,
//   allTestCases,
// }: {
//   testFlowName: string | null;
//   testFlows: TestFlow[];
//   allTestCases: TestCase[];
// }) {
//   // Filter test cases only once using memo
//   const filteredTestCases = useMemo(() => {
//     if (!testFlowName) return [];
//     const testFlow = testFlows.find((flow) => flow.name === testFlowName);
//     if (!testFlow) return [];
//     return allTestCases.filter((tc) => testFlow.testcases.includes(tc.name));
//   }, [testFlowName, testFlows, allTestCases]);

//   if (!testFlowName) {
//     return (
//       <div className="p-8 text-center text-gray-500">
//         <div className="text-gray-400 mb-4">
//           <svg
//             className="w-16 h-16 mx-auto mb-4"
//             fill="none"
//             stroke="currentColor"
//             viewBox="0 0 24 24"
//           >
//             <path
//               strokeLinecap="round"
//               strokeLinejoin="round"
//               strokeWidth={1.5}
//               d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
//             />
//           </svg>
//           <p className="text-lg font-medium text-gray-600">
//             Select a test flow to view test cases
//           </p>
//           <p className="text-sm text-gray-500">
//             Choose a test flow from the sidebar to see its associated test cases
//           </p>
//         </div>
//       </div>
//     );
//   }

//   if (filteredTestCases.length === 0) {
//     return (
//       <div className="p-8 text-center">
//         <div className="text-gray-500 mb-4">
//           <svg
//             className="w-12 h-12 mx-auto mb-2"
//             fill="none"
//             stroke="currentColor"
//             viewBox="0 0 24 24"
//           >
//             <path
//               strokeLinecap="round"
//               strokeLinejoin="round"
//               strokeWidth={2}
//               d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
//             />
//           </svg>
//           <p className="text-lg font-medium">No test cases found</p>
//           <p className="text-sm">
//             This test flow doesn't have any test cases yet.
//           </p>
//         </div>
//       </div>
//     );
//   }

//   return (
//     <div className="relative">
//       <AccordionTable
//         testCases={filteredTestCases}
//         showActionButtons={true}
//         showCheckboxes={true}
//         allowEditing={true}
//         showCucumberButton={false}
//         showPlayButton={false}
//       />
//     </div>
//   );
// }

export default function Page() {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [editingTestFlow, setEditingTestFlow] = useState<TestFlow | null>(null);
  const [testFlows, setTestFlows] = useState<TestFlow[]>([]);
  const [selectedTestSuite, setSelectedTestSuite] = useState<string | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [testSuiteNames, setTestSuiteNames] = useState<string[]>([]);
  const [moduleNames, setModuleNames] = useState([]);

  const [environment,setEnvironment] = useState(['qa','stage','production']);
  const [userDefinedVariables,setUserDefinedvariables] = useState(['test test','test2, test2']);

  
  const [allowEditing, setAllowEditing] = useState(true);
  
  const [isSaving, setIsSaving] = useState(false);

    const [editingCell, setEditingCell] = useState<{
    row: number | null;
    field: string | null;
  }>({ row: null, field: null })

  // useEffect(() => {
  //   // Fetch both concurrently
  //   async function fetchData() {
  //     setLoading(true);
  //     setError(null);
  //     try {
  //       const [flows, testCases] = await Promise.all([
  //         getTestFlowsFromAPI(),
  //         getTestCasesFromAPI(),
  //       ]);
  //       setTestFlows(flows);
  //       setAllTestCases(testCases);

  //       // Set default test flow
  //       if (!selectedTestFlow) {
  //         const defaultFlow = flows.find((flow) => flow.name === "testflow1");
  //         setSelectedTestFlow(
  //           defaultFlow ? "testflow1" : flows.length > 0 ? flows[0].name : null
  //         );
  //       }
  //     } catch (err) {
  //       console.error(err);
  //       setError("Failed to load test flows or test cases");
  //     } finally {
  //       setLoading(false);
  //     }
  //   }
  //   fetchData();
  // }, []);

  

  

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getAllTestSuiteNames();
        const files: string[] = res.files;
        console.log(files);
        console.log("setLoading : "+loading)
        setTestSuiteNames(files);
        console.log(testSuiteNames);
        setSelectedTestSuite(files[0]);
        getAndSetAllModuleNames();
      } catch (err) {
        console.error(err);
        setError("Failed to load test flows or test cases");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  async function getAndSetAllModuleNames() {
    console.log(selectedTestSuite);
    if(selectedTestSuite){
      const res = await getAllModuleNames(selectedTestSuite);
      const data = res.data;
      const files = data.map((dataItem : any)=> dataItem.moduleFiles);
      console.log(files);
      setModuleNames(files);
      setModuleNamesGlobal(files);
    }
  }

  const handleDialogChange = async (open: boolean) => {
    setDialogOpen(open);
    getAndSetAllModuleNames();

    if (!open) {
      // Reset edit mode when closing
      setEditMode(false);
      setEditingTestFlow(null);

      // Refresh test flows and test cases after dialog closes
      setLoading(true);
      setError(null);
      // try {
      //   const [flows, testCases] = await Promise.all([
      //     getTestFlowsFromAPI(),
      //     getTestCasesFromAPI(),
      //   ]);
      //   setTestFlows(flows);
      //   setAllTestCases(testCases);
      // } catch (err) {
      //   console.error(err);
      //   setError("Failed to refresh test flows or test cases");
      // } finally {
      //   setLoading(false);
      // }
      try {
        const res = await getAllTestSuiteNames();
        const files = res.files;
        setTestSuiteNames(files);
        setSelectedTestSuite(files[0]);
      } catch (err) {
        console.error(err);
        setError("Failed to load test flows or test cases");
      } finally {
        setLoading(false);
      }
    }
  };

  // Handle edit button click
  const handleEditTestFlow = () => {
    if (selectedTestSuite) {
      const flowToEdit = testFlows.find(
        (flow) => flow.name === selectedTestSuite
      );
      if (flowToEdit) {
        setEditingTestFlow(flowToEdit);
        setEditMode(true);
        setDialogOpen(true);
      }
    }
  };

  if (loading) {
    return (
      <SidebarProvider
        style={{ "--sidebar-width": "45px" } as React.CSSProperties}
      >
        <AppSidebar />
        <SidebarInset>
          <div className="flex h-screen items-center justify-center">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#15537C]"></div>
              <span>Loading test suites and test flows...</span>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  if (error) {
    return (
      <SidebarProvider
        style={{ "--sidebar-width": "45px" } as React.CSSProperties}
      >
        <AppSidebar />
        <SidebarInset>
          <div className="flex h-screen items-center justify-center">
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600 font-medium">Error loading data</p>
              <p className="text-red-500 text-sm">{error}</p>
              <Button
                onClick={() => window.location.reload()}
                className="mt-2 text-sm"
                variant="outline"
              >
                Retry
              </Button>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider
      style={{ "--sidebar-width": "45px" } as React.CSSProperties}
    >
      <AppSidebar />
      <SidebarInset>
        <div className="flex h-screen overflow-hidden">
          <aside className="w-[15%] border-r bg-muted p-2 space-y-1 bg-[#F7F9FC] font-normal tracking-normal font-roboto">
            <div className="bg-[#15537C] text-white px-2 py-1 flex items-center space-x-1 hover:brightness-110 rounded-xs justify-start">
              <span className="font-bold text-sm ml-1 truncate whitespace-nowrap overflow-hidden max-w-[160px]">
                Test Suits
              </span>
            </div>
            <ul className="text-[#37393A] py-2 flex flex-col gap-2 rounded-xs">
              {testSuiteNames.map((suiteName) => (
                <li
                  key={suiteName}
                  className="text-left w-full text-sm hover:bg-[#B1DBEA]"
                >
                  <button
                    className={`w-full px-2 py-1 text-left ${
                      selectedTestSuite === suiteName
                        ? "bg-[#B1DBEA] text-[#15537C] font-medium"
                        : ""
                    }`}
                    onClick={() => setSelectedTestSuite(suiteName)}
                    // title={flow.description}
                  >
                    <div className="truncate">{suiteName}</div>
                  </button>
                </li>
              ))}
            </ul>
          </aside>

          <div className="flex-1 flex flex-col overflow-hidden">
            <header className="bg-background flex items-center justify-between border-b p-2">
              <div className="flex items-center space-x-3">
                <h1 className="text-left font-medium text-[24px] leading-[20px] tracking-[0px] text-[#2C2E2E]">
                  {selectedTestSuite
                    ? `Test Flows - ${selectedTestSuite}`
                    : "Test Suites"}
                </h1>
              </div>
              <Button
                className="bg-[#15537C] text-white hover:brightness-110 rounded-md w-34 h-6"
                onClick={() => {
                  setEditMode(false);
                  setEditingTestFlow(null);
                  setDialogOpen(true);
                }}
              >
                Create Test Suite
              </Button>
            </header>
            <div className="p-5">
                <TestSuiteUiList selectedTestSuite={selectedTestSuite}/>
            </div>
          </div>
        </div>
      </SidebarInset>
      {/* <TestFlowUICreatorDialog
        open={dialogOpen}
        onOpenChange={handleDialogChange}
        editMode={editMode}
        editingTestFlow={editingTestFlow}
        moduleNames={moduleNames}
      /> */}
    </SidebarProvider>
  );
}

async function getAllTestFlowNames() {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/testflowui`
  );
  return await response.json();
}

async function getAllTestSuiteNames() {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/testsuiteapi`
  );
  return await response.json();
}

async function getAllModuleNames(selectedTestSuite : string) {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/testsuiteapi/${selectedTestSuite}`
  );
  return await response.json();

  
}

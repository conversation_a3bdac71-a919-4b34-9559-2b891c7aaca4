"use client";
import axios from "axios";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogClose,
} from "@/components/ui/dialog";
import { useState, useEffect } from "react";
import {
  Search,
  ChevronRight,
  ChevronLeft,
  Loader2,
  GripVertical,
} from "lucide-react";
import { Button } from "./ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";

// Utility functions for underscore handling - display vs storage format
const formatDisplayName = (name: string): string => {
  return name.replace(/_/g, ' ');
};

const formatStorageName = (name: string): string => {
  return name.replace(/\s+/g, '_');
};

interface TestCase {
  id: number;
  name: string;
}

interface TestFlow {
  name: string;
  description: string;
  testcases: string[];
}

type TestFlowCreatorDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  editMode?: boolean;
  editingTestFlow?: TestFlow | null;
};

export default function TestFlowCreatorDialog({
  open,
  onOpenChange,
  editMode = false,
  editingTestFlow = null,
}: TestFlowCreatorDialogProps) {
  const [testFlowName, setTestFlowName] = useState("");
  const [description, setDescription] = useState("");

  const [leftSelectedIds, setLeftSelectedIds] = useState<number[]>([]);
  const [rightSelectedIds, setRightSelectedIds] = useState<number[]>([]);
  const [selectedTestCases, setSelectedTestCases] = useState<TestCase[]>([]);

  const [leftSearchTerm, setLeftSearchTerm] = useState("");
  const [rightSearchTerm, setRightSearchTerm] = useState("");

  const [allTestCases, setAllTestCases] = useState<TestCase[]>([]);
  const [loading, setLoading] = useState(false);
  const [isCreatingUpdating, setIsCreatingUpdating] = useState(false);

  const { toast } = useToast();

  // Handle drag end for reordering selected test cases
  const handleDragEnd = (result: any) => {
    if (!result.destination) {
      return;
    }

    const sourceIndex = result.source.index;
    const destinationIndex = result.destination.index;

    if (sourceIndex === destinationIndex) {
      return;
    }

    // Create a new array with reordered items
    const reorderedTestCases = Array.from(selectedTestCases);
    const [removed] = reorderedTestCases.splice(sourceIndex, 1);
    reorderedTestCases.splice(destinationIndex, 0, removed);

    setSelectedTestCases(reorderedTestCases);
    // Clear selections after reordering
    setRightSelectedIds([]);
  };

  // Add custom CSS for focus styles matching TestSuite
  useEffect(() => {
    const style = document.createElement("style");
    style.innerHTML = `
      /* Add cursor pointer to checkbox rows */
      .checkbox-row {
        cursor: pointer;
      }
      
      /* Optionally add hover effect */
      .checkbox-row:hover {
        background-color: #f9fafb;
      }

      /* Enhanced toast styling */
      .success-toast {
        backdrop-filter: blur(8px);
        animation: slideInFromRight 0.3s ease-out, pulse 0.6s ease-in-out;
      }
      
      @keyframes slideInFromRight {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }
      
      @keyframes pulse {
        0%, 100% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.02);
        }
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  useEffect(() => {
    if (open) {
      fetchTestCases();
    }
  }, [open]);

  // Populate form when editing
  useEffect(() => {
    if (editMode && editingTestFlow && open) {
      // Show display name (with spaces) when editing instead of storage name
      setTestFlowName(formatDisplayName(editingTestFlow.name));
      setDescription(editingTestFlow.description || "");

      // We need to wait for the data to load before setting selected test cases
      const timer = setTimeout(() => {
        const selectedCases: TestCase[] = editingTestFlow.testcases.map(
          (caseName, index) => {
            // Find the actual test case from allTestCases or create a placeholder
            const foundCase = allTestCases.find((tc) => tc.name === caseName);
            return foundCase || { id: index + 1000, name: caseName }; // Use high ID to avoid conflicts
          }
        );
        setSelectedTestCases(selectedCases);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [editMode, editingTestFlow, open, allTestCases]);

  const fetchTestCases = async () => {
    setLoading(true);
try {
  const response = await axios.get("/api/test-cases?type=names");
  setAllTestCases(response.data);
} catch (error) {
  console.error("Error fetching test cases:", error);
  setAllTestCases([]);
} finally {
  setLoading(false);
}

  };

  const getAvailableTestCases = () => {
    return allTestCases.filter(
      (tc) => !selectedTestCases.some((sel) => sel.name === tc.name)
    );
  };

  const filteredAvailableTestCases = getAvailableTestCases().filter(
    (testCase) =>
      testCase.name.toLowerCase().includes(leftSearchTerm.toLowerCase())
  );

  const filteredSelectedTestCases = selectedTestCases.filter((testCase) =>
    testCase.name.toLowerCase().includes(rightSearchTerm.toLowerCase())
  );

  // Helper functions to check if all filtered items are selected
  const areAllFilteredAvailableSelected = () => {
    return (
      filteredAvailableTestCases.length > 0 &&
      filteredAvailableTestCases.every((tc) => leftSelectedIds.includes(tc.id))
    );
  };

  const areAllFilteredSelectedSelected = () => {
    return (
      filteredSelectedTestCases.length > 0 &&
      filteredSelectedTestCases.every((tc) => rightSelectedIds.includes(tc.id))
    );
  };

  const handleLeftSelectAll = (checked: boolean) => {
    if (checked) {
      // Add filtered items to existing selections
      const filteredIds = filteredAvailableTestCases.map((tc) => tc.id);
      setLeftSelectedIds((prev) => [...new Set([...prev, ...filteredIds])]);
    } else {
      // Remove ONLY the filtered items, keep others
      const filteredIds = filteredAvailableTestCases.map((tc) => tc.id);
      setLeftSelectedIds((prev) =>
        prev.filter((id) => !filteredIds.includes(id))
      );
    }
  };

  const handleRightSelectAll = (checked: boolean) => {
    if (checked) {
      // Add filtered items to existing selections
      const filteredIds = filteredSelectedTestCases.map((tc) => tc.id);
      setRightSelectedIds((prev) => [...new Set([...prev, ...filteredIds])]);
    } else {
      // Remove ONLY the filtered items, keep others
      const filteredIds = filteredSelectedTestCases.map((tc) => tc.id);
      setRightSelectedIds((prev) =>
        prev.filter((id) => !filteredIds.includes(id))
      );
    }
  };

  const handleLeftTestCaseSelect = (id: number, checked: boolean) => {
    setLeftSelectedIds((prev) =>
      checked ? [...prev, id] : prev.filter((tcId) => tcId !== id)
    );
  };

  const handleRightTestCaseSelect = (id: number, checked: boolean) => {
    setRightSelectedIds((prev) =>
      checked ? [...prev, id] : prev.filter((tcId) => tcId !== id)
    );
  };

  const toggleLeftCheckbox = (id: number) => {
    setLeftSelectedIds((prev) =>
      prev.includes(id) ? prev.filter((tcId) => tcId !== id) : [...prev, id]
    );
  };

  const toggleRightCheckbox = (id: number) => {
    setRightSelectedIds((prev) =>
      prev.includes(id) ? prev.filter((tcId) => tcId !== id) : [...prev, id]
    );
  };

  const moveToSelected = () => {
    if (leftSelectedIds.length === 0) return;

    const casesToAdd = leftSelectedIds
      .map((id) => allTestCases.find((tc) => tc.id === id)!)
      .filter((tc) => !selectedTestCases.some((sel) => sel.name === tc.name));

    const updated = [...selectedTestCases, ...casesToAdd];
    updated.sort((a, b) => a.id - b.id);

    setSelectedTestCases(updated);
    setLeftSelectedIds([]);
  };

  const removeFromSelected = () => {
    if (rightSelectedIds.length === 0) return;
    setSelectedTestCases((prev) =>
      prev.filter((tc) => !rightSelectedIds.includes(tc.id))
    );
    setRightSelectedIds([]);
  };

  const handleCreateOrUpdate = async () => {
    if (!testFlowName.trim()) {
      toast({
        title: "Missing field",
        description: "Test Flow Name is required",
        variant: "destructive",
      });
      return;
    }
    if (selectedTestCases.length === 0) {
      toast({
        title: "Nothing selected",
        description: "At least one test case must be selected",
        variant: "destructive",
      });
      return;
    }

    setIsCreatingUpdating(true);

  try {
  if (editMode) {
    // Update existing test flow - INCLUDE ORIGINAL NAME
    // Convert display name to storage format (add underscores) before sending to API
    const testFlowData = {
      originalName: editingTestFlow?.name, // Keep original storage name for lookup
      name: formatStorageName(testFlowName.trim()),
      description: description.trim() || `${testFlowName.trim()} description`,
      testcases: selectedTestCases.map((tc) => tc.name),
    };

    const testFlowResponse = await axios.put("/api/test-flows", testFlowData);

    // Axios throws on non-2xx status, so no need to check response.ok
    toast({
      title: "Success",
      description: `Test flow "${testFlowName}" updated successfully!`,
      duration: 1500,
      style: {
        backgroundColor: "#00AB6A",
        borderColor: "#00966A",
        color: "white",
        borderRadius: "12px",
        boxShadow: "0 10px 25px rgba(0, 171, 106, 0.3), 0 4px 10px rgba(0, 0, 0, 0.1)",
        border: "1px solid #00966A",
      },
      className: "top-4 right-4 w-96 h-16 text-sm p-4 flex items-center gap-3 success-toast",
    });
  } else {
    // Create new test flow (existing logic)

    // Convert display name to storage format (add underscores) for feature file tagging
    const featureFilesResponse = await axios.post("/api/test-cases", {
      testFlowName: formatStorageName(testFlowName.trim()),
      selectedTestCases,
    });

    const featureFilesResult = featureFilesResponse.data;

    // Convert display name to storage format (add underscores) before sending to API
    const testFlowData = {
      name: formatStorageName(testFlowName.trim()),
      description: description.trim() || `${testFlowName.trim()} description`,
      testcases: selectedTestCases.map((tc) => tc.name),
    };

    try {
      const testFlowResponse = await axios.post("/api/test-flows", testFlowData);
      const testFlowResult = testFlowResponse.data;

      let successMessage = "";
      if (featureFilesResult.updatedFiles === 0) {
        successMessage = `Test flow "${testFlowName}" created successfully! ${featureFilesResult.message}`;
      } else if (featureFilesResult.skippedFiles > 0) {
        successMessage = `Test flow "${testFlowName}" created successfully! ${featureFilesResult.message}`;
      } else {
        successMessage = `Test flow "${testFlowName}" created successfully! Updated ${featureFilesResult.updatedFiles} feature files.`;
      }

      toast({
        title: "Success",
        description: successMessage,
        duration: 3000,
        style: {
          backgroundColor: "#00AB6A",
          borderColor: "#00966A",
          color: "white",
          borderRadius: "12px",
          boxShadow: "0 10px 25px rgba(0, 171, 106, 0.3), 0 4px 10px rgba(0, 0, 0, 0.1)",
          border: "1px solid #00966A",
        },
        className: "top-4 right-4 w-96 h-16 text-sm p-4 flex items-center gap-3 success-toast",
      });
    } catch (error: any) {
      console.error("Failed to create test flow in JSON:", error.response?.data?.error || error.message);
      toast({
        title: "Save failed",
        description:
          `Feature files updated, but failed to save test-flow configuration: ` +
          (error.response?.data?.error || "Unknown error"),
        variant: "destructive",
      });
    }
  }

  onOpenChange(false);
  handleCancel();
} catch (error: any) {
  console.error(`Error ${editMode ? "updating" : "creating"} test flow:`, error);
  toast({
    title: "Network error",
    description: `Error ${editMode ? "updating" : "creating"} test flow. Please try again.`,
    variant: "destructive",
  });
} finally {
  setIsCreatingUpdating(false);
}

  };

  const handleCancel = () => {
    setTestFlowName("");
    setDescription("");
    setSelectedTestCases([]);
    setLeftSelectedIds([]);
    setRightSelectedIds([]);
    setLeftSearchTerm("");
    setRightSearchTerm("");
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-screen overflow-y-auto">
        <DialogHeader>
          {/* Display dialog title with spaces instead of underscores */}
          <DialogTitle>
            {editMode
              ? `Edit Test Flow: ${formatDisplayName(editingTestFlow?.name || '')}`
              : "Create New Test Flow"}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Test Flow Name */}
          <div>
            <label className="font-medium">
              Test Flow Name<span className="text-red-500">*</span>
            </label>
            <Input
              value={testFlowName}
              onChange={(e) => setTestFlowName(e.target.value)}
              placeholder="Provide a Test Flow Name"
              className="border border-[#A9ACAE]"
            />
          </div>

          {/* Description */}
          <div>
            <label className="font-medium">Test Flow Description</label>
            <Textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Provide a Test Flow Description"
              className="resize-none min-h-[38px] max-h-[38px] py-2 border border-[#A9ACAE]"
            />
          </div>

          {/* Dual List Layout */}
          <div className="flex gap-4">
            {/* Left Column */}
            <div className="w-1/2">
              <p className="font-medium mb-2">
                List of Test Cases (
                {loading ? "..." : filteredAvailableTestCases.length})
              </p>

              <div className="border border-[#A9ACAE] rounded h-64 overflow-auto">
                {/* Select All and Search - Inside Container */}
                <div className="p-2 border-b border-[#A9ACAE] sticky top-0 bg-white z-10">
                  <div className="flex items-center justify-between">
                    <div
                      className="flex items-center gap-2 checkbox-row px-1 py-0.5 rounded"
                      onClick={() =>
                        handleLeftSelectAll(!areAllFilteredAvailableSelected())
                      }
                    >
                      <input
                        type="checkbox"
                        checked={areAllFilteredAvailableSelected()}
                        onChange={(e) => {
                          e.stopPropagation();
                          handleLeftSelectAll(e.target.checked);
                        }}
                        onClick={(e) => e.stopPropagation()}
                        disabled={loading}
                        className="mr-2 h-4 w-4 accent-blue-900 rounded text-white focus:ring-0"
                      />
                      <span className="text-sm font-medium">Select All</span>
                    </div>
                    <div className="relative w-40">
                      <Input
                        placeholder="Search..."
                        value={leftSearchTerm}
                        onChange={(e) => setLeftSearchTerm(e.target.value)}
                        className="pr-8 h-8 border border-[#A9ACAE]"
                        disabled={loading}
                      />
                      <Search className="h-4 w-4 absolute right-3 top-2 text-gray-400" />
                    </div>
                  </div>
                </div>

                {/* Test Cases List */}
                <div className="p-2 space-y-2">
                  {loading ? (
                    <div className="text-center text-gray-500">
                      Loading test cases...
                    </div>
                  ) : filteredAvailableTestCases.length === 0 ? (
                    <div className="text-center text-gray-500">
                      No test cases found
                    </div>
                  ) : (
                    filteredAvailableTestCases.map((testCase) => {
                      const isChecked = leftSelectedIds.includes(testCase.id);
                      return (
                        <div
                          key={testCase.id}
                          className="flex items-center gap-2 p-1 checkbox-row"
                          onClick={() => toggleLeftCheckbox(testCase.id)}
                        >
                          <input
                            type="checkbox"
                            checked={isChecked}
                            onChange={(e) => {
                              e.stopPropagation();
                              handleLeftTestCaseSelect(
                                testCase.id,
                                e.target.checked
                              );
                            }}
                            onClick={(e) => e.stopPropagation()}
                            className="mr-2 h-4 w-4 accent-blue-900 rounded text-white focus:ring-0"
                          />
                          <span className="text-sm">{testCase.name}</span>
                        </div>
                      );
                    })
                  )}
                </div>
              </div>
            </div>

            {/* Middle Buttons */}
            <div className="flex flex-col justify-center gap-2">
              <button
                onClick={moveToSelected}
                disabled={leftSelectedIds.length === 0}
                className="bg-[#15537C] text-white w-12 h-8 rounded-md flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:brightness-110"
              >
                <ChevronRight size={20} />
              </button>
              <button
                onClick={removeFromSelected}
                disabled={rightSelectedIds.length === 0}
                className="bg-white border border-[#A9ACAE] w-12 h-8 rounded-md flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                <ChevronLeft size={20} color="#15537C" />
              </button>
            </div>

            {/* Right Column - With Drag & Drop */}
            <div className="w-1/2">
              <p className="font-medium mb-2">
                Selected Test Cases ({filteredSelectedTestCases.length})
              </p>

              <div className="border border-[#A9ACAE] rounded h-64 overflow-hidden">
                {/* Header with Search */}
                <div className="p-2 border-b border-[#A9ACAE] sticky top-0 bg-white z-10">
                  <div className="flex items-center justify-between">
                    <div
                      className="flex items-center gap-2 checkbox-row px-1 py-0.5 rounded"
                      onClick={() =>
                        handleRightSelectAll(!areAllFilteredSelectedSelected())
                      }
                    >
                      <input
                        type="checkbox"
                        checked={areAllFilteredSelectedSelected()}
                        onChange={(e) => {
                          e.stopPropagation();
                          handleRightSelectAll(e.target.checked);
                        }}
                        onClick={(e) => e.stopPropagation()}
                        className="mr-2 h-4 w-4 accent-blue-900 rounded text-white focus:ring-0"
                      />
                      <span className="text-sm font-medium">Select All</span>
                    </div>
                    <div className="relative w-40">
                      <Input
                        placeholder="Search..."
                        value={rightSearchTerm}
                        onChange={(e) => setRightSearchTerm(e.target.value)}
                        className="pr-8 h-8 border border-[#A9ACAE]"
                      />
                      <Search className="h-4 w-4 absolute right-3 top-2 text-gray-400" />
                    </div>
                  </div>
                </div>

                {/* Drag & Drop Selected Test Cases List */}
                <div className="h-[calc(100%-60px)]">
                  <DragDropContext onDragEnd={handleDragEnd}>
                    <Droppable
                      droppableId="selected-test-cases"
                      direction="vertical"
                      renderClone={(provided, snapshot, rubric) => {
                        const testCase = selectedTestCases[rubric.source.index];
                        const isChecked = rightSelectedIds.includes(
                          testCase.id
                        );

                        return (
                          <div
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            ref={provided.innerRef}
                            className="flex items-center gap-2 p-1 bg-white shadow-xl border-2 border-blue-400 rounded-md"
                            style={{
                              ...provided.draggableProps.style,
                              zIndex: 9999,
                              position: "fixed",
                              minWidth: "300px",
                              backgroundColor: "white",
                              boxShadow: "0 10px 20px rgba(0,0,0,0.3)",
                            }}
                          >
                            <div className="text-blue-600">
                              <GripVertical size={16} />
                            </div>
                            <div className="flex items-center gap-2 flex-1">
                              <input
                                type="checkbox"
                                checked={isChecked}
                                readOnly
                                className="mr-2 h-4 w-4 accent-blue-900 rounded"
                              />
                              <span className="text-sm font-medium">
                                {testCase.name}
                              </span>
                            </div>
                          </div>
                        );
                      }}
                    >
                      {(provided, snapshot) => (
                        <div
                          {...provided.droppableProps}
                          ref={provided.innerRef}
                          className="p-2 space-y-2"
                          style={{
                            maxHeight: "100%",
                            overflowY: "auto",
                            overflowX: "hidden",
                            minHeight: "200px",
                          }}
                        >
                          {filteredSelectedTestCases.length === 0 ? (
                            <div className="text-center text-gray-500 py-8">
                              No test cases selected
                            </div>
                          ) : (
                            filteredSelectedTestCases.map((testCase, index) => {
                              const actualIndex = selectedTestCases.findIndex(
                                (tc) => tc.id === testCase.id
                              );
                              const isChecked = rightSelectedIds.includes(
                                testCase.id
                              );

                              return (
                                <Draggable
                                  key={testCase.id}
                                  draggableId={testCase.id.toString()}
                                  index={actualIndex}
                                >
                                  {(provided, snapshot) => (
                                    <div
                                      ref={provided.innerRef}
                                      {...provided.draggableProps}
                                      className={`flex items-center gap-2 p-1 checkbox-row transition-opacity ${
                                        snapshot.isDragging
                                          ? "opacity-40"
                                          : "opacity-100"
                                      }`}
                                      style={{
                                        ...provided.draggableProps.style,
                                        transform: snapshot.isDragging
                                          ? `translate(0px, ${
                                              provided.draggableProps.style?.transform?.match(
                                                /translate\([^,]+,\s*([^)]+)\)/
                                              )?.[9] || "0px"
                                            })`
                                          : provided.draggableProps.style
                                              ?.transform,
                                      }}
                                    >
                                      <div
                                        {...provided.dragHandleProps}
                                        className="cursor-grab active:cursor-grabbing text-gray-400 hover:text-gray-600"
                                      >
                                        <GripVertical size={16} />
                                      </div>

                                      <div
                                        className="flex items-center gap-2 flex-1"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          toggleRightCheckbox(testCase.id);
                                        }}
                                      >
                                        <input
                                          type="checkbox"
                                          checked={isChecked}
                                          onChange={(e) => {
                                            e.stopPropagation();
                                            handleRightTestCaseSelect(
                                              testCase.id,
                                              e.target.checked
                                            );
                                          }}
                                          onClick={(e) => e.stopPropagation()}
                                          className="mr-2 h-4 w-4 accent-blue-900 rounded text-white focus:ring-0"
                                        />
                                        <span className="text-sm">
                                          {testCase.name}
                                        </span>
                                      </div>
                                    </div>
                                  )}
                                </Draggable>
                              );
                            })
                          )}
                          {provided.placeholder}
                        </div>
                      )}
                    </Droppable>
                  </DragDropContext>
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            className="border-[#A9ACAE] text-gray-700 hover:bg-gray-50 hover:text-gray-800 w-24 rounded"
            onClick={handleCancel}
            disabled={isCreatingUpdating}
          >
            Cancel
          </Button>
          <Button
            onClick={handleCreateOrUpdate}
            className="bg-[#00AB6A] hover:bg-[#00AB6A]/90 text-white w-24 rounded"
            disabled={loading || isCreatingUpdating}
          >
            {isCreatingUpdating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {editMode ? "Updating..." : "Creating..."}
              </>
            ) : editMode ? (
              "Update"
            ) : (
              "Create"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

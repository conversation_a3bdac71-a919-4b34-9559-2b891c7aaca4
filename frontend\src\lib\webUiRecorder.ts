const WEBUIRECORDER_URL = 'http://localhost:5000';
import axios from 'axios';
export interface ProjectPathResponse {
  success: boolean;
  message?: string;
  error?: string;
  currentPath?: string;
}

// Function to update webuirecorder with current project path
export async function updateWebuirecorderPath(projectPath: string): Promise<ProjectPathResponse> {
  try {
  console.log(`Updating webuirecorder path to: ${projectPath}`);

  const response = await axios.post(`${WEBUIRECORDER_URL}/setCurrentProjectPath`, {
    currentprojectpath: projectPath,
  });

  const data: ProjectPathResponse = response.data;

  if (data.success) {
    console.log(`webuirecorder path updated successfully`);
  } else {
    console.error(`Failed to update webuirecorder path:`, data.error);
  }

  return data;
} catch (error: any) {
  console.error('Error communicating with webuirecorder:', error);
  return {
    success: false,
    error:
      'Failed to communicate with webuirecorder service. Make sure webuirecorder is running on port 5000.',
  };
}

}

// Optional: Function to check current path in webuirecorder (for testing)
export async function getCurrentWebuirecorderPath(): Promise<ProjectPathResponse> {
try {
  const response = await axios.get(`${WEBUIRECORDER_URL}/getCurrentProjectPath`);
  const data: ProjectPathResponse = response.data;
  return data;
} catch (error) {
  console.error('Error getting current path from webuirecorder:', error);
  return {
    success: false,
    error: 'Failed to get current path from webuirecorder',
  };
}

}
const path = require("path");
const fs = require("fs");
const xlsx = require("xlsx");

const excelDir = __dirname + '/../create-api';

// ------------ ROUTE HANDLERS ------------

// GET All API Module Names(files)
exports.getModules = async (_req, res) => {
  try {
    const moduleNames = await getModuleNames();
    res.json({ files: moduleNames });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// GET API Module data by name(file data)
exports.getModuleByName = async (req, res) => {
  try {
    const data = await getModuleDataByName(req.params.name);
    res.json({ file: `TC_${req.params.name}.xlsx`, data });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// GET TestCase Names only by module name
exports.getTCNamesByModule = async (req, res) => {
  try {
    const file = 'TC_' + req.query.name + '.xlsx';
    const filePath = path.join(excelDir, file);

    // Read excelFile
    const excelFile = xlsx.readFile(filePath);

    // Get first sheet
    const sheetName = excelFile.SheetNames[1] || excelFile.SheetNames[0];
    const worksheet = excelFile.Sheets[sheetName];

    // Convert to JSON
    const data = xlsx.utils.sheet_to_json(worksheet);

    const testCaseNames = data.map(item => item.SamplerProxyName)

    res.json({ file: file, testcases: testCaseNames });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

exports.getAllModuleData = async (_req, res) => {
  try {
    const moduleNames = await getModuleNames();
    const allModuleData = {};

    for (const moduleName of moduleNames) {
      allModuleData[moduleName] = await getModuleDataByName(moduleName);
    }

    res.json(allModuleData);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};


// ------------ HELPER FUNCTIONS ------------

async function getModuleDataByName(name) {
  const file = `TC_${name}.xlsx`;
  const filePath = path.join(excelDir, file);

  const excelFile = xlsx.readFile(filePath);
  const sheetName = excelFile.SheetNames[1] || excelFile.SheetNames[0];
  const worksheet = excelFile.Sheets[sheetName];

  return xlsx.utils.sheet_to_json(worksheet);
}


async function getModuleNames() {
  const files = await fs.promises.readdir(excelDir);

  return files
    .filter(file =>
      file.startsWith("TC_") && (file.endsWith(".xlsx") || file.endsWith(".xls"))
    )
    .map(file =>
      file.replace(/^TC_/, "").replace(/\.(xlsx|xls)$/, "")
    );
}


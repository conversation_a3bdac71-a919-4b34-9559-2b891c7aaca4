import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import config from '@/config/config.json';

export async function POST(request: NextRequest) {
  try {
    const { fileName, content, testCaseName } = await request.json();

    if (!fileName || !content || !testCaseName) {
      return NextResponse.json(
        { error: 'Missing required fields: fileName, content, or testCaseName' },
        { status: 400 }
      );
    }

    const recordingsPath = config.recordingsPath;
    const fullFilePath = path.join(recordingsPath, fileName);

    // Check if directory exists, create if it doesn't
    if (!fs.existsSync(recordingsPath)) {
      try {
        fs.mkdirSync(recordingsPath, { recursive: true });
      } catch (error) {
        return NextResponse.json(
          { error: `Failed to create directory: ${recordingsPath}` },
          { status: 500 }
        );
      }
    }

    // Check if file already exists
    if (fs.existsSync(fullFilePath)) {
      return NextResponse.json(
        { error: `File "${fileName}" already exists at ${recordingsPath}` },
        { status: 409 }
      );
    }

    // Write file
    try {
      fs.writeFileSync(fullFilePath, content, 'utf8');
      
      return NextResponse.json({
        success: true,
        message: `Test case "${testCaseName}" saved successfully to ${recordingsPath}`,
        filePath: fullFilePath
      });
    } catch (error) {
      return NextResponse.json(
        { error: `Failed to write file: ${error instanceof Error ? error.message : 'Unknown error'}` },
        { status: 500 }
      );
    }

  } catch (error) {
    return NextResponse.json(
      { error: `Server error: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
}

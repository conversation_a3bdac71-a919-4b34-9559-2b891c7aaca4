export function updateXmlTag(
  obj: unknown,
  path: (string | number)[],
  newValue: string
): boolean {
  if (typeof obj !== 'object' || obj === null) {
    return false;
  }

  let current: unknown = obj;

  for (let i = 0; i < path.length - 1; i++) {
    if (
      typeof current === 'object' &&
      current !== null &&
      (current as Record<string | number, unknown>)[path[i]] !== undefined
    ) {
      current = (current as Record<string | number, unknown>)[path[i]];
    } else {
      return false;
    }
  }

  const finalKey = path[path.length - 1];
  if (typeof current === 'object' && current !== null) {
    const currentObj = current as Record<string | number, unknown>;
    if (currentObj[finalKey] !== undefined) {
      currentObj[finalKey] = newValue;
      return true;
    }
  }

  return false;
}

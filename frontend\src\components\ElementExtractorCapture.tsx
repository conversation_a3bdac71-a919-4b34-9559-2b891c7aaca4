"use client";
import React, { useState, useEffect } from "react";
import { ChevronDown } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import config from "@/config/config.json";
import axios from 'axios';
interface ElementExtractorCaptureProps {
  onCancel?: () => void;
  onSuccess?: () => void;
}

interface BrowserConfig {
  name: string;
  icon: string;
  value: string;
}

const ElementExtractorCapture: React.FC<ElementExtractorCaptureProps> = ({
  onCancel,
  onSuccess,
}) => {
  const [extractorName, setExtractorName] = useState("");
  const [url, setUrl] = useState("");
  const [browser, setBrowser] = useState<BrowserConfig | null>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isExtracting, setIsExtracting] = useState(false);
  const [browsers, setBrowsers] = useState<BrowserConfig[]>([]);

  // Fetch browsers from config file
  useEffect(() => {
    try {
      const configBrowsers: BrowserConfig[] = config.browsers || [];
      setBrowsers(configBrowsers);

      // Set default browser if available
      if (configBrowsers.length > 0) {
        setBrowser(configBrowsers[0]);
      }
    } catch (error) {
      console.error("Failed to load browsers from config:", error);
      // Fallback browsers
      const fallbackBrowsers: BrowserConfig[] = [
        { name: "Chrome", icon: "🌐", value: "chromium" },
        { name: "Firefox", icon: "🦊", value: "firefox" },
        { name: "Safari", icon: "🧭", value: "safari" },
        { name: "Edge", icon: "📘", value: "edge" },
      ];
      setBrowsers(fallbackBrowsers);
      setBrowser(fallbackBrowsers[0]);
    }
  }, []);

  const toggleDropdown = () => setIsDropdownOpen(!isDropdownOpen);

  const selectBrowser = (selected: BrowserConfig) => {
    setBrowser(selected);
    setIsDropdownOpen(false);
  };

  // Generate Gherkin feature content
  const generateFeatureContent = (name: string, url: string): string => {
    return `Feature: capture webelements


  Scenario: capture current page elements
    Given I Navigate to the page "${url}"
    And capture current page web element and generate properties file as "${name}"
    Then I copy UI element properties files from extractor to modules

`;
  };

  const handleSave = async () => {
    if (!extractorName.trim() || !url.trim() || !browser) {
      toast({
        title: "Missing fields",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    setIsExtracting(true);


try {
  // Generate Gherkin content
  const gherkinContent = generateFeatureContent(extractorName, url);

  // POST request using Axios
  const response = await axios.post('/api/element-extractor', {
    fileName: extractorName,
    gherkinContent,
    url,
    browser: browser.value,
  });

  const data = response.data;

  if (data.success) {
    toast({
      title: 'Element Extractor Created',
      description: `Element extractor '${extractorName}.feature' created successfully`,
      variant: 'default',
    });

    // Reset form
    setExtractorName('');
    setUrl('');
    setBrowser(browsers[0] || null);

    // Close dialog and refresh
    onCancel?.();
    onSuccess?.();
  } else {
    toast({
      title: 'Save failed',
      description: data.error || 'Failed to create element extractor.',
      variant: 'destructive',
    });
  }
} catch (error: any) {
  toast({
    title: 'Network error',
    description:
      error.response?.data?.error ||
      error.message ||
      'Failed to connect to server.',
    variant: 'destructive',
  });
} finally {
  setIsExtracting(false);
}

  };

  const isFormValid = extractorName.trim() && url.trim() && browser;

  return (
    <div className="w-full mx-auto my-6 bg-white relative">
      <div>
        {/* ✅ Name Field - Same styling as TestCase Form */}
        <div className="relative">
          <label className="absolute -top-3 left-2 bg-white px-1 text-xs font-medium text-gray-700">
            Name <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            className="w-full border border-gray-300 rounded p-3 pt-4"
            value={extractorName}
            onChange={(e) => setExtractorName(e.target.value)}
            disabled={isExtracting}
            placeholder="Enter properties file name"
          />
        </div>

        {/* ✅ URL Field - Increased spacing from mt-4 to mt-8 */}
        <div className="relative mt-8"> {/* ✅ Changed from mt-4 to mt-8 for more space */}
          <label className="absolute -top-3 left-2 bg-white px-1 text-xs font-medium text-gray-700">
            URL <span className="text-red-500">*</span>
          </label>
          <input
            type="url"
            className="w-full border border-gray-300 rounded p-3 pt-4"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            disabled={isExtracting}
            placeholder="https://example.com"
          />
        </div>

        {/* ✅ Browser Dropdown - Increased spacing from mt-6 to mt-10 */}
        <div className="flex flex-wrap justify-between items-end mt-10 gap-4"> {/* ✅ Changed from mt-6 to mt-10 for more space */}
          <div className="min-w-[200px] max-w-[400px] w-full sm:w-[300px]">
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Select Browser
            </label>
            <div className="relative">
              <div
                className={`w-full flex items-center justify-between border border-gray-300 rounded p-3 bg-white ${
                  isExtracting
                    ? "cursor-not-allowed opacity-50"
                    : "cursor-pointer"
                }`}
                onClick={isExtracting ? undefined : toggleDropdown}
              >
                <div className="flex items-center">
                  {browser && <span className="mr-2">{browser.icon}</span>}
                  {browser?.name || "Select a browser"}
                </div>
                <ChevronDown
                  size={16}
                  className={`transition-transform duration-200 ${
                    isDropdownOpen ? "rotate-180" : ""
                  }`}
                />
              </div>

              {isDropdownOpen && !isExtracting && (
                <div className="absolute w-full bg-white border border-gray-300 mt-1 rounded shadow-lg z-10">
                  {browsers.map((b) => (
                    <div
                      key={b.name}
                      className="p-2 hover:bg-gray-100 cursor-pointer flex items-center"
                      onClick={() => selectBrowser(b)}
                    >
                      <span className="mr-2">{b.icon}</span>
                      {b.name}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* ✅ Buttons - Same styling as TestCase Form */}
          <div className="flex space-x-3">
            <Button
              className="px-5 py-3 border border-gray-300 rounded 
                         bg-white text-gray-700 font-medium hover:bg-gray-100 transition-colors duration-200"
              onClick={() => {
                setExtractorName("");
                setUrl("");
                setBrowser(browsers[0] || null);
                onCancel?.();
              }}
              disabled={isExtracting}
            >
              Cancel
            </Button>

            <Button
              style={{ backgroundColor: "#15537C" }}
              className="px-5 py-2.5 text-white rounded font-medium"
              onClick={handleSave}
              disabled={!isFormValid || isExtracting}
            >
              {isExtracting ? "Creating..." : "Save"}
            </Button>
          </div>
        </div>

        {/* ✅ Status indicator - Same styling as TestCase Form */}
        {isExtracting && (
          <div className="mt-3 p-3 rounded-md bg-blue-50 border border-blue-200">
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full mr-2 bg-blue-500 animate-pulse"></div>
              <span className="text-sm font-medium text-blue-800">
                Creating {extractorName}.feature file...
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ElementExtractorCapture;

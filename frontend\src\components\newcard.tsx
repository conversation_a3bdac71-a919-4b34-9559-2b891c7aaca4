"use client";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { useState, useEffect } from "react";
import { ChevronDown, ChevronUp, Loader2, FolderOpen } from "lucide-react";
import { useRouter } from "next/navigation";
import axios from "axios";
import api from "@/app/services/api";
import { updateWebuirecorderPath } from "@/lib/webUiRecorder";
import { useToast } from "@/hooks/use-toast";



type User = {
  role: string;
  assignee: string;
};

type Project = {
  id: number;
  title: string;
  users: User[];
  tenant_id?: number;
  name?: string;
  description?: string;
  start_date?: string | null;
  end_date?: string | null;
  is_enabled?: number;
  created_at?: string;
  projecturl?: string; // <-- Add this line
};

type BigStatCardProps = {
  project: Project;
  index: number;
  isExpanded: boolean;
  onToggle: (index: number) => void;
};

// Static actions that will be the same for all projects
const staticActions = [
  {
    name: "Test Cases",
    route: "/testcasemanagement",
  },
  {
    name: "Test Flows",
    route: "/testflows",
  },
  {
    name: "Test Suites",
    route: "/testsuites",
  },
];

export default function BigStatCard({
  project,
  index,
  isExpanded,
  onToggle,
}: BigStatCardProps) {
  const router = useRouter();

  const handleActionClick = async (route: string) => {
    router.push(route);
  };

  const toggleAccordion = () => {
    onToggle(index);
  };

  return (
    <Card className="w-full">
      <CardHeader
        className="py-0 px-3 cursor-pointer transition-colors w-full h-2 flex items-center"
        onClick={toggleAccordion}
      >
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-2">
            <CardTitle className="text-base font-medium">
              {project.name}
            </CardTitle>
            {isExpanded && (
              <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                Active
              </span>
            )}
          </div>
          {isExpanded ? (
            <ChevronUp className="h-4 w-4 text-gray-500" />
          ) : (
            <ChevronDown className="h-4 w-4 text-gray-500" />
          )}
        </div>
      </CardHeader>

      {isExpanded && (
        
        <CardContent className="w-full p-2">
          <div className="w-full flex border border-gray-200 rounded-lg shadow-sm bg-gray-50 py-2 px-3">
            {/* Left Partition */}
            <div className="flex-[0_0_70%] border-r border-gray-300 pr-3">
              <h3 className="font-semibold text-sm mb-1 text-gray-800">
                Users Details
              </h3>
              <div className="space-y-1">
                {project.users && project.users.length > 0 ? (
                  project.users.map((user, userIndex) => (
                    <div key={userIndex} className="text-xs">
                      <span className="font-medium text-gray-900">
                        {user.assignee} - {user.role}
                      </span>
                    </div>
                  ))
                ) : (
                  <div className="text-xs text-gray-500">No users assigned</div>
                )}
              </div>
            </div>

            {/* Right Partition */}
            <div className="flex-[0_0_30%] pl-3">
              <div className="flex flex-col space-y-1 items-start">
                {staticActions.map((action, actionIndex) => (
                  <button
                    key={actionIndex}
                    onClick={() => handleActionClick(action.route)}
                    className="text-blue-600 hover:text-blue-800 underline decoration-blue-600 hover:decoration-blue-800 text-sm font-medium transition-colors text-left whitespace-nowrap hover:bg-blue-50 px-2 py-1 rounded"
                  >
                    {action.name}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
}

export function BigStatCardsContainer() {
  const { toast } = useToast();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeCardIndex, setActiveCardIndex] = useState<number>(0);
  const [activeProjectName, setActiveProjectName] = useState<string>("");

  // Function to switch active project
  const switchToProject = async (projectName: string) => {
  

try {
  // Step 1: Update config.json (internal API route)
  const response = await axios.post("/api/active-project", { projectName });

  console.log("✅ Config updated:", response.data.message);
  setActiveProjectName(projectName);

  // Step 2: Fetch updated config and update webuirecorder
  try {
    console.log("🔍 Fetching updated config after project switch...");

    const configResponse = await axios.get("/api/get-config");
    const projectPath = configResponse.data.projectPath;

    console.log("✅ Got updated projectPath:", projectPath);
    console.log("🚀 Sending to webuirecorder:", projectPath);

    if (!projectPath) {
      throw new Error("ProjectPath not found in config");
    }

    const webuiResult = await updateWebuirecorderPath(projectPath);

    if (webuiResult.success) {
      console.log("✅ Webuirecorder updated successfully with:", projectPath);
    } else {
      console.warn("⚠️ Webuirecorder update failed:", webuiResult.error);
    }
  } catch (configError) {
    console.error("❌ Failed to get updated config:", configError);
    // Don't block the flow
  }
} catch (error: any) {
  const errorMessage = error.response?.data?.error || error.message || "Unknown error";
  console.error("❌ Error switching project:", errorMessage);

  toast({
    title: "Switch failed",
    description: `Failed to switch project: ${errorMessage}`,
    variant: "destructive",
  });
}

  };

  // Function to fetch current active project from config
  const fetchActiveProject = async () => {


try {
  const response = await axios.get("/api/active-project");
  const configData = response.data;
  const currentActiveProject = configData.projectName || "";
  setActiveProjectName(currentActiveProject);
  return currentActiveProject;
} catch (error) {
  console.error("Error fetching active project:", error);
}

    return "";
  };

  // Helper function to fetch user details for a project (fixed version)
  const fetchProjectUsers = async (projectId: number): Promise<User[]> => {
    try {
      const assignmentsResponse = await api.get(
        `/projectassignment/${projectId}`
      );
      const assignments = assignmentsResponse.data;

      if (!assignments || assignments.length === 0) {
        return [];
      }

      // Fixed: Proper array declaration and synchronous processing
      const users: User[] = [];

      assignments.forEach((assignment: any) => {
        users.push({
          role: assignment.role_name || "Unknown Role",
          assignee: assignment.user_name || "Unknown User",
        });
      });

      return users;
    } catch (error) {
      console.error(
        `Error fetching assignments for project ${projectId}:`,
        error
      );
      return [];
    }
  };

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);
        setError(null);

        // Step 1: Fetch projects
        const response = await api.get("/projects");
        const projectsData = response.data;
        console.log("Projects data:", projectsData);
        console.log("Active card index:", activeCardIndex);
        console.log(projectsData[activeCardIndex])
        // let index (activeCardIndex === -1) ? 0 : activeCardIndex;
        
        if (!projectsData || !Array.isArray(projectsData)) {
          throw new Error("Invalid projects data received");
        }

        // Step 2: Fetch ALL project assignments simultaneously (efficient approach)
        const assignmentPromises = projectsData.map((project) =>
          api.get(`/projectassignment/${project.id}`).catch((error) => {
            console.error(
              `Error fetching assignments for project ${project.id}:`,
              error
            );
            return { data: [] }; // Return empty array if call fails
          })
        );

        // Wait for all assignment calls to complete
        const assignmentResponses = await Promise.all(assignmentPromises);

        // Step 3: Combine projects with their assignments
        const projectsWithUsers = projectsData.map(
          (project: any, index: number) => {
            const assignments = assignmentResponses[index].data || [];

            const users = assignments.map((assignment: any) => ({
              role: assignment.role_name || "Unknown Role",
              assignee: assignment.user_name || "Unknown User",
            }));

            return {
              ...project,
              users,
            };
          }
        );

        console.log("Final projects with users:", projectsWithUsers);
        setProjects(projectsWithUsers);

        // Fetch active project and set correct card as expanded
        const currentActiveProject = await fetchActiveProject();

        if (projectsWithUsers.length > 0) {
          if (currentActiveProject) {
            // Find the index of the currently active project
            const activeIndex = projectsWithUsers.findIndex(
              (p) => p.name === currentActiveProject
            );
            setActiveCardIndex(activeIndex >= 0 ? activeIndex : 0);
          } else {
            // If no active project in config, default to first
            setActiveCardIndex(0);
          }
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred");
        console.error("Error fetching projects:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  const handleCardToggle = async (index: number) => {
  
    setActiveCardIndex(index);

    if (projects[index]) {
      const selectedProject = projects[index];
      // Store the active project in localStorage
      localStorage.setItem("activeProject", JSON.stringify(selectedProject));
      sessionStorage.setItem("currentProjectUrl", ""+projects[index].projecturl);
      await switchToProject(selectedProject.name ?? "");
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="w-full space-y-4">
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          <span className="ml-2 text-gray-600">
            Loading projects from database...
          </span>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="w-full space-y-4">
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <p className="text-red-600 mb-2">Error loading projects: {error}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Empty state - Updated to match consistent pattern
  if (projects.length === 0) {
    return (
      <div className="p-8 text-center text-gray-500">
        <div className="text-gray-400 mb-4">
          <svg
            className="w-16 h-16 mx-auto mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
            />
          </svg>

          <p className="text-lg font-medium text-gray-600">No projects found</p>
          <p className="text-sm text-gray-500">
            Create your first project to get started
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-4">
      {projects.map((project, index) => (
        <BigStatCard
          key={project.id}
          project={project}
          index={index}
          isExpanded={activeCardIndex === index}
          onToggle={handleCardToggle}
        />
      ))}
    </div>
  );
}

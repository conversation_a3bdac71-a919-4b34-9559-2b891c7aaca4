"use client";

import { useState, useEffect } from "react";
import { AppSidebar } from "@/components/dashboard-sidebar";
import { Button } from "@/components/ui/button";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import ConfigTable from "@/components/ConfigTable";
import axios from 'axios';

interface ConfigRow {
  id: number;
  data: {
    envName: string;
    baseUrl: string;
    dbName: string;
    dbCredentials: string;
  };
}
export default function Page() {
  const [configRows, setConfigRows] = useState<ConfigRow[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch configurations from file on component mount
  useEffect(() => {
    fetchConfigurations();
  }, []);


const fetchConfigurations = async () => {
  try {
    setLoading(true);
    setError(null);

    // Axios automatically throws on non-2xx
    const response = await axios.get<ConfigRow[] | { error: string }>('/api/projectConfigurations');

    const data = response.data;

    // If API returned a custom error object
    if ('error' in data) {
      throw new Error(data.error);
    }

    setConfigRows(data); // data is ConfigRow[]

  } catch (error: any) {
    console.error('Error fetching configurations:', error);

    setError(
      error instanceof Error
        ? error.message
        : 'Failed to load configurations'
    );

    // Fallback: create one empty row
    setConfigRows([
      {
        id: Date.now(),
        data: {
          envName: "",
          baseUrl: "",
          dbName: "",
          dbCredentials: ""
        }
      }
    ]);
  } finally {
    setLoading(false);
  }
};


  const handleAddRow = () => {
    const newRow: ConfigRow = {
      id: Date.now(),
      data: {
        envName: "",
        baseUrl: "",
        dbName: "",
        dbCredentials: ""
      }
    };

    setConfigRows(prevRows => [...prevRows, newRow]);
  };



const handleRemoveRow = async (rowId: number) => {
  if (configRows.length === 1) {
    alert("At least one configuration row must remain");
    return;
  }

  console.log(`Deleting configuration for row ${rowId}`);

  // Remove from local state first
  const updatedRows = configRows.filter(row => row.id !== rowId);
  setConfigRows(updatedRows);

  // Save the updated data to properties file (without the deleted row)
  try {
    const response = await axios.put('/api/projectConfigurations', updatedRows, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result = response.data;

    console.log('Row deleted and properties file updated successfully!');
  } catch (error: any) {
    const errorMessage = error.response?.data?.error || error.message || 'Unknown error';
    console.error('Failed to update file after deletion:', errorMessage);
    alert('Failed to save deletion: ' + errorMessage);
  }
};



const handleSaveRow = async (rowId: number, data: any) => {
  console.log(`Saving configuration for row ${rowId}:`, data);

  // Update local state first
  setConfigRows(prevRows =>
    prevRows.map(row =>
      row.id === rowId ? { ...row, data } : row
    )
  );

  // Prepare updated rows for saving
  const updatedRows = configRows.map(row =>
    row.id === rowId ? { ...row, data } : row
  );

  try {
    const response = await axios.put('/api/projectConfigurations', updatedRows, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result = response.data;

    console.log('Configuration saved successfully to properties file!');
    // Optional: show success UI

  } catch (error: any) {
    const errorMessage = error.response?.data?.error || error.message || 'Unknown error';
    console.error('Failed to save configuration:', errorMessage);
    alert('Failed to save: ' + errorMessage);
  }
};

  
  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "45px",
        } as React.CSSProperties
      }
    >
      <AppSidebar />
      <SidebarInset>
        <header className="bg-background sticky top-0 flex items-center justify-between border-b p-2">
          <h1 className="text-left font-medium text-[24px] leading-[20px] tracking-[0px] text-[#2C2E2E]">
            Project Configurations
          </h1>
          <div className="flex gap-2 ml-auto">
            <Button
              className="bg-[#15537C] text-white hover:brightness-110 rounded-md w-34 h-6"
              onClick={handleAddRow}
            >
              Add Configuration
            </Button>
            {error && (
              <Button
                variant="outline"
                className="text-red-600 border-red-600 hover:bg-red-50"
                onClick={fetchConfigurations}
              >
                Retry Load
              </Button>
            )}
          </div>
        </header>

        <main className="p-6">
          {loading ? (
            <div className="text-center py-8">
              <p>Loading configurations...</p>
            </div>
          ) : error ? (
            <div className="text-center py-8 text-red-600">
              <p>Error: {error}</p>
              <p className="text-sm text-gray-500 mt-2">Using fallback empty configuration</p>
            </div>
          ) : (
            <ConfigTable
              rows={configRows}
              onSave={handleSaveRow}
              onRemove={handleRemoveRow}
            />
          )}
        </main>
      </SidebarInset>
    </SidebarProvider>  
  );
}

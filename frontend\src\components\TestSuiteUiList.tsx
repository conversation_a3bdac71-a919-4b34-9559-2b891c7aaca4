import React, { useEffect, useState } from "react";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";

import { useToast } from "@/hooks/use-toast";

function TestSuiteUiList(selectedTestSuite: any) {
  const [loading, setLoading] = useState(false);
  const [testFlows, setTestFlows] = useState([]);
  const [envNames, setEnvNames] = useState([]);
  const { toast } = useToast();

  async function getEnvironmentNames() {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/config/envnames`
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch environment names: ${response.status}`);
      }
    } catch (error : any){
        console.error("Error fetching enironment:", error);
      toast({
        title: "Error",
        description: error.message || "Something went wrong",
        variant: "destructive",
        duration: 2000,
        style: {
          backgroundColor: "#dc3545",
          color: "#f8f9fa",
          borderRadius: "12px",
        },
      });
    }
  }

  async function fetchTestFlows() {
    setLoading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/testsuiteapi/${selectedTestSuite.selectedTestSuite}`
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch configs: ${response.status}`);
      }

      const res = await response.json();
      const data = res.data;
      const files = data.map((dataItem: any) => dataItem.moduleFiles);

      console.log(res);
      setTestFlows(files);
    } catch (error: any) {
      console.error("Error fetching configs:", error);
      toast({
        title: "Error",
        description: error.message || "Something went wrong",
        variant: "destructive",
        duration: 2000,
        style: {
          backgroundColor: "#dc3545",
          color: "#f8f9fa",
          borderRadius: "12px",
        },
      });
      // setUsersFetchError(error.message || "Failed to load users");
    } finally {
      // setIsLoadingUsers(false);
      setLoading(false);
    }
  }

  useEffect(() => {
    fetchTestFlows();
    getEnvironmentNames();
  }, [selectedTestSuite]);

  return (
    <div>
      {testFlows?.length <= 0 ? (
        <div className="p-8 text-center text-gray-500">
          <div className="text-gray-400 mb-4">
            <svg
              className="w-16 h-16 mx-auto mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
              />
            </svg>

            <p className="text-lg font-medium text-gray-600">
              No configurations found
            </p>
            <p className="text-sm text-gray-500">
              Start by identifying and creating configurations for your project
            </p>
          </div>
        </div>
      ) : (
        <Table className="table-fixed">
          <TableHeader className="bg-[#15537c]">
            <TableRow>
              <TableHead className="w-[5%]"></TableHead>
              <TableHead className="text-[#ffffff] w-[15%]">
                Flow Name
              </TableHead>
              <TableHead className="text-[#ffffff] w-[20%]">
                Environment Name
              </TableHead>
              <TableHead className="text-[#ffffff] w-[60%]">
                User defined variables
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {testFlows?.map((testFlow: string, index: number) => {
              return (
                <TableRow
                  key={index}
                  className={
                    index % 2 === 0 ? "bg-[#F7F9FC] " : "bg-[#FFFFFF4D]"
                  }
                >
                  <TableCell className="w-[5%]">
                    <input type="checkbox" className="accent-blue-500" />
                  </TableCell>
                  <TableCell>{testFlow}</TableCell>
                  <TableCell key={index} className="h-13">
                    <select name="" id=""></select>
                  </TableCell>
                  {/* <TableCell>{userDefinedVariables[index]}</TableCell> */}
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      )}
    </div>
  );
}

export default TestSuiteUiList;

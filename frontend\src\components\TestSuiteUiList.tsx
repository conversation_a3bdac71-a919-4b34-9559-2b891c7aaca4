import React, { useEffect, useState } from "react";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";

import { useToast } from "@/hooks/use-toast";
import { Plus } from "lucide-react";

type TestFlow = {
  runMode: string;
  moduleFiles: string;
  environment: string;
  UserDefinedVariables: string;
};

function TestSuiteUiList(selectedTestSuite: any) {
  const [loading, setLoading] = useState(false);
  const [testFlowData, setTestFlowData] = useState<TestFlow[]>([]);
  const [envNames, setEnvNames] = useState([]);
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  const [editingRow, setEditingRow] = useState<number | null>(null);

  const [selectedTestFlows, setSelectedTestFlows] = useState<boolean[]>(
    Array(testFlowData.length).fill(false)
  );

  //toggle logic
  const toggleElement = (index: number) => {
    const updated = [...testFlowData];
    if(updated[index].runMode == 'Yes')
        updated[index].runMode = 'No'
    else 
        updated[index].runMode = 'Yes'
    
    setTestFlowData(updated)
    setIsSaving(true);
  };

  async function getEnvironmentNames() {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/config/envnames`
      );

      if (!response.ok) {
        throw new Error(
          `Failed to fetch environment names: ${response.status}`
        );
      }

      const envNames = await response.json();
      console.log(envNames);
      setEnvNames(envNames);
    } catch (error: any) {
      console.error("Error fetching enironment:", error);
      toast({
        title: "Error",
        description: error.message || "Something went wrong",
        variant: "destructive",
        duration: 2000,
        style: {
          backgroundColor: "#dc3545",
          color: "#f8f9fa",
          borderRadius: "12px",
        },
      });
    }
  }

  async function fetchTestFlows() {
    setLoading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/testsuiteapi/${selectedTestSuite.selectedTestSuite}`
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch configs: ${response.status}`);
      }

      const res = await response.json();
      const data = res.data;
      console.log("data : ")
      console.log(data);
      setTestFlowData(data);
    } catch (error: any) {
      console.error("Error fetching configs:", error);
      toast({
        title: "Error",
        description: error.message || "Something went wrong",
        variant: "destructive",
        duration: 2000,
        style: {
          backgroundColor: "#dc3545",
          color: "#f8f9fa",
          borderRadius: "12px",
        },
      });
      // setUsersFetchError(error.message || "Failed to load users");
    } finally {
      // setIsLoadingUsers(false);
      setLoading(false);
    }
  }

  const handleCellChange = (
    e: React.ChangeEvent<HTMLInputElement> | React.ChangeEvent<HTMLTextAreaElement> | React.ChangeEvent<HTMLSelectElement>,
    rowIndex: number,
    field: keyof TestFlow
  ) => {
    const updatedtestFlowData = [...testFlowData];
    updatedtestFlowData[rowIndex] = {
      ...updatedtestFlowData[rowIndex],
      [field]: e.target.value,
    };
    console.log(updatedtestFlowData);
    setIsSaving(true);
    setTestFlowData(updatedtestFlowData);
    console.log("testFlowData");
    console.log(testFlowData);
  };

  function handleAddRow(index : number){

  }
  function handleDeleteSelected(indexNo : number){
    
      const elementsToKeep = testFlowData.filter((_,index) => index !== indexNo);
      console.log(elementsToKeep);

      // Update local state - renumber the remaining elements
      const renumbered = elementsToKeep.map((element) => ({
        ...element,
      }));

      setTestFlowData(renumbered);
  }

  useEffect(() => {
    fetchTestFlows();
    getEnvironmentNames();
  }, [selectedTestSuite]);

  return (
    <div>
      <header className="bg-background flex items-center justify-between border-b p-2">
        <div className="flex items-center space-x-3">
          <h1 className="text-left font-medium text-[24px] leading-[20px] tracking-[0px] text-[#2C2E2E]">
            {selectedTestSuite
              ? `Test Suites - ${selectedTestSuite.selectedTestSuite}`
              : "Test Suites"}
          </h1>
        </div>
        <Button
          className="bg-[#15537C] text-white hover:brightness-110 rounded-md w-34 h-6"
          onClick={() => {
            // setEditMode(false);
            // setEditingTestFlow(null);
            // setDialogOpen(true);
          }}
        >
          Create Test Suite
        </Button>
      </header>
      <div className="flex items-center gap-[20px] p-3">
        <div className="w-[85%]">
          <input
            type="text"
            placeholder="Search by Test flow name..."
            value=""
            className="border px-3 py-2 rounded w-full"
          />
        </div>
        <div className="flex items-center gap-[20px]">
          {/* save elements */}
          <a
            onClick={(e) => {
              e.stopPropagation();
              // handleSaveClick();
            }}
            className={`${
              isSaving
                ? "pointer-events-auto cursor-pointer"
                : "pointer-events-none"
            }`}
            title="Save new configs"
          >
            <img
              src="/save1.svg"
              className="w-7 h-8 cursor-pointer"
              style={{ opacity: isSaving ? 1 : 0.5 }}
            />
          </a>
        </div>
      </div>
      {testFlowData?.length <= 0 ? (
        <div className="p-8 text-center text-gray-500">
          <div className="text-gray-400 mb-4">
            <svg
              className="w-16 h-16 mx-auto mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
              />
            </svg>

            <p className="text-lg font-medium text-gray-600">
              No configurations found
            </p>
            <p className="text-sm text-gray-500">
              Start by identifying and creating configurations for your project
            </p>
          </div>
        </div>
      ) : (
        <div className="p-3">
          <Table className="table-fixed">
            <TableHeader className="bg-[#15537c]">
              <TableRow>
                <TableHead className="w-[5%]"></TableHead>
                <TableHead className="text-[#ffffff] w-[15%]">
                  Flow Name
                </TableHead>
                <TableHead className="text-[#ffffff] w-[20%]">
                  Environment Name
                </TableHead>
                <TableHead className="text-[#ffffff] w-[54%]">
                  User defined variables
                </TableHead>
                <TableHead></TableHead>
                <TableHead></TableHead>

              </TableRow>
            </TableHeader>
            <TableBody>
              {testFlowData?.map((testFlow, index: number) => {
                return (
                  <TableRow
                    key={index}
                    className={
                      index % 2 === 0 ? "bg-[#F7F9FC] " : "bg-[#FFFFFF4D]"
                    }
                  >
                    <TableCell className="w-[5%]">
                      <input
                        type="checkbox"
                        className="accent-blue-500"
                        checked={
                          testFlow.runMode == "Yes"
                            ? true
                            : false || selectedTestFlows[index]
                        }
                        onChange={() => toggleElement(index)}
                      />
                    </TableCell>
                    <TableCell>
                      {testFlow.moduleFiles
                        .replace(/^TF_/, "")
                        .replace(/\.xlsx$/, "")}
                    </TableCell>
                    <TableCell key={index} className="h-13">
                      <select
                        className="w-50 p-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        onChange={(e) =>
                          handleCellChange(e, index, "environment")
                        }
                      >
                        <option> {testFlow.environment}
                        </option>
                        {envNames.map((envName, envIndex) => (
                          <option key={envIndex} value={envName}>
                            {envName}
                          </option>
                        ))}
                      </select>
                    </TableCell>
                    <TableCell
                      className="cursor-pointer"
                      onClick={() => setEditingRow(index)}
                    >
                      {editingRow === index ? (
                        <textarea
                          value={testFlow.UserDefinedVariables || ""}
                          onChange={(e) =>
                            handleCellChange(e, index, "UserDefinedVariables")
                          }
                          onBlur={() => setEditingRow(null)} // Exit edit mode on blur
                          autoFocus
                          className="w-full p-2 h-20 border rounded"
                        />
                      ) : (
                        <textarea
                          name=""
                          id=""
                          value={testFlow.UserDefinedVariables || ""}
                          className="w-full p-2 h-20 border rounded"
                          placeholder="Enter User defined variables"
                        ></textarea>
                      )}
                    </TableCell>
                    <TableCell>
                      <a
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAddRow(index);
                        }}
                        title="Add new config"
                      >
                        <Plus className="w-7 h-8 cursor-pointer text-[#1d5881]" />
                      </a>
                    </TableCell>
                    <TableCell>
                      <a
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteSelected(index);
                        }}
                        title="Delete config"
                      >
                        <img
                          src="/Group 21846.svg"
                          className="w-6 h-6 cursor-pointer hover:text-[#7b7c7a] cursor-pointer"
                        />
                      </a>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
}

export default TestSuiteUiList;

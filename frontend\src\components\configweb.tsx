import React from "react";
import {
  Table,
  TableHead,
  TableRow,
  TableHeader,
  TableBody,
  TableCell,
} from "./ui/table";
import { Plus } from "lucide-react";

export default function ConfigWeb() {
  return (
    <>
      <header className="bg-background sticky top-0 flex items-center justify-between p-[20px]">
        <div className="w-[85%]">
           <input
            type="text"
            placeholder="Search by Environment name..."
            value=""
            className="border px-3 py-2 rounded w-full"
          />
        </div>
        <div className="flex items-center gap-[20px]">
          {/* Add Element Button */}
          <a
            onClick={(e) => {
              e.stopPropagation();
            }}
            title="Add new config"
          >
            <Plus className="w-7 h-8 cursor-pointer text-[#1d5881]" />
          </a>
          {/* save elements */}
          <a
            onClick={(e) => {
              e.stopPropagation();
            }}
            className="cursor-pointer"
            title="Save new configs"
          >
            <img src="/save1.svg" className="w-7 h-8 cursor-pointer" />
          </a>
          <a
            onClick={(e) => {
              e.stopPropagation();
            }}
            title="Delete config"
          >
            <img
              src="/Group 21846.svg"
              className="w-6 h-6 cursor-pointer hover:text-[#7b7c7a] cursor-pointer"
            />
          </a>
        </div>
      </header>
      <div className="px-5">
        <Table className="table-fixed">
          <TableHeader className="bg-[#15537c]">
            <TableRow>
              <TableHead className="w-[3%]"></TableHead>
              <TableHead className="text-[#ffffff] text-center w-[8%]">
                Env Name
              </TableHead>
              <TableHead className="text-[#ffffff] text-center w-[13%]">
                URI
              </TableHead>
              <TableHead className="text-[#ffffff] text-center w-[8%]">
                Database Type
              </TableHead>
              <TableHead className="text-[#ffffff] text-center w-[10%]">
                Database Host
              </TableHead>
              <TableHead className="text-[#ffffff] text-center w-[7%]">
                Port
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell className="h-13 p-2 text-center w-[3%]">
                <input type="checkbox" />
              </TableCell>
              <TableCell className="h-13 p-2 text-center w-[8%]">
                <span>QA</span>
              </TableCell>
              <TableCell className="h-13 p-2 text-center w-[13%]">
                www.abjayon.com
              </TableCell>
              <TableCell className="h-13 p-2 text-center w-[8%]">
                Mysql
              </TableCell>
              <TableCell className="h-13 p-2 text-center w-[10%]">
                127.0.0.1
              </TableCell>
              <TableCell className="h-13 p-2 text-center w-[7%]">
                3306
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="h-13 p-2 text-center w-[3%]">
                <input type="checkbox" />
              </TableCell>
              <TableCell className="h-13 p-2 text-center w-[8%]">
                Stage
              </TableCell>
              <TableCell className="h-13 p-2 text-center w-[13%]">
                www.abjayonstage.com
              </TableCell>
              <TableCell className="h-13 p-2 text-center w-[8%]">
                MongoDB
              </TableCell>
              <TableCell className="h-13 p-2 text-center w-[10%]">
                127.0.0.2
              </TableCell>
              <TableCell className="h-13 p-2 text-center w-[7%]">
                3307
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </>
  );
}

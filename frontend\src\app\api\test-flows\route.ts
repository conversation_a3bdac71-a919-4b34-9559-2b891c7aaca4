import { NextRequest, NextResponse } from "next/server";
import { promises as fs } from "fs";
import path from "path";
import config from "@/config/config.json";

// Function to get test flows file path from config
async function getTestFlowsFilePath(): Promise<string> {
  const configPath = path.join(process.cwd(), "src", "config", "config.json");
  const configContent = await fs.readFile(configPath, "utf8");
  const config = JSON.parse(configContent);

  if (!config.testflowpath) {
    throw new Error(
      "Config.json is missing testflowpath. Please create a project first."
    );
  }

  return config.testflowpath;
}

const RECORDINGS_PATH = config.recordingsPath;

interface TestFlow {
  name: string;
  description: string;
  testcases: string[];
  originalName?: string; // For updates when name changes
}

// In-memory cache for test flows
let cachedTestFlows: TestFlow[] | null = null;
let isWriting = false;
let writeQueue: (() => void)[] = [];

// Read from file once (or refresh if needed)
async function loadTestFlows() {
  if (cachedTestFlows) {
    return cachedTestFlows;
  }
  try {
    const TEST_FLOWS_FILE_PATH = await getTestFlowsFilePath();
    const fileContents = await fs.readFile(TEST_FLOWS_FILE_PATH, "utf8");
    cachedTestFlows = JSON.parse(fileContents);
  } catch (error) {
    console.error("Error reading test flows:", error);
    cachedTestFlows = [];
  }
  return cachedTestFlows;
}

// Write cache back to file (with write queue to avoid race)
async function saveTestFlows() {
  if (isWriting) {
    // If a write is ongoing, queue this write
    await new Promise<void>((resolve) => writeQueue.push(resolve));
    return saveTestFlows();
  }

  isWriting = true;
  try {
    if (!cachedTestFlows) cachedTestFlows = [];
    const TEST_FLOWS_FILE_PATH = await getTestFlowsFilePath();
    await fs.writeFile(
      TEST_FLOWS_FILE_PATH,
      JSON.stringify(cachedTestFlows, null, 2),
      "utf8"
    );
  } catch (error) {
    console.error("Error writing test flows:", error);
    throw error;
  } finally {
    isWriting = false;
    if (writeQueue.length > 0) {
      const nextResolve = writeQueue.shift();
      nextResolve && nextResolve();
    }
  }
}

// Function to remove specific tag from all feature files
async function removeTagFromAllFeatureFiles(tagToRemove: string) {
  try {
    const fileNames = await fs.readdir(RECORDINGS_PATH);
    const featureFiles = fileNames.filter((file) => file.endsWith(".feature"));

    let removedFromCount = 0;

    for (const featureFile of featureFiles) {
      const featureFilePath = `${RECORDINGS_PATH}/${featureFile}`;

      try {
        const content = await fs.readFile(featureFilePath, "utf-8");
        const lines = content.split("\n");

        // Check if first line has tags and contains our tag
        if (lines[0].trim().startsWith("@") && lines[0].includes(tagToRemove)) {
          // Remove the specific tag from the first line
          let updatedFirstLine = lines[0];

          // Remove the tag (handle spaces properly)
          updatedFirstLine = updatedFirstLine
            .replace(
              new RegExp(
                `\\s*${tagToRemove.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}\\s*`,
                "g"
              ),
              " "
            )
            .replace(/\s+/g, " ")
            .trim();

          // If no tags left, remove the entire first line
          if (updatedFirstLine === "" || updatedFirstLine === "@") {
            lines.shift(); // Remove the empty tag line
          } else {
            lines[0] = updatedFirstLine; // Update with remaining tags
          }

          const updatedContent = lines.join("\n");
          await fs.writeFile(featureFilePath, updatedContent, "utf-8");
          removedFromCount++;

          console.log(`Removed ${tagToRemove} from ${featureFile}`);
        }
      } catch (fileError) {
        console.error(
          `Failed to process feature file ${featureFile}:`,
          fileError
        );
        // Continue with other files
      }
    }

    return removedFromCount;
  } catch (error) {
    console.error("Error removing tag from feature files:", error);
    throw error;
  }
}

// Function to add tag to specific feature files
async function addTagToFeatureFiles(tagToAdd: string, testCaseNames: string[]) {
  let addedToCount = 0;

  for (const testCaseName of testCaseNames) {
    const featureFilePath = `${RECORDINGS_PATH}/${testCaseName}.feature`;

    try {
      const content = await fs.readFile(featureFilePath, "utf-8");
      const lines = content.split("\n");

      let updatedContent;

      // Check if first line already has tags
      if (lines[0].trim().startsWith("@")) {
        // Check if tag already exists
        if (!lines[0].includes(tagToAdd)) {
          // Append new tag to existing tags
          lines[0] = `${lines[0].trim()} ${tagToAdd}`;
          updatedContent = lines.join("\n");

          await fs.writeFile(featureFilePath, updatedContent, "utf-8");
          addedToCount++;
          console.log(`Added ${tagToAdd} to ${testCaseName}.feature`);
        }
      } else {
        // No existing tags, add new tag as first line
        updatedContent = `${tagToAdd}\n${content}`;

        await fs.writeFile(featureFilePath, updatedContent, "utf-8");
        addedToCount++;
        console.log(`Added ${tagToAdd} to ${testCaseName}.feature`);
      }
    } catch (fileError) {
      console.error(
        `Failed to add tag to feature file ${testCaseName}.feature:`,
        fileError
      );
      // Continue with other files
    }
  }

  return addedToCount;
}

// GET - Fetch all test flows
export async function GET() {
  const testFlows = await loadTestFlows();
  return NextResponse.json(testFlows, { status: 200 });
}

// POST - Create a new test flow
export async function POST(request: NextRequest) {
  try {
    const newTestFlow: TestFlow = await request.json();

    if (
      !newTestFlow.name ||
      !newTestFlow.description ||
      !Array.isArray(newTestFlow.testcases)
    ) {
      return NextResponse.json(
        {
          error:
            "Invalid test flow data. Name, description, and testcases array are required.",
        },
        { status: 400 }
      );
    }

    const testFlows = await loadTestFlows();

    if (!testFlows) {
      return NextResponse.json(
        { error: "Test flows data is unavailable" },
        { status: 500 }
      );
    }

    const exists = testFlows.find((flow) => flow.name === newTestFlow.name);
    if (exists) {
      return NextResponse.json(
        {
          error: `No Two TestFlow Names Should Be Same. "${newTestFlow.name}" already exists, please choose a different name.`,
        },
        { status: 409 }
      );
    }

    testFlows.push(newTestFlow);
    cachedTestFlows = testFlows;
    await saveTestFlows();

    return NextResponse.json(
      { message: "Test flow created successfully", testFlow: newTestFlow },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating test flow:", error);
    return NextResponse.json(
      { error: "Failed to create test flow" },
      { status: 500 }
    );
  }
}

// PUT - Update an existing test flow WITH FEATURE FILE RE-TAGGING
export async function PUT(request: NextRequest) {
  try {
    const updatedTestFlow: TestFlow = await request.json();

    if (
      !updatedTestFlow.name ||
      !updatedTestFlow.description ||
      !Array.isArray(updatedTestFlow.testcases)
    ) {
      return NextResponse.json(
        {
          error:
            "Invalid test flow data. Name, description, and testcases array are required.",
        },
        { status: 400 }
      );
    }

    const testFlows = await loadTestFlows();

    if (!testFlows) {
      return NextResponse.json(
        { error: "Test flows data is unavailable" },
        { status: 500 }
      );
    }

    // Use originalName for lookup if provided, otherwise use name
    const nameToFind = updatedTestFlow.originalName || updatedTestFlow.name;
    const idx = testFlows.findIndex((flow) => flow.name === nameToFind);

    if (idx === -1) {
      return NextResponse.json(
        { error: "Test flow not found" },
        { status: 404 }
      );
    }

    // Store original name for feature file operations
    const originalName = testFlows[idx].name;
    const newName = updatedTestFlow.name;

    // Check for duplicate name (only if name is changing)
    if (originalName !== newName) {
      const duplicateExists = testFlows.find(
        (flow, index) => flow.name === newName && index !== idx
      );

      if (duplicateExists) {
        return NextResponse.json(
          {
            error: `No Two TestFlow Names Should Be Same. "${newName}" already exists, please choose a different name.`,
          },
          { status: 409 }
        );
      }
    }

    // STEP 1: Update the JSON configuration
    testFlows[idx] = {
      name: updatedTestFlow.name,
      description: updatedTestFlow.description,
      testcases: updatedTestFlow.testcases,
    };
    cachedTestFlows = testFlows;
    await saveTestFlows();

    // STEP 2: Complete Re-tagging of Feature Files
    try {
      let removedCount = 0;
      let addedCount = 0;

      // If name changed, we need to handle both old and new tags
      if (originalName !== newName) {
        // Remove the OLD tag from ALL feature files that have it
        const oldTag = `@${originalName}`;
        removedCount = await removeTagFromAllFeatureFiles(oldTag);
        console.log(`Removed ${oldTag} from ${removedCount} feature files`);

        // Add the NEW tag to ALL currently selected test cases
        const newTag = `@${newName}`;
        addedCount = await addTagToFeatureFiles(
          newTag,
          updatedTestFlow.testcases
        );
        console.log(`Added ${newTag} to ${addedCount} feature files`);
      } else {
        // Name didn't change, just do regular re-tagging
        const tagToUpdate = `@${updatedTestFlow.name}`;

        // Remove the tag from ALL feature files that have it
        removedCount = await removeTagFromAllFeatureFiles(tagToUpdate);
        console.log(
          `Removed ${tagToUpdate} from ${removedCount} feature files`
        );

        // Add the tag to ALL currently selected test cases
        addedCount = await addTagToFeatureFiles(
          tagToUpdate,
          updatedTestFlow.testcases
        );
        console.log(`Added ${tagToUpdate} to ${addedCount} feature files`);
      }

      return NextResponse.json(
        {
          message: "Test flow updated successfully",
          testFlow: updatedTestFlow,
          featureFileUpdates: {
            removedFrom: removedCount,
            addedTo: addedCount,
            nameChanged: originalName !== newName,
          },
        },
        { status: 200 }
      );
    } catch (featureFileError) {
      console.error("Error updating feature files:", featureFileError);
      // JSON was updated successfully, but feature files failed
      return NextResponse.json(
        {
          message:
            "Test flow configuration updated, but feature file update failed",
          testFlow: updatedTestFlow,
          error: "Feature file update failed",
        },
        { status: 207 } // 207 Partial Success
      );
    }
  } catch (error) {
    console.error("Error updating test flow:", error);
    return NextResponse.json(
      { error: "Failed to update test flow" },
      { status: 500 }
    );
  }
}

// DELETE - Delete a test flow
export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const testFlowName = url.searchParams.get("name");

    if (!testFlowName) {
      return NextResponse.json(
        { error: "Test flow name is required" },
        { status: 400 }
      );
    }

    const testFlows = await loadTestFlows();

    if (!testFlows) {
      return NextResponse.json(
        { error: "Test flows data is unavailable" },
        { status: 500 }
      );
    }

    const filteredFlows = testFlows.filter(
      (flow) => flow.name !== testFlowName
    );
    if (filteredFlows.length === testFlows.length) {
      return NextResponse.json(
        { error: "Test flow not found" },
        { status: 404 }
      );
    }

    cachedTestFlows = filteredFlows;
    await saveTestFlows();

    return NextResponse.json(
      { message: "Test flow deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting test flow:", error);
    return NextResponse.json(
      { error: "Failed to delete test flow" },
      { status: 500 }
    );
  }
}

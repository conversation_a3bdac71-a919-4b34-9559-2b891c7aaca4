Feature: second
  Scenario: User performs actions on https://www.abjayon.com
    Given I am on the home page
    Then I load the "elementProperties" module "Abjayon_Bring_IT_On" locators
    Then I click on "firstname_id_xpath" on "https://www.abjayon.com" page
    Then I send "firstname_id_xpath" as a "syed" in to "https://www.abjayon.com" page
    Then I send "lastname_id_xpath" as a "nauman" in to "https://www.abjayon.com" page
    Then I send "emailaddress_id_xpath" as a "uddin" in to "https://www.abjayon.com" page
    Then I send "phone_id_xpath" as a "1234566789" in to "https://www.abjayon.com" page
    Then I click on "message_id_xpath" on "https://www.abjayon.com" page
    Then I send "message_id_xpath" as a "2345" in to "https://www.abjayon.com" page
    Then I click on "input-1753718177665_id_xpath" on "https://www.abjayon.com" page
    Then I verify "div-1753718182701_id_xpath" has data "The email address entered is invalid, please check the formatting (e.g. <EMAIL>)." on "https://www.abjayon.com" page

"use client";
import React, { useState, useEffect, useMemo, useRef } from "react";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { stepDefinitions } from "@/data/stepDefinitions";
import axios from 'axios'; // import static steps

interface SaveValidatedGherkinProps {
  onCancel?: () => void;
}

const SaveValidatedGherkin: React.FC<SaveValidatedGherkinProps> = ({
  onCancel,
}) => {
  const [testCaseName, setTestCaseName] = useState("");
  const [gherkinText, setGherkinText] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // ghost suggestion state
  const [ghostSuggestion, setGhostSuggestion] = useState("");
  const [currentLineIndex, setCurrentLineIndex] = useState(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Store test case names in localStorage to ensure uniqueness
  const getSavedTestCases = (): string[] => {
    const stored = localStorage.getItem("test_case_names");
    return stored ? JSON.parse(stored) : [];
  };

  const saveTestCaseName = (name: string) => {
    const saved = getSavedTestCases();
    saved.push(name);
    localStorage.setItem("test_case_names", JSON.stringify(saved));
  };

  const validateGherkin = (gherkin: string): string | null => {
    const lines = gherkin.split("\n");
    const validSteps = ["Given", "When", "Then", "And", "But"];

    let hasFeature = false;
    let hasScenario = false;

    for (let line of lines) {
      line = line.trim();
      if (line.startsWith("Feature:")) hasFeature = true;
      if (line.startsWith("Scenario:")) hasScenario = true;

      // Basic step validation
      if (
        line &&
        !line.startsWith("Feature:") &&
        !line.startsWith("Scenario:") &&
        !validSteps.some((step) => line.startsWith(step)) &&
        !line.startsWith("#") // Allow comments
      ) {
        return `Invalid Gherkin step: "${line}"`;
      }
    }

    if (!hasFeature) return "Missing 'Feature:' declaration";
    if (!hasScenario) return "Missing 'Scenario:' declaration";

    return null; // valid
  };

  const prettifyGherkin = (gherkin: string): string => {
    return gherkin
      .split("\n")
      .map((line) => {
        let trimmed = line.trim();
        if (trimmed.startsWith("Feature:")) return trimmed;
        if (trimmed.startsWith("Scenario:")) return `  ${trimmed}`;
        if (trimmed.startsWith("#")) return trimmed; // Keep comments at original indent
        return `    ${trimmed}`;
      })
      .join("\n");
  };

  // Check if form is valid
  const isFormValid = useMemo(() => {
    const trimmedName = testCaseName.trim().replace(/\s+/g, "_");
    if (!trimmedName || !gherkinText.trim()) return false;
    if (getSavedTestCases().includes(trimmedName)) return false;
    if (validateGherkin(gherkinText) !== null) return false;
    return true;
  }, [testCaseName, gherkinText]);

  // helper: get current line content
  const getCurrentLine = (text: string, cursorPos: number) => {
    const lines = text.substring(0, cursorPos).split("\n");
    const idx = lines.length - 1;
    return { currentLine: lines[idx], lineIndex: idx };
  };

  // filter suggestions and set ghostSuggestion from first match
const updateGhostSuggestion = (currentLine: string) => {
  const trimmed = currentLine.trim();
  
  if (!trimmed) {
    setGhostSuggestion("");
    return;
  }

  const input = trimmed.toLowerCase();
  const firstChar = input[0];
  
  // Type-safe mapping with const assertion
  const typeMapping = {
    'g': 'Given',
    'w': 'When', 
    't': 'Then',
    'a': 'And',
    'b': 'But'
  } as const;
  
  // Type guard function
  const isValidStarter = (char: string): char is keyof typeof typeMapping => {
    return char in typeMapping;
  };
  
  // Step 1: Validate that input starts with valid step keyword letter
  if (!isValidStarter(firstChar)) {
    setGhostSuggestion("");
    return;
  }
  
  // Step 2: Get the target step type
  const targetType = typeMapping[firstChar];
  
  // Step 3: Filter steps by type first
  const stepsOfType = stepDefinitions.filter(s => s.type === targetType);
  
  // Step 4: Filter based on exact matching (no fallback)
  const matches = stepsOfType.filter(s => 
    s.pattern.toLowerCase().startsWith(input)
  );
  
  // Step 5: Only show suggestion if there's a real match
  if (matches.length > 0) {
    setGhostSuggestion(matches[0].pattern);
  } else {
    setGhostSuggestion(""); // No suggestion for invalid/non-matching input
  }
};



  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    const cursorPos = e.target.selectionStart;
    setGherkinText(value);

    const { currentLine, lineIndex } = getCurrentLine(value, cursorPos);
    setCurrentLineIndex(lineIndex);
    updateGhostSuggestion(currentLine);
  };

  const acceptGhostSuggestion = () => {
    const textarea = textareaRef.current;
    if (!textarea || !ghostSuggestion) return;

    const lines = gherkinText.split("\n");
    lines[currentLineIndex] = `    ${ghostSuggestion}`;
    const newText = lines.join("\n");
    setGherkinText(newText);
    setGhostSuggestion("");

    setTimeout(() => {
      textarea.focus();
      const pos = lines.slice(0, currentLineIndex + 1).join("\n").length;
      textarea.setSelectionRange(pos, pos);
    }, 0);
  };

  // Only Tab handling here
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Tab key to accept suggestion
    if (e.key === "Tab" && ghostSuggestion) {
      e.preventDefault();
      acceptGhostSuggestion();
      return;
    }
  };

  // Capture-phase native listener to intercept Escape before Dialog
  useEffect(() => {
    const el = textareaRef.current;
    if (!el) return;

    const onKeyDownCapture = (e: KeyboardEvent) => {
      if (e.key === "Escape" && ghostSuggestion) {
        e.preventDefault();
        e.stopPropagation(); // prevents shadcn/Radix Dialog from closing
        setGhostSuggestion("");
      }
    };

    el.addEventListener("keydown", onKeyDownCapture, { capture: true });
    return () => {
      el.removeEventListener("keydown", onKeyDownCapture, { capture: true });
    };
  }, [ghostSuggestion]);

  const handleSave = async () => {
    setIsLoading(true);

    const trimmedName = testCaseName.trim().replace(/\s+/g, "_");

    if (!trimmedName || !gherkinText.trim()) {
      toast({
        title: "Missing fields",
        description: "Both test-case name and Gherkin content are required.",
        variant: "destructive",
      });
    }

    const existing = getSavedTestCases();
    if (existing.includes(trimmedName)) {
      toast({
        title: "Duplicate name",
        description: "A test case with this name already exists.",
        variant: "destructive",
      });
    }

    const validationError = validateGherkin(gherkinText);
    if (validationError) {
      toast({
        title: "Invalid Gherkin",
        description: validationError,
        variant: "destructive",
      });
    }

    const prettyText = prettifyGherkin(gherkinText);
    const fileName = trimmedName + ".feature";


try {
  const response = await axios.post('/api/save-feature', {
    fileName: fileName,
    content: prettyText,
    testCaseName: trimmedName,
  });

  const data = response.data;

  if (data.success) {
    saveTestCaseName(trimmedName);
    toast({
      title: "Created",
      description: `${trimmedName} saved successfully.`,
      variant: "success",
    });

    // Close dialog first
    onCancel?.();

    // Reload page to show new test case
    window.location.reload();
  } else {
    toast({
      title: "Save failed",
      description: data.error || "Failed to save file.",
      variant: "destructive",
    });
  }
} catch (error) {
  toast({
    title: "Network error",
    description:
      error instanceof Error
        ? error.message
        : "Failed to connect to server.",
    variant: "destructive",
  });
} finally {
  setIsLoading(false);
}

  };

  return (
    <div className="w-full text-sm text-gray-800 space-y-7">
      <div className="flex items-center gap-2">
        <span className="font-medium text-gray-700 whitespace-nowrap">
          Test Case Name:
        </span>
        <input
          type="text"
          placeholder="Enter unique test case name"
          value={testCaseName}
          onChange={(e) => setTestCaseName(e.target.value)}
          className="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900"
          disabled={isLoading}
        />
      </div>

      {/* ghost suggestion wrapper */}
      <div className="relative w-full">
        {/* transparent background so overlay is visible */}
        <textarea
          ref={textareaRef}
          placeholder={`Feature: Sample Test Case
  Scenario: User performs basic actions
    Given I am on the home page
    Then I load the "elementProperties" module "Sample_Module" locators
    Then I click on "firstname" on "Sample Page" page
    Then I send "firstname" as a "John" in to "Sample Page" page
    Then I click on "submit" on "Sample Page" page`}
          value={gherkinText}
          onChange={handleTextareaChange}
          onKeyDown={handleKeyDown}
          className="w-full h-64 font-mono text-sm bg-transparent border border-gray-300 rounded-md p-4 resize-none overflow-auto relative z-10"
          style={{ color: "black", position: "relative" }}
          disabled={isLoading}
        />
        {ghostSuggestion && (
          <div
            className="absolute top-0 left-0 p-4 font-mono text-sm whitespace-pre-wrap pointer-events-none"
            style={{ color: "rgba(0,0,0,0.35)", zIndex: 5 }}
          >
            {(() => {
              const lines = gherkinText.split("\n");
              return lines.map((line, idx) => {
                if (idx === currentLineIndex) {
                  const extra = ghostSuggestion.slice(line.length);
                  return (
                    <div key={idx}>
                      <span style={{ color: "transparent" }}>{line}</span>
                      <span>{extra}</span>
                    </div>
                  );
                }
                return <div key={idx}>{line}</div>;
              });
            })()}
          </div>
        )}
      </div>

      <div className="flex justify-end gap-3">
        <button
          onClick={() => {
            setTestCaseName("");
            setGherkinText("");
            onCancel?.();
          }}
          className="flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200 text-gray-700 font-medium shadow-sm"
          disabled={isLoading}
        >
          Cancel
        </button>
        <Button
          onClick={handleSave}
          disabled={!isFormValid || isLoading}
          className={`bg-green-600 text-white hover:bg-green-700 ${
            !isFormValid || isLoading
              ? "opacity-50 cursor-not-allowed hover:bg-green-600"
              : ""
          }`}
        >
          {isLoading ? "Saving..." : "Create"}
        </Button>
      </div>
    </div>
  );
};

export default SaveValidatedGherkin;

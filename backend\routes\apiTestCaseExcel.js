const express = require('express');
const router = express.Router();

const { 
  addDataController, 
  deleteDataController, 
  updateDataController, 
  readDataController 
} = require('../controllers/apiTestCaseExcelController');

router.post('/add', addDataController);
router.delete('/delete', deleteDataController);
router.put('/update', updateDataController);
router.get('/read', readDataController);

module.exports = router;
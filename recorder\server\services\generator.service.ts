import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import { Recording } from '@shared/schema';
import { RecordedAction } from '@shared/types';

const writeFileAsync = promisify(fs.writeFile);

export class GeneratorService {
  
  async generateJsonFile(recording: Recording, filePath: string): Promise<string> {
    try {
      // Ensure the directory exists
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      
      const actions = recording.actions as RecordedAction[];
      
      // Format the JSON with indentation for readability
      const jsonContent = JSON.stringify(actions, null, 2);
      
      // Write to file
      await writeFileAsync(filePath, jsonContent, 'utf8');
      
      return filePath;
    } catch (error) {
      console.error('Error generating JSON file:', error);
      throw new Error(`Failed to generate JSON file: ${(error as Error).message}`);
    }
  }
  
  async generateFeatureFile(recording: Recording, filePath: string): Promise<string> {
    try {
      // Ensure the directory exists
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      
      const actions = recording.actions as RecordedAction[];
      
      // Generate feature file content
      const featureContent = this.convertToFeatureFile(recording.name, recording.url, actions, recording.title,recording.scenario);
      
      // Write to file
      await writeFileAsync(filePath, featureContent, 'utf8');
      
      return filePath;
    } catch (error) {
      console.error('Error generating feature file:', error);
      throw new Error(`Failed to generate feature file: ${(error as Error).message}`);
    }
  }
  
  private convertToFeatureFile(name: string, initialUrl: string, actions: RecordedAction[], title: string,scenario:string): string {
    // Format the recording name for the feature title
    const featureTitle = name.replace(/[_\s]+/g, ' ').trim();
    
    // Start building feature file
    let featureContent = `Feature: ${featureTitle}\n`;
    featureContent += `  Scenario: ${scenario}\n`;
    featureContent += `    Given I am on the home page\n`;
    featureContent += `    Then I load the "elementProperties" module "${title}" locators\n`;
    console.log(`title is ${title}`);
    console.log(`actions`, actions);
    
    // Add each action as a step
    actions.forEach(action => {
      switch (action.action) {
        // case 'navigate':
        //   if (action.url && action.url !== initialUrl) {
        //     featureContent += `    Then I should be navigated to "${action.url}"\n`;
        //   }
        //   break;
          
        case 'click':
          if (action.selector) {
            featureContent += `    Then I click on "${action.label}" on "${action.title}" page\n`;
          }
          break;
          
        case 'type':
          if (action.selector && action.value) {
            featureContent += `    Then I send "${action.label}" as a "${action.value}" in to "${action.title}" page\n`;
          }
          break;
          
        // case 'select':
        //   if (action.selector && action.value) {
        //     featureContent += `    Then I select "${action.label}" with data "${action.value}" on "${action.url || initialUrl}" page\n`;
        //   }
        //   break;

        // case 'scroll':
        //   if (action.scrollDirection) {
        //     featureContent += `    Then I scroll "${action.scrollDirection}" on "${initialUrl}" page\n`;
        //   } else if (action.scrollX !== undefined && action.scrollY !== undefined) {
        //     featureContent += `    Then I scroll to position X:"${action.scrollX}" Y:"${action.scrollY}" on "${initialUrl}" page\n`;
        //   }
        //   break;

        // case 'rightclick':
        //   if (action.selector) {
        //     featureContent += `    Then I right click on "${action.label}" on "${initialUrl}" page\n`;
        //   }
        //   break;

        // UPDATED: Single unified assertion format (replaced complex switch statement)
        case 'assertion':
          if (action.selector && action.assertionText) {
            featureContent += `    Then I verify "${action.label}" has data "${action.assertionText}" on "${action.title}" page\n`;
          }
          break;

        default:
          // Handle any unknown action types gracefully
          console.warn(`Unknown action type: ${action.action}`);
          break;
      }
    });
    
    return featureContent;
  }
}
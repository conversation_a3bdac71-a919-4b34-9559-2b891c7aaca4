import { NextResponse } from 'next/server';
import * as fs from 'fs';
import * as path from 'path';

interface ConfigRow {
  id: number;
  data: {
    envName: string;
    baseUrl: string;
    dbName: string;
    dbCredentials: string;
  };
}

function getConfigPath(): string {
  // Read config.json from the correct location
  const configFilePath = path.join(process.cwd(), 'src', 'config', 'config.json');
  const configContent = fs.readFileSync(configFilePath, 'utf8');
  const config = JSON.parse(configContent);
  
  return config.projectConfigurations;
}

export async function GET() {
  try {
    // Get dynamic path from config.json
    const filePath = getConfigPath();
    
    console.log('Reading from dynamic path:', filePath);
    
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return NextResponse.json({ 
        error: 'Configuration file not found',
        path: filePath 
      }, { status: 404 });
    }

    // Read file content
    const fileContent = fs.readFileSync(filePath, 'utf8');
    
    // Parse the properties file
    const configRows = parsePropertiesFile(fileContent);
    
    return NextResponse.json(configRows);
    
  } catch (error) {
    console.error('Error reading config file:', error);
    return NextResponse.json({ error: 'Failed to read configuration file' }, { status: 500 });
  }
}

// NEW: PUT method for saving/updating configurations
export async function PUT(request: Request) {
  try {
    // Get the updated data from request
    const updatedRows: ConfigRow[] = await request.json();
    
    console.log(' Updating configuration with:', updatedRows);
    
    // Get dynamic path from config.json (using your existing function)
    const filePath = getConfigPath();
    
    // Convert back to properties format
    const propertiesContent = convertToPropertiesFormat(updatedRows);
    
    console.log(' Generated properties content:', propertiesContent);
    
    // Write to file
    fs.writeFileSync(filePath, propertiesContent, 'utf8');
    
    console.log(' Configuration updated successfully to file:', filePath);
    
    return NextResponse.json({ 
      success: true, 
      message: 'Configuration updated successfully' 
    });
    
  } catch (error) {
    console.error(' Error updating config file:', error);
    return NextResponse.json({ 
      error: 'Failed to update configuration file' 
    }, { status: 500 });
  }
}

function parsePropertiesFile(content: string): ConfigRow[] {
  const lines = content.split('\n').filter(line => line.trim() && !line.startsWith('#'));
  const envGroups: { [key: string]: any } = {};
  
  lines.forEach(line => {
    const [key, value] = line.split('=').map(part => part.trim());
    if (!key || !value) return;
    
    // Check if key contains underscore (grouped properties)
    if (key.includes('_')) {
      const [envName, propertyType] = key.split('_', 2);
      
      if (!envGroups[envName]) {
        envGroups[envName] = {
          envName,
          baseUrl: '',
          dbName: '',
          dbCredentials: ''
        };
      }
      
      // Map property types to our fields
      switch (propertyType.toLowerCase()) {
        case 'applicationhome':  // this case is for your properties file
        case 'baseurl':          // this is for backward compatibility
          envGroups[envName].baseUrl = value;
          break;
        case 'dbname':
          envGroups[envName].dbName = value;
          break;
        case 'bdcredentials':
        case 'dbcredentials':
          envGroups[envName].dbCredentials = value;
          break;
      }
    } else {
      // Single property without underscore (like applicationHome)
      envGroups[key] = {
        envName: key,
        baseUrl: value,
        dbName: '',
        dbCredentials: ''
      };
    }
  });
  
  // Convert to array with IDs
  const configRows: ConfigRow[] = Object.values(envGroups).map((config, index) => ({
    id: Date.now() + index,
    data: config
  }));
  
  return configRows;
}

// NEW: Helper function to convert data back to properties format
function convertToPropertiesFormat(rows: ConfigRow[]): string {
  let content = '';
  
  rows.forEach((row) => {
    const { envName, baseUrl, dbName, dbCredentials } = row.data;
    
    // Only add fields that have values
    if (baseUrl && baseUrl.trim()) {
      content += `${envName}_applicationHome=${baseUrl}\n`;
    }
    
    if (dbName && dbName.trim()) {
      content += `${envName}_dbname=${dbName}\n`;
    }
    
    if (dbCredentials && dbCredentials.trim()) {
      content += `${envName}_dbcredentials=${dbCredentials}\n`;
    }
  });
  
  return content;
}


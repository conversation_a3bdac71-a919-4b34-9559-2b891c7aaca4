"use client";

import api from '@/app/services/api';

import { useRouter } from "next/navigation";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import React from "react";

export function LoginForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const router = useRouter();
  const [email, setEmail] = React.useState("");
  const [password, setPassword] = React.useState("");
  const [message, setMessage] = React.useState('');

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();


    // In a real application, you would send 'email' and 'password' to your authentication API
    console.log("Attempting login with:", { email, password });

    try {
      const res = await api.post('/login', {
        email,
        password
      });
      //setMessage(JSON.stringify(res.data));
      
      localStorage.setItem("loggedInUser", email);
      console.log("stored logged in user", email);
      localStorage.setItem("antp_token", res.data.accessToken); // Set a dummy token for demonstration
      localStorage.setItem('antp_refresh_token', res.data.refreshToken);  // Store refresh token
      router.push("/projectmanagement");
      
    } catch (err: any) { 
      console.error(err);
      setMessage('Failed: ' + err.message);
      setEmail('');
      setPassword('');
    }
  };

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card
        className="font-light text-[19px]
      border-0
      pt-6
      pb-0
      gap-8
      leading-[27px]
      tracking-[0px]
      text-[#15537C]
      w-[350px]
      h-auto
      mx-auto
      mr-[220px]
      bg-[#EFF2F4]
      bg-no-repeat
      bg-left-top
      opacity-100
     "
      >
        <CardHeader className="text-center">
          <CardTitle className="font-normal text-[25px] leading-[24px]">
            Welcome to ANTP
          </CardTitle>
          <CardDescription className="font-light text-[15px] leading-[20px]">
            Abjayon No Code Test Platform
          </CardDescription>
        </CardHeader>
        <CardContent className="pb-0 mb-0">
          <form onSubmit={handleSubmit}>
            <div className="flex flex-col gap-6">
              <div className="grid gap-3">
                <Label htmlFor="email">Email</Label>
                <Input
                  className="bg-[#FFFFFF]"
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
              <div className="grid gap-3">
                <div className="flex items-center">
                  <Label htmlFor="password">Password</Label>
                  <a
                    href="#"
                    className="ml-auto inline-block text-sm underline-offset-4 hover:underline"
                  >
                    Forgot your password?
                  </a>
                </div>
                <Input
                  id="password"
                  type="password"
                  className="bg-[#FFFFFF]"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
              </div>
              <div className="flex flex-col gap-3">
                <Button
                  className="bg-no-repeat bg-left-top opacity-100 shadow-[0px_3px_6px_#00000029] rounded-lg bg-green-700"
                  type="submit"
                >
                  Login
                </Button>
                {message && <p>{message}</p>}
              </div>
            </div>
          </form>
        </CardContent>
        <CardFooter
          className="m-0 p-0
        border-0
        justify-end
        bg-[url('/login-icon/CARDBOTTOM.svg')]
        bg-contain
        bg-no-repeat
        bg-bottom w-full h-auto
        "
        >
          <Image
            src="/login-icon/impressaai.svg"
            alt="Impressa AI"
            width={100}
            height={30}
            className="m-0 pb-8 pr-6 leading-none"
          />
        </CardFooter>
      </Card>
    </div>
  );
}

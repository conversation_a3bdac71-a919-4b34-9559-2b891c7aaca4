<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="1931.5" height="76.5" viewBox="0 0 1931.5 76.5">
  <defs>
    <filter id="Footer-bg" x="0" y="0" width="1931.5" height="76.5" filterUnits="userSpaceOnUse">
      <feOffset dx="10" dy="10" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="0.5" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g transform="matrix(1, 0, 0, 1, 0, 0)" filter="url(#Footer-bg)">
    <rect id="Footer-bg-2" data-name="Footer-bg" width="1920" height="65" opacity="0.2"/>
  </g>
</svg>

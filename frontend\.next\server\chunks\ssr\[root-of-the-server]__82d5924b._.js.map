{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/antpui/frontend/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Avatar({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn(\r\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarImage({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn(\"aspect-square size-full\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn(\r\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/antpui/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/antpui/frontend/src/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nconst MOBILE_BREAKPOINT = 768;\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean>(() => {\r\n    if (typeof window === \"undefined\") {\r\n      return false; // Default to false on server, or handle as needed\r\n    }\r\n    return window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`).matches;\r\n  });\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);\r\n    const onChange = (event: MediaQueryListEvent) => {\r\n      setIsMobile(event.matches);\r\n    };\r\n    mql.addEventListener(\"change\", onChange);\r\n    return () => mql.removeEventListener(\"change\", onChange);\r\n  }, []);\r\n\r\n  return !!isMobile;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAW;QACtD,wCAAmC;YACjC,OAAO,OAAO,kDAAkD;QAClE;;IAEF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW,CAAC;YAChB,YAAY,MAAM,OAAO;QAC3B;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/antpui/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/antpui/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 457, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/antpui/frontend/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Separator({\r\n  className,\r\n  orientation = \"horizontal\",\r\n  decorative = true,\r\n  ...props\r\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\r\n  return (\r\n    <SeparatorPrimitive.Root\r\n      data-slot=\"separator-root\"\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/antpui/frontend/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\r\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\r\n}\r\n\r\nfunction SheetTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\r\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\r\n}\r\n\r\nfunction SheetClose({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\r\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\r\n}\r\n\r\nfunction SheetPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\r\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\r\n}\r\n\r\nfunction SheetOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\r\n  return (\r\n    <SheetPrimitive.Overlay\r\n      data-slot=\"sheet-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetContent({\r\n  className,\r\n  children,\r\n  side = \"right\",\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\r\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\r\n}) {\r\n  return (\r\n    <SheetPortal>\r\n      <SheetOverlay />\r\n      <SheetPrimitive.Content\r\n        data-slot=\"sheet-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n          side === \"right\" &&\r\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\r\n          side === \"left\" &&\r\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\r\n          side === \"top\" &&\r\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\r\n          side === \"bottom\" &&\r\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\r\n          <XIcon className=\"size-4\" />\r\n          <span className=\"sr-only\">Close</span>\r\n        </SheetPrimitive.Close>\r\n      </SheetPrimitive.Content>\r\n    </SheetPortal>\r\n  )\r\n}\r\n\r\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-header\"\r\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-footer\"\r\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\r\n  return (\r\n    <SheetPrimitive.Title\r\n      data-slot=\"sheet-title\"\r\n      className={cn(\"text-foreground font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\r\n  return (\r\n    <SheetPrimitive.Description\r\n      data-slot=\"sheet-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sheet,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 659, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/antpui/frontend/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 684, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/antpui/frontend/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot=\"tooltip-provider\"\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction Tooltip({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return (\r\n    <TooltipProvider>\r\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\r\n    </TooltipProvider>\r\n  )\r\n}\r\n\r\nfunction TooltipTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 0,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot=\"tooltip-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  )\r\n}\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,8OAAC,mKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,8OAAC;kBACC,cAAA,8OAAC,mKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,8OAAC,mKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 769, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/antpui/frontend/src/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { VariantProps, cva } from \"class-variance-authority\"\r\nimport { PanelLeftIcon } from \"lucide-react\"\r\n\r\nimport { useIsMobile } from \"@/hooks/use-mobile\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Separator } from \"@/components/ui/separator\"\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetHeader,\r\n  SheetTitle,\r\n} from \"@/components/ui/sheet\"\r\nimport { Skeleton } from \"@/components/ui/skeleton\"\r\nimport {\r\n  Toolt<PERSON>,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\"\r\n\r\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\"\r\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\r\nconst SIDEBAR_WIDTH = \"16rem\"\r\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\r\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\r\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\r\n\r\ntype SidebarContextProps = {\r\n  state: \"expanded\" | \"collapsed\"\r\n  open: boolean\r\n  setOpen: (open: boolean) => void\r\n  openMobile: boolean\r\n  setOpenMobile: (open: boolean) => void\r\n  isMobile: boolean\r\n  toggleSidebar: () => void\r\n}\r\n\r\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null)\r\n\r\nfunction useSidebar() {\r\n  const context = React.useContext(SidebarContext)\r\n  if (!context) {\r\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\r\n  }\r\n\r\n  return context\r\n}\r\n\r\nfunction SidebarProvider({\r\n  defaultOpen = true,\r\n  open: openProp,\r\n  onOpenChange: setOpenProp,\r\n  className,\r\n  style,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  defaultOpen?: boolean\r\n  open?: boolean\r\n  onOpenChange?: (open: boolean) => void\r\n}) {\r\n  const isMobile = useIsMobile()\r\n  const [openMobile, setOpenMobile] = React.useState(false)\r\n\r\n  // This is the internal state of the sidebar.\r\n  // We use openProp and setOpenProp for control from outside the component.\r\n  const [_open, _setOpen] = React.useState(defaultOpen)\r\n  const open = openProp ?? _open\r\n  const setOpen = React.useCallback(\r\n    (value: boolean | ((value: boolean) => boolean)) => {\r\n      const openState = typeof value === \"function\" ? value(open) : value\r\n      if (setOpenProp) {\r\n        setOpenProp(openState)\r\n      } else {\r\n        _setOpen(openState)\r\n      }\r\n\r\n      // This sets the cookie to keep the sidebar state.\r\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\r\n    },\r\n    [setOpenProp, open]\r\n  )\r\n\r\n  // Helper to toggle the sidebar.\r\n  const toggleSidebar = React.useCallback(() => {\r\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open)\r\n  }, [isMobile, setOpen, setOpenMobile])\r\n\r\n  // Adds a keyboard shortcut to toggle the sidebar.\r\n  React.useEffect(() => {\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      if (\r\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\r\n        (event.metaKey || event.ctrlKey)\r\n      ) {\r\n        event.preventDefault()\r\n        toggleSidebar()\r\n      }\r\n    }\r\n\r\n    window.addEventListener(\"keydown\", handleKeyDown)\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown)\r\n  }, [toggleSidebar])\r\n\r\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\r\n  // This makes it easier to style the sidebar with Tailwind classes.\r\n  const state = open ? \"expanded\" : \"collapsed\"\r\n\r\n  const contextValue = React.useMemo<SidebarContextProps>(\r\n    () => ({\r\n      state,\r\n      open,\r\n      setOpen,\r\n      isMobile,\r\n      openMobile,\r\n      setOpenMobile,\r\n      toggleSidebar,\r\n    }),\r\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\r\n  )\r\n\r\n  return (\r\n    <SidebarContext.Provider value={contextValue}>\r\n      <TooltipProvider delayDuration={0}>\r\n        <div\r\n          data-slot=\"sidebar-wrapper\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH,\r\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\r\n              ...style,\r\n            } as React.CSSProperties\r\n          }\r\n          className={cn(\r\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\r\n            className\r\n          )}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      </TooltipProvider>\r\n    </SidebarContext.Provider>\r\n  )\r\n}\r\n\r\nfunction Sidebar({\r\n  side = \"left\",\r\n  variant = \"sidebar\",\r\n  collapsible = \"offcanvas\",\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  side?: \"left\" | \"right\"\r\n  variant?: \"sidebar\" | \"floating\" | \"inset\"\r\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\"\r\n}) {\r\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar()\r\n\r\n  if (collapsible === \"none\") {\r\n    return (\r\n      <div\r\n        data-slot=\"sidebar\"\r\n        className={cn(\r\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n      </div>\r\n    )\r\n  }\r\n\r\n  if (isMobile) {\r\n    return (\r\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n        <SheetContent\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar\"\r\n          data-mobile=\"true\"\r\n          className=\"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\r\n            } as React.CSSProperties\r\n          }\r\n          side={side}\r\n        >\r\n          <SheetHeader className=\"sr-only\">\r\n            <SheetTitle>Sidebar</SheetTitle>\r\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\r\n          </SheetHeader>\r\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\r\n        </SheetContent>\r\n      </Sheet>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"group peer text-sidebar-foreground hidden md:block\"\r\n      data-state={state}\r\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\r\n      data-variant={variant}\r\n      data-side={side}\r\n      data-slot=\"sidebar\"\r\n    >\r\n      {/* This is what handles the sidebar gap on desktop */}\r\n      <div\r\n        data-slot=\"sidebar-gap\"\r\n        className={cn(\r\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\",\r\n          \"group-data-[collapsible=offcanvas]:w-0\",\r\n          \"group-data-[side=right]:rotate-180\",\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\"\r\n        )}\r\n      />\r\n      <div\r\n        data-slot=\"sidebar-container\"\r\n        className={cn(\r\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\",\r\n          side === \"left\"\r\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\r\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\r\n          // Adjust the padding for floating and inset variants.\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <div\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar-inner\"\r\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\r\n        >\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction SidebarTrigger({\r\n  className,\r\n  onClick,\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { toggleSidebar } = useSidebar()\r\n\r\n  return (\r\n    <Button\r\n      data-sidebar=\"trigger\"\r\n      data-slot=\"sidebar-trigger\"\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={cn(\"size-7\", className)}\r\n      onClick={(event) => {\r\n        onClick?.(event)\r\n        toggleSidebar()\r\n      }}\r\n      {...props}\r\n    >\r\n      <PanelLeftIcon />\r\n      <span className=\"sr-only\">Toggle Sidebar</span>\r\n    </Button>\r\n  )\r\n}\r\n\r\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\r\n  const { toggleSidebar } = useSidebar()\r\n\r\n  return (\r\n    <button\r\n      data-sidebar=\"rail\"\r\n      data-slot=\"sidebar-rail\"\r\n      aria-label=\"Toggle Sidebar\"\r\n      tabIndex={-1}\r\n      onClick={toggleSidebar}\r\n      title=\"Toggle Sidebar\"\r\n      className={cn(\r\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\",\r\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\r\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\r\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\r\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\r\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\r\n  return (\r\n    <main\r\n      data-slot=\"sidebar-inset\"\r\n      className={cn(\r\n        \"bg-background relative flex w-full flex-1 flex-col\",\r\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Input>) {\r\n  return (\r\n    <Input\r\n      data-slot=\"sidebar-input\"\r\n      data-sidebar=\"input\"\r\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-header\"\r\n      data-sidebar=\"header\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-footer\"\r\n      data-sidebar=\"footer\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Separator>) {\r\n  return (\r\n    <Separator\r\n      data-slot=\"sidebar-separator\"\r\n      data-sidebar=\"separator\"\r\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-content\"\r\n      data-sidebar=\"content\"\r\n      className={cn(\r\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group\"\r\n      data-sidebar=\"group\"\r\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroupLabel({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"div\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-label\"\r\n      data-sidebar=\"group-label\"\r\n      className={cn(\r\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroupAction({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-action\"\r\n      data-sidebar=\"group-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroupContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group-content\"\r\n      data-sidebar=\"group-content\"\r\n      className={cn(\"w-full text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu\"\r\n      data-sidebar=\"menu\"\r\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-item\"\r\n      data-sidebar=\"menu-item\"\r\n      className={cn(\"group/menu-item relative\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nconst sidebarMenuButtonVariants = cva(\r\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\r\n        outline:\r\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\r\n      },\r\n      size: {\r\n        default: \"h-8 text-sm\",\r\n        sm: \"h-7 text-xs\",\r\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction SidebarMenuButton({\r\n  asChild = false,\r\n  isActive = false,\r\n  variant = \"default\",\r\n  size = \"default\",\r\n  tooltip,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean\r\n  isActive?: boolean\r\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>\r\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n  const { isMobile, state } = useSidebar()\r\n\r\n  const button = (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-button\"\r\n      data-sidebar=\"menu-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n\r\n  if (!tooltip) {\r\n    return button\r\n  }\r\n\r\n  if (typeof tooltip === \"string\") {\r\n    tooltip = {\r\n      children: tooltip,\r\n    }\r\n  }\r\n\r\n  return (\r\n    <Tooltip>\r\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\r\n      <TooltipContent\r\n        side=\"right\"\r\n        align=\"center\"\r\n        hidden={state !== \"collapsed\" || isMobile}\r\n        {...tooltip}\r\n      />\r\n    </Tooltip>\r\n  )\r\n}\r\n\r\nfunction SidebarMenuAction({\r\n  className,\r\n  asChild = false,\r\n  showOnHover = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean\r\n  showOnHover?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-action\"\r\n      data-sidebar=\"menu-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        showOnHover &&\r\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuBadge({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-badge\"\r\n      data-sidebar=\"menu-badge\"\r\n      className={cn(\r\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\r\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSkeleton({\r\n  className,\r\n  showIcon = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  showIcon?: boolean\r\n}) {\r\n  // Random width between 50 to 90%.\r\n  const width = React.useMemo(() => {\r\n    return `${Math.floor(Math.random() * 40) + 50}%`\r\n  }, [])\r\n\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-skeleton\"\r\n      data-sidebar=\"menu-skeleton\"\r\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\r\n      {...props}\r\n    >\r\n      {showIcon && (\r\n        <Skeleton\r\n          className=\"size-4 rounded-md\"\r\n          data-sidebar=\"menu-skeleton-icon\"\r\n        />\r\n      )}\r\n      <Skeleton\r\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\r\n        data-sidebar=\"menu-skeleton-text\"\r\n        style={\r\n          {\r\n            \"--skeleton-width\": width,\r\n          } as React.CSSProperties\r\n        }\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu-sub\"\r\n      data-sidebar=\"menu-sub\"\r\n      className={cn(\r\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSubItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-sub-item\"\r\n      data-sidebar=\"menu-sub-item\"\r\n      className={cn(\"group/menu-sub-item relative\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSubButton({\r\n  asChild = false,\r\n  size = \"md\",\r\n  isActive = false,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean\r\n  size?: \"sm\" | \"md\"\r\n  isActive?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-sub-button\"\r\n      data-sidebar=\"menu-sub-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\r\n        size === \"sm\" && \"text-xs\",\r\n        size === \"md\" && \"text-sm\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupAction,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarInput,\r\n  SidebarInset,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuBadge,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSkeleton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarProvider,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n  SidebarTrigger,\r\n  useSidebar,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AApBA;;;;;;;;;;;;;;AA2BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAA8B;AAEvE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,gBAAgB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAC9B,CAAC;QACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;QAC9D,IAAI,aAAa;YACf,YAAY;QACd,OAAO;YACL,SAAS;QACX;QAEA,kDAAkD;QAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;IACpG,GACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QACtC,OAAO,WAAW,cAAc,CAAC,OAAS,CAAC,QAAQ,QAAQ,CAAC,OAAS,CAAC;IACxE,GAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,gBAAgB,CAAC;YACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;gBACA,MAAM,cAAc;gBACpB;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAC/B,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,8OAAC,mIAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,8OAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,QAAQ,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,8OAAC,iIAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,8OAAC,iIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC,iIAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,8OAAC,iIAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,8OAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,8OAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,8OAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,8OAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC;IACpC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,8OAAC,oNAAA,CAAA,gBAAa;;;;;0BACd,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAuC;IAC1E,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mPACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAqC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACgC;IACnC,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACoC;IACvC,qBACE,8OAAC,qIAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD;IACpD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD;IACvD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAmC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAmC;IAC1E,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,MAAM,4BAA4B,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAClC,qzBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C;IAChD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,MAAM,uBACJ,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC3D,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC,mIAAA,CAAA,UAAO;;0BACN,8OAAC,mIAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,8OAAC,mIAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oVACA,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ;IACC,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;IAClD,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1418, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/antpui/frontend/src/components/nav-user.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useRouter } from \"next/navigation\";\r\nimport { ChevronsUpDown, LogOut } from \"lucide-react\";\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport {\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  useSidebar,\r\n} from \"@/components/ui/sidebar\";\r\n\r\nexport function NavUser({\r\n  user,\r\n}: {\r\n  user: {\r\n    name: string;\r\n    email: string;\r\n    avatar: string;\r\n  };\r\n}) {\r\n  const { isMobile } = useSidebar();\r\n  const router = useRouter();\r\n\r\n  const handleSignOut = () => {\r\n    localStorage.removeItem(\"authToken\");\r\n    router.push(\"/login\");\r\n  };\r\n\r\n  return (\r\n    <SidebarMenu>\r\n      <SidebarMenuItem>\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <SidebarMenuButton\r\n              size=\"sm\"\r\n              className=\"data-[state=open]:bg-sidebar-accent\r\n               data-[state=open]:text-sidebar-accent-foreground md:h-8 md:p-0\"\r\n            >\r\n              <Avatar className=\"h-8 w-8 rounded-lg\">\r\n                <AvatarImage src={user.avatar} alt={user.name} />\r\n                <AvatarFallback className=\"rounded-lg\">SY</AvatarFallback>\r\n              </Avatar>\r\n              <ChevronsUpDown className=\"ml-auto size-4\" />\r\n            </SidebarMenuButton>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent\r\n            className=\"w-[var(--radix-dropdown-menu-trigger-width)] min-w-56 rounded-lg\"\r\n            side={isMobile ? \"bottom\" : \"right\"}\r\n            align=\"end\"\r\n            sideOffset={4}\r\n          >\r\n            <DropdownMenuLabel className=\"p-0 font-normal\">\r\n              <div className=\"flex items-center gap-2 px-1 py-1.5 text-left text-sm\">\r\n                <Avatar className=\"h-8 w-8 rounded-lg\">\r\n                  <AvatarImage src={user.avatar} alt={user.name} />\r\n                  <AvatarFallback className=\"rounded-lg\">SY</AvatarFallback>\r\n                </Avatar>\r\n                <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                  <span className=\"truncate font-medium\">{user.name}</span>\r\n                  <span className=\"truncate text-xs\">{user.email}</span>\r\n                </div>\r\n              </div>\r\n            </DropdownMenuLabel>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuItem onClick={handleSignOut}>\r\n              <LogOut className=\"mr-2 h-4 w-4\" />\r\n              Log out\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </SidebarMenuItem>\r\n    </SidebarMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AACA;AASA;AAfA;;;;;;;AAsBO,SAAS,QAAQ,EACtB,IAAI,EAOL;IACC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IAC9B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB;QACpB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,8OAAC,mIAAA,CAAA,cAAW;kBACV,cAAA,8OAAC,mIAAA,CAAA,kBAAe;sBACd,cAAA,8OAAC,4IAAA,CAAA,eAAY;;kCACX,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;4BAChB,MAAK;4BACL,WAAU;;8CAGV,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,8OAAC,kIAAA,CAAA,cAAW;4CAAC,KAAK,KAAK,MAAM;4CAAE,KAAK,KAAK,IAAI;;;;;;sDAC7C,8OAAC,kIAAA,CAAA,iBAAc;4CAAC,WAAU;sDAAa;;;;;;;;;;;;8CAEzC,8OAAC,8NAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG9B,8OAAC,4IAAA,CAAA,sBAAmB;wBAClB,WAAU;wBACV,MAAM,WAAW,WAAW;wBAC5B,OAAM;wBACN,YAAY;;0CAEZ,8OAAC,4IAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAC3B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAK,KAAK,MAAM;oDAAE,KAAK,KAAK,IAAI;;;;;;8DAC7C,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAU;8DAAa;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAwB,KAAK,IAAI;;;;;;8DACjD,8OAAC;oDAAK,WAAU;8DAAoB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;0CAIpD,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0CACtB,8OAAC,4IAAA,CAAA,mBAAgB;gCAAC,SAAS;;kDACzB,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}, {"offset": {"line": 1618, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/antpui/frontend/src/components/dashboard-sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport Image from \"next/image\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport { NavUser } from \"@/components/nav-user\";\r\nimport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupContent,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  useSidebar,\r\n} from \"@/components/ui/sidebar\";\r\n\r\n// This is sample data\r\nconst data = {\r\n  user: {\r\n    name: \"User\",\r\n    email: \"<EMAIL>\",\r\n    avatar: \"\", // Added for NavUser component\r\n  },\r\n  navMain: [\r\n    {\r\n      title: \"ProjectManagement\",\r\n      url: \"/projectmanagement\",\r\n      icon: \"/dashboard-icons/newproject.svg\",\r\n      isActive: false,\r\n    },\r\n    {\r\n      title: \"Dashboard\",\r\n      url: \"/dashboard\",\r\n      icon: \"/dashboard-icons/dashboard.svg\",\r\n      isActive: true,\r\n    },\r\n    {\r\n      title: \"TestCaseManagement\",\r\n      url: \"/testcase\",\r\n      icon: \"/dashboard-icons/projectmanagement.svg\",\r\n      isActive: true,\r\n    },\r\n    {\r\n      title: \"TestFlows\",\r\n      url: \"/testFlowView\",\r\n      icon: \"/dashboard-icons/testflows.svg\",\r\n      isActive: false,\r\n    },\r\n    {\r\n      title: \"TestSuites\",\r\n      url: \"/testsuites\",\r\n      icon: \"/dashboard-icons/testsuite.svg\",\r\n      isActive: false,\r\n    },\r\n     {\r\n      title: \"Test Suites\",\r\n      url: \"/testsuitesapi\",\r\n      icon: \"/dashboard-icons/testsuite.svg\",\r\n      isActive: false,\r\n    },\r\n    // {\r\n    //   title: \"projectconfigurations\",\r\n    //   url: \"/projectconfigurations\",\r\n    //   icon: \"/dashboard-icons/testsuite.svg\",\r\n    //   isActive: false,\r\n    // },\r\n    // {\r\n    //   title: \"APIs\",\r\n    //   url: \"/testapis\",\r\n    //   icon: \"/dashboard-icons/testapi.svg\",\r\n    //   isActive: false,\r\n    // },\r\n    // {\r\n    //   title: \"Test Flows\",\r\n    //   url: \"/testflowsui\",\r\n    //   icon: \"/dashboard-icons/testflowsui.svg\",\r\n    //   isActive: false,\r\n    // },\r\n    {\r\n      title: \"Configs\",\r\n      url: \"/config\",\r\n      icon: \"/dashboard-icons/config.svg\",\r\n      isActive: true,\r\n    },\r\n  ],\r\n};\r\n\r\nexport function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {\r\n  // Note: I'm using state to show active item.\r\n  // IRL you should use the url/router.\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const { setOpen } = useSidebar();\r\n\r\n  const [activeItem, setActiveItem] = React.useState(\r\n    () => data.navMain.find((item) => item.url === pathname) || data.navMain[0]\r\n  );\r\n\r\n  React.useEffect(() => {\r\n    const currentItem = data.navMain.find((item) => item.url === pathname);\r\n    if (currentItem) {\r\n      setActiveItem(currentItem);\r\n    }\r\n  }, [pathname]);\r\n\r\n  const handleNavClick = (item: typeof data.navMain[0]) => {\r\n    setOpen(true);\r\n    \r\n    // If clicking on ProjectManagement, force a refresh\r\n    if (item.title === \"ProjectManagement\") {\r\n      // Add a timestamp to force route refresh\r\n      const currentPath = item.url;\r\n      router.push(`${currentPath}?refresh=${Date.now()}`);\r\n    } else {\r\n      router.push(item.url);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Sidebar {...props}>\r\n      {/* This is the first sidebar */}\r\n      {/* We disable collapsible and adjust width to icon. */}\r\n      {/* This will make the sidebar appear as icons. */}\r\n      <Sidebar\r\n        collapsible=\"none\"\r\n        className=\"bg-[#15537C] \r\n        bg-no-repeat \r\n        shadow-[0px_1px_2px_#00000029] \r\n        w-[calc(var(--sidebar-width-icon))]! border-r\"\r\n      >\r\n        <SidebarContent>\r\n          <SidebarGroup className=\"w-full p-0\">\r\n            <SidebarGroupContent className=\"\">\r\n              <SidebarMenu>\r\n                {data.navMain.map((item) => (\r\n                  <SidebarMenuItem\r\n                    key={item.title}\r\n                    onClick={() => handleNavClick(item)}\r\n                  >\r\n                    <SidebarMenuButton\r\n                      tooltip={{\r\n                        children: item.title,\r\n                        hidden: false,\r\n                      }}\r\n                      isActive={activeItem?.title === item.title}\r\n                      className={`\r\n                         w-full flex items-center rounded-none py-5\r\n                        data-[active=true]:bg-[#00AB6A] data-[active=true]:text-white\r\n                        data-[active=true]:border-l-3\r\n                        hover:bg-[#00AB6A] hover:text-white\r\n                      `}\r\n                    >\r\n                      <Image\r\n                        src={item.icon}\r\n                        alt={item.title}\r\n                        width={120}\r\n                        height={120}\r\n                        className=\"w-30 h-30\"\r\n                      />\r\n                    </SidebarMenuButton>\r\n                  </SidebarMenuItem>\r\n                ))}\r\n              </SidebarMenu>\r\n            </SidebarGroupContent>\r\n          </SidebarGroup>\r\n        </SidebarContent>\r\n        <SidebarFooter>\r\n          <NavUser user={data.user} />\r\n        </SidebarFooter>\r\n      </Sidebar>\r\n\r\n      {/* This is the second sidebar */}\r\n      {/* We disable collapsible and let it fill remaining space */}\r\n    </Sidebar>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAkBA,sBAAsB;AACtB,MAAM,OAAO;IACX,MAAM;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;IACV;IACA,SAAS;QACP;YACE,OAAO;YACP,KAAK;YACL,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,KAAK;YACL,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,KAAK;YACL,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,KAAK;YACL,MAAM;YACN,UAAU;QACZ;QACA;YACE,OAAO;YACP,KAAK;YACL,MAAM;YACN,UAAU;QACZ;QACC;YACC,OAAO;YACP,KAAK;YACL,MAAM;YACN,UAAU;QACZ;QACA,IAAI;QACJ,oCAAoC;QACpC,mCAAmC;QACnC,4CAA4C;QAC5C,qBAAqB;QACrB,KAAK;QACL,IAAI;QACJ,mBAAmB;QACnB,sBAAsB;QACtB,0CAA0C;QAC1C,qBAAqB;QACrB,KAAK;QACL,IAAI;QACJ,yBAAyB;QACzB,yBAAyB;QACzB,8CAA8C;QAC9C,qBAAqB;QACrB,KAAK;QACL;YACE,OAAO;YACP,KAAK;YACL,MAAM;YACN,UAAU;QACZ;KACD;AACH;AAEO,SAAS,WAAW,EAAE,GAAG,OAA6C;IAC3E,6CAA6C;IAC7C,qCAAqC;IACrC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IAE7B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAC/C,IAAM,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC,OAAS,KAAK,GAAG,KAAK,aAAa,KAAK,OAAO,CAAC,EAAE;IAG7E,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,cAAc,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC,OAAS,KAAK,GAAG,KAAK;QAC7D,IAAI,aAAa;YACf,cAAc;QAChB;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,iBAAiB,CAAC;QACtB,QAAQ;QAER,oDAAoD;QACpD,IAAI,KAAK,KAAK,KAAK,qBAAqB;YACtC,yCAAyC;YACzC,MAAM,cAAc,KAAK,GAAG;YAC5B,OAAO,IAAI,CAAC,GAAG,YAAY,SAAS,EAAE,KAAK,GAAG,IAAI;QACpD,OAAO;YACL,OAAO,IAAI,CAAC,KAAK,GAAG;QACtB;IACF;IAEA,qBACE,8OAAC,mIAAA,CAAA,UAAO;QAAE,GAAG,KAAK;kBAIhB,cAAA,8OAAC,mIAAA,CAAA,UAAO;YACN,aAAY;YACZ,WAAU;;8BAKV,8OAAC,mIAAA,CAAA,iBAAc;8BACb,cAAA,8OAAC,mIAAA,CAAA,eAAY;wBAAC,WAAU;kCACtB,cAAA,8OAAC,mIAAA,CAAA,sBAAmB;4BAAC,WAAU;sCAC7B,cAAA,8OAAC,mIAAA,CAAA,cAAW;0CACT,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,qBACjB,8OAAC,mIAAA,CAAA,kBAAe;wCAEd,SAAS,IAAM,eAAe;kDAE9B,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;4CAChB,SAAS;gDACP,UAAU,KAAK,KAAK;gDACpB,QAAQ;4CACV;4CACA,UAAU,YAAY,UAAU,KAAK,KAAK;4CAC1C,WAAW,CAAC;;;;;sBAKZ,CAAC;sDAED,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,KAAK,IAAI;gDACd,KAAK,KAAK,KAAK;gDACf,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;uCArBT,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;8BA8B3B,8OAAC,mIAAA,CAAA,gBAAa;8BACZ,cAAA,8OAAC,iIAAA,CAAA,UAAO;wBAAC,MAAM,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;AAQlC", "debugId": null}}, {"offset": {"line": 1828, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/antpui/frontend/src/services/testApi.ts"], "sourcesContent": ["let moduleNames: string[] = [];\r\n\r\n// Setter\r\nexport const setModuleNamesGlobal = (names: string[]) => {\r\n  moduleNames = names;\r\n};\r\n\r\n// Getter\r\nexport const getModuleNamesGlobal = () => moduleNames;\r\n\r\n\r\n//Common functions\r\nexport const formatXML = (xml: string) => {\r\n  try {\r\n    const PADDING = \"  \"; // 2 spaces\r\n    const reg = /(>)(<)(\\/*)/g;\r\n    let formatted = \"\";\r\n    let pad = 0;\r\n\r\n    xml = xml.replace(reg, \"$1\\r\\n$2$3\"); // add newlines\r\n    xml.split(\"\\r\\n\").forEach((node) => {\r\n      let indent = 0;\r\n      if (node.match(/.+<\\/\\w[^>]*>$/)) {\r\n        indent = 0;\r\n      } else if (node.match(/^<\\/\\w/)) {\r\n        if (pad !== 0) {\r\n          pad -= 1;\r\n        }\r\n      } else if (node.match(/^<\\w([^>]*[^/])?>.*$/)) {\r\n        indent = 1;\r\n      } else {\r\n        indent = 0;\r\n      }\r\n\r\n      const padding = new Array(pad + 1).join(PADDING);\r\n      formatted += padding + node + \"\\r\\n\";\r\n      pad += indent;\r\n    });\r\n\r\n    return formatted.trim();\r\n  } catch (err) {\r\n    console.error(\"Error formatting XML:\", err);\r\n    return xml; // fallback\r\n  }\r\n};\r\n\r\nexport const normalizeBody = (rawBody: string) => {\r\n  if (!rawBody) return \"\";\r\n\r\n  let content = typeof rawBody === \"string\" ? rawBody : JSON.stringify(rawBody);\r\n\r\n  // unescape if needed\r\n  if (content.startsWith('\"') && content.endsWith('\"')) {\r\n    content = JSON.parse(content);\r\n  }\r\n\r\n  // try JSON pretty print\r\n  try {\r\n    return JSON.stringify(JSON.parse(content), null, 2);\r\n  } catch {\r\n    // fallback to XML formatting if it starts with '<'\r\n    if (content.trim().startsWith(\"<\")) {\r\n      return formatXML(content);\r\n    }\r\n    return content;\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAAA,IAAI,cAAwB,EAAE;AAGvB,MAAM,uBAAuB,CAAC;IACnC,cAAc;AAChB;AAGO,MAAM,uBAAuB,IAAM;AAInC,MAAM,YAAY,CAAC;IACxB,IAAI;QACF,MAAM,UAAU,MAAM,WAAW;QACjC,MAAM,MAAM;QACZ,IAAI,YAAY;QAChB,IAAI,MAAM;QAEV,MAAM,IAAI,OAAO,CAAC,KAAK,eAAe,eAAe;QACrD,IAAI,KAAK,CAAC,QAAQ,OAAO,CAAC,CAAC;YACzB,IAAI,SAAS;YACb,IAAI,KAAK,KAAK,CAAC,mBAAmB;gBAChC,SAAS;YACX,OAAO,IAAI,KAAK,KAAK,CAAC,WAAW;gBAC/B,IAAI,QAAQ,GAAG;oBACb,OAAO;gBACT;YACF,OAAO,IAAI,KAAK,KAAK,CAAC,yBAAyB;gBAC7C,SAAS;YACX,OAAO;gBACL,SAAS;YACX;YAEA,MAAM,UAAU,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC;YACxC,aAAa,UAAU,OAAO;YAC9B,OAAO;QACT;QAEA,OAAO,UAAU,IAAI;IACvB,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,KAAK,WAAW;IACzB;AACF;AAEO,MAAM,gBAAgB,CAAC;IAC5B,IAAI,CAAC,SAAS,OAAO;IAErB,IAAI,UAAU,OAAO,YAAY,WAAW,UAAU,KAAK,SAAS,CAAC;IAErE,qBAAqB;IACrB,IAAI,QAAQ,UAAU,CAAC,QAAQ,QAAQ,QAAQ,CAAC,MAAM;QACpD,UAAU,KAAK,KAAK,CAAC;IACvB;IAEA,wBAAwB;IACxB,IAAI;QACF,OAAO,KAAK,SAAS,CAAC,KAAK,KAAK,CAAC,UAAU,MAAM;IACnD,EAAE,OAAM;QACN,mDAAmD;QACnD,IAAI,QAAQ,IAAI,GAAG,UAAU,CAAC,MAAM;YAClC,OAAO,UAAU;QACnB;QACA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1893, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/antpui/frontend/src/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Table = React.forwardRef<\r\n  HTMLTableElement,\r\n  React.HTMLAttributes<HTMLTableElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div className=\"relative w-full overflow-auto\">\r\n    <table\r\n      ref={ref}\r\n      className={cn(\"w-full caption-bottom text-sm\", className)}\r\n      {...props}\r\n    />\r\n  </div>\r\n))\r\nTable.displayName = \"Table\"\r\n\r\nconst TableHeader = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\r\n))\r\nTableHeader.displayName = \"TableHeader\"\r\n\r\nconst TableBody = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tbody\r\n    ref={ref}\r\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nTableBody.displayName = \"TableBody\"\r\n\r\nconst TableFooter = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tfoot\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableFooter.displayName = \"TableFooter\"\r\n\r\nconst TableRow = React.forwardRef<\r\n  HTMLTableRowElement,\r\n  React.HTMLAttributes<HTMLTableRowElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tr\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableRow.displayName = \"TableRow\"\r\n\r\nconst TableHead = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.ThHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <th\r\n    ref={ref}\r\n    className={cn(\r\n      \"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableHead.displayName = \"TableHead\"\r\n\r\nconst TableCell = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.TdHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <td\r\n    ref={ref}\r\n    className={cn(\r\n      \"p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableCell.displayName = \"TableCell\"\r\n\r\nconst TableCaption = React.forwardRef<\r\n  HTMLTableCaptionElement,\r\n  React.HTMLAttributes<HTMLTableCaptionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <caption\r\n    ref={ref}\r\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nTableCaption.displayName = \"TableCaption\"\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0IACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wFACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2003, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/antpui/frontend/src/components/TestSuiteUiList.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport {\n  Table,\n  TableHeader,\n  TableRow,\n  TableHead,\n  TableBody,\n  TableCell,\n} from \"@/components/ui/table\";\nimport { Button } from \"@/components/ui/button\";\n\nimport { useToast } from \"@/hooks/use-toast\";\nimport { Plus } from \"lucide-react\";\n\ntype TestFlow = {\n  runMode: string;\n  moduleFiles: string;\n  environment: string;\n  UserDefinedVariables: string;\n};\n\nfunction TestSuiteUiList(selectedTestSuite: any) {\n  const [loading, setLoading] = useState(false);\n  const [testFlowData, setTestFlowData] = useState<TestFlow[]>([]);\n  const [envNames, setEnvNames] = useState([]);\n  const { toast } = useToast();\n  const [isSaving, setIsSaving] = useState(false);\n  const [editingRow, setEditingRow] = useState<number | null>(null);\n\n  const [selectedTestFlows, setSelectedTestFlows] = useState<boolean[]>(\n    Array(testFlowData.length).fill(false)\n  );\n\n  //toggle logic\n  const toggleElement = (index: number) => {\n    const updated = [...testFlowData];\n    if(updated[index].runMode == 'Yes')\n        updated[index].runMode = 'No'\n    else \n        updated[index].runMode = 'Yes'\n    \n    setTestFlowData(updated)\n    setIsSaving(true);\n  };\n\n  async function getEnvironmentNames() {\n    try {\n      const response = await fetch(\n        `${process.env.NEXT_PUBLIC_API_BASE_URL}/config/envnames`\n      );\n\n      if (!response.ok) {\n        throw new Error(\n          `Failed to fetch environment names: ${response.status}`\n        );\n      }\n\n      const envNames = await response.json();\n      console.log(envNames);\n      setEnvNames(envNames);\n    } catch (error: any) {\n      console.error(\"Error fetching enironment:\", error);\n      toast({\n        title: \"Error\",\n        description: error.message || \"Something went wrong\",\n        variant: \"destructive\",\n        duration: 2000,\n        style: {\n          backgroundColor: \"#dc3545\",\n          color: \"#f8f9fa\",\n          borderRadius: \"12px\",\n        },\n      });\n    }\n  }\n\n  async function fetchTestFlows() {\n    setLoading(true);\n    try {\n      const response = await fetch(\n        `${process.env.NEXT_PUBLIC_API_BASE_URL}/testsuiteapi/${selectedTestSuite.selectedTestSuite}`\n      );\n\n      if (!response.ok) {\n        throw new Error(`Failed to fetch configs: ${response.status}`);\n      }\n\n      const res = await response.json();\n      const data = res.data;\n      console.log(\"data : \")\n      console.log(data);\n      setTestFlowData(data);\n    } catch (error: any) {\n      console.error(\"Error fetching configs:\", error);\n      toast({\n        title: \"Error\",\n        description: error.message || \"Something went wrong\",\n        variant: \"destructive\",\n        duration: 2000,\n        style: {\n          backgroundColor: \"#dc3545\",\n          color: \"#f8f9fa\",\n          borderRadius: \"12px\",\n        },\n      });\n      // setUsersFetchError(error.message || \"Failed to load users\");\n    } finally {\n      // setIsLoadingUsers(false);\n      setLoading(false);\n    }\n  }\n\n  const handleCellChange = (\n    e: React.ChangeEvent<HTMLInputElement> | React.ChangeEvent<HTMLTextAreaElement> | React.ChangeEvent<HTMLSelectElement>,\n    rowIndex: number,\n    field: keyof TestFlow\n  ) => {\n    const updatedtestFlowData = [...testFlowData];\n    updatedtestFlowData[rowIndex] = {\n      ...updatedtestFlowData[rowIndex],\n      [field]: e.target.value,\n    };\n    console.log(updatedtestFlowData);\n    setIsSaving(true);\n    setTestFlowData(updatedtestFlowData);\n    console.log(\"testFlowData\");\n    console.log(testFlowData);\n  };\n\n  function handleAddRow(index : number){\n\n  }\n  function handleDeleteSelected(indexNo : number){\n      const elementsToKeep = testFlowData.filter((_,index) => index !== indexNo);\n      console.log(elementsToKeep);\n\n      // Update local state - renumber the remaining elements\n      const renumbered = elementsToKeep.map((element) => ({\n        ...element,\n      }));\n\n      setTestFlowData(renumbered);\n  }\n\n  useEffect(() => {\n    fetchTestFlows();\n    getEnvironmentNames();\n  }, [selectedTestSuite]);\n\n  return (\n    <div>\n      <header className=\"bg-background flex items-center justify-between border-b p-2\">\n        <div className=\"flex items-center space-x-3\">\n          <h1 className=\"text-left font-medium text-[24px] leading-[20px] tracking-[0px] text-[#2C2E2E]\">\n            {selectedTestSuite\n              ? `Test Suites - ${selectedTestSuite.selectedTestSuite}`\n              : \"Test Suites\"}\n          </h1>\n        </div>\n        <Button\n          className=\"bg-[#15537C] text-white hover:brightness-110 rounded-md w-34 h-6\"\n          onClick={() => {\n            // setEditMode(false);\n            // setEditingTestFlow(null);\n            // setDialogOpen(true);\n          }}\n        >\n          Create Test Suite\n        </Button>\n      </header>\n      <div className=\"flex items-center gap-[20px] p-3\">\n        <div className=\"w-[85%]\">\n          <input\n            type=\"text\"\n            placeholder=\"Search by Test flow name...\"\n            value=\"\"\n            className=\"border px-3 py-2 rounded w-full\"\n          />\n        </div>\n        <div className=\"flex items-center gap-[20px]\">\n          {/* save elements */}\n          <a\n            onClick={(e) => {\n              e.stopPropagation();\n              // handleSaveClick();\n            }}\n            className={`${\n              isSaving\n                ? \"pointer-events-auto cursor-pointer\"\n                : \"pointer-events-none\"\n            }`}\n            title=\"Save new configs\"\n          >\n            <img\n              src=\"/save1.svg\"\n              className=\"w-7 h-8 cursor-pointer\"\n              style={{ opacity: isSaving ? 1 : 0.5 }}\n            />\n          </a>\n        </div>\n      </div>\n      {testFlowData?.length <= 0 ? (\n        <div className=\"p-8 text-center text-gray-500\">\n          <div className=\"text-gray-400 mb-4\">\n            <svg\n              className=\"w-16 h-16 mx-auto mb-4\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={1.5}\n                d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\n              />\n            </svg>\n\n            <p className=\"text-lg font-medium text-gray-600\">\n              No configurations found\n            </p>\n            <p className=\"text-sm text-gray-500\">\n              Start by identifying and creating configurations for your project\n            </p>\n          </div>\n        </div>\n      ) : (\n        <div className=\"p-3\">\n          <Table className=\"table-fixed\">\n            <TableHeader className=\"bg-[#15537c]\">\n              <TableRow>\n                <TableHead className=\"w-[5%]\"></TableHead>\n                <TableHead className=\"text-[#ffffff] w-[15%]\">\n                  Flow Name\n                </TableHead>\n                <TableHead className=\"text-[#ffffff] w-[20%]\">\n                  Environment Name\n                </TableHead>\n                <TableHead className=\"text-[#ffffff] w-[54%]\">\n                  User defined variables\n                </TableHead>\n                <TableHead></TableHead>\n                <TableHead></TableHead>\n\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {testFlowData?.map((testFlow, index: number) => {\n                return (\n                  <TableRow\n                    key={index}\n                    className={\n                      index % 2 === 0 ? \"bg-[#F7F9FC] \" : \"bg-[#FFFFFF4D]\"\n                    }\n                  >\n                    <TableCell className=\"w-[5%]\">\n                      <input\n                        type=\"checkbox\"\n                        className=\"accent-blue-500\"\n                        checked={\n                          testFlow.runMode == \"Yes\"\n                            ? true\n                            : false || selectedTestFlows[index]\n                        }\n                        onChange={() => toggleElement(index)}\n                      />\n                    </TableCell>\n                    <TableCell>\n                      {testFlow.moduleFiles\n                        .replace(/^TF_/, \"\")\n                        .replace(/\\.xlsx$/, \"\")}\n                    </TableCell>\n                    <TableCell key={index} className=\"h-13\">\n                      <select\n                        className=\"w-50 p-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                        onChange={(e) =>\n                          handleCellChange(e, index, \"environment\")\n                        }\n                      >\n                        <option> {testFlow.environment}\n                        </option>\n                        {envNames.map((envName, envIndex) => (\n                          <option key={envIndex} value={envName}>\n                            {envName}\n                          </option>\n                        ))}\n                      </select>\n                    </TableCell>\n                    <TableCell\n                      className=\"cursor-pointer\"\n                      onClick={() => setEditingRow(index)}\n                    >\n                      {editingRow === index ? (\n                        <textarea\n                          value={testFlow.UserDefinedVariables || \"\"}\n                          onChange={(e) =>\n                            handleCellChange(e, index, \"UserDefinedVariables\")\n                          }\n                          onBlur={() => setEditingRow(null)} // Exit edit mode on blur\n                          autoFocus\n                          className=\"w-full p-2 h-20 border rounded\"\n                        />\n                      ) : (\n                        <textarea\n                          name=\"\"\n                          id=\"\"\n                          value={testFlow.UserDefinedVariables || \"\"}\n                          className=\"w-full p-2 h-20 border rounded\"\n                          placeholder=\"Enter User defined variables\"\n                        ></textarea>\n                      )}\n                    </TableCell>\n                    <TableCell>\n                      <a\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          handleAddRow(index);\n                        }}\n                        title=\"Add new config\"\n                      >\n                        <Plus className=\"w-7 h-8 cursor-pointer text-[#1d5881]\" />\n                      </a>\n                    </TableCell>\n                    <TableCell>\n                      <a\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          handleDeleteSelected(index);\n                        }}\n                        title=\"Delete config\"\n                      >\n                        <img\n                          src=\"/Group 21846.svg\"\n                          className=\"w-6 h-6 cursor-pointer hover:text-[#7b7c7a] cursor-pointer\"\n                        />\n                      </a>\n                    </TableCell>\n                  </TableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default TestSuiteUiList;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAQA;AAEA;AACA;;;;;;;AASA,SAAS,gBAAgB,iBAAsB;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC/D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACvD,MAAM,aAAa,MAAM,EAAE,IAAI,CAAC;IAGlC,cAAc;IACd,MAAM,gBAAgB,CAAC;QACrB,MAAM,UAAU;eAAI;SAAa;QACjC,IAAG,OAAO,CAAC,MAAM,CAAC,OAAO,IAAI,OACzB,OAAO,CAAC,MAAM,CAAC,OAAO,GAAG;aAEzB,OAAO,CAAC,MAAM,CAAC,OAAO,GAAG;QAE7B,gBAAgB;QAChB,YAAY;IACd;IAEA,eAAe;QACb,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,iEAAwC,gBAAgB,CAAC;YAG3D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MACR,CAAC,mCAAmC,EAAE,SAAS,MAAM,EAAE;YAE3D;YAEA,MAAM,WAAW,MAAM,SAAS,IAAI;YACpC,QAAQ,GAAG,CAAC;YACZ,YAAY;QACd,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;gBACJ,OAAO;gBACP,aAAa,MAAM,OAAO,IAAI;gBAC9B,SAAS;gBACT,UAAU;gBACV,OAAO;oBACL,iBAAiB;oBACjB,OAAO;oBACP,cAAc;gBAChB;YACF;QACF;IACF;IAEA,eAAe;QACb,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,iEAAwC,cAAc,EAAE,kBAAkB,iBAAiB,EAAE;YAG/F,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,MAAM,EAAE;YAC/D;YAEA,MAAM,MAAM,MAAM,SAAS,IAAI;YAC/B,MAAM,OAAO,IAAI,IAAI;YACrB,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC;YACZ,gBAAgB;QAClB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;gBACJ,OAAO;gBACP,aAAa,MAAM,OAAO,IAAI;gBAC9B,SAAS;gBACT,UAAU;gBACV,OAAO;oBACL,iBAAiB;oBACjB,OAAO;oBACP,cAAc;gBAChB;YACF;QACA,+DAA+D;QACjE,SAAU;YACR,4BAA4B;YAC5B,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,CACvB,GACA,UACA;QAEA,MAAM,sBAAsB;eAAI;SAAa;QAC7C,mBAAmB,CAAC,SAAS,GAAG;YAC9B,GAAG,mBAAmB,CAAC,SAAS;YAChC,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,KAAK;QACzB;QACA,QAAQ,GAAG,CAAC;QACZ,YAAY;QACZ,gBAAgB;QAChB,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;IACd;IAEA,SAAS,aAAa,KAAc,GAEpC;IACA,SAAS,qBAAqB,OAAgB;QAC1C,MAAM,iBAAiB,aAAa,MAAM,CAAC,CAAC,GAAE,QAAU,UAAU;QAClE,QAAQ,GAAG,CAAC;QAEZ,uDAAuD;QACvD,MAAM,aAAa,eAAe,GAAG,CAAC,CAAC,UAAY,CAAC;gBAClD,GAAG,OAAO;YACZ,CAAC;QAED,gBAAgB;IACpB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG;QAAC;KAAkB;IAEtB,qBACE,8OAAC;;0BACC,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCACX,oBACG,CAAC,cAAc,EAAE,kBAAkB,iBAAiB,EAAE,GACtD;;;;;;;;;;;kCAGR,8OAAC,kIAAA,CAAA,SAAM;wBACL,WAAU;wBACV,SAAS;wBACP,sBAAsB;wBACtB,4BAA4B;wBAC5B,uBAAuB;wBACzB;kCACD;;;;;;;;;;;;0BAIH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,aAAY;4BACZ,OAAM;4BACN,WAAU;;;;;;;;;;;kCAGd,8OAAC;wBAAI,WAAU;kCAEb,cAAA,8OAAC;4BACC,SAAS,CAAC;gCACR,EAAE,eAAe;4BACjB,qBAAqB;4BACvB;4BACA,WAAW,GACT,WACI,uCACA,uBACJ;4BACF,OAAM;sCAEN,cAAA,8OAAC;gCACC,KAAI;gCACJ,WAAU;gCACV,OAAO;oCAAE,SAAS,WAAW,IAAI;gCAAI;;;;;;;;;;;;;;;;;;;;;;YAK5C,cAAc,UAAU,kBACvB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,WAAU;4BACV,MAAK;4BACL,QAAO;4BACP,SAAQ;sCAER,cAAA,8OAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,aAAa;gCACb,GAAE;;;;;;;;;;;sCAIN,8OAAC;4BAAE,WAAU;sCAAoC;;;;;;sCAGjD,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;qCAMzC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;;sCACf,8OAAC,iIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;kDACP,8OAAC,iIAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC,iIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAyB;;;;;;kDAG9C,8OAAC,iIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAyB;;;;;;kDAG9C,8OAAC,iIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAyB;;;;;;kDAG9C,8OAAC,iIAAA,CAAA,YAAS;;;;;kDACV,8OAAC,iIAAA,CAAA,YAAS;;;;;;;;;;;;;;;;sCAId,8OAAC,iIAAA,CAAA,YAAS;sCACP,cAAc,IAAI,CAAC,UAAU;gCAC5B,qBACE,8OAAC,iIAAA,CAAA,WAAQ;oCAEP,WACE,QAAQ,MAAM,IAAI,kBAAkB;;sDAGtC,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDACnB,cAAA,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SACE,SAAS,OAAO,IAAI,QAChB,OACA,SAAS,iBAAiB,CAAC,MAAM;gDAEvC,UAAU,IAAM,cAAc;;;;;;;;;;;sDAGlC,8OAAC,iIAAA,CAAA,YAAS;sDACP,SAAS,WAAW,CAClB,OAAO,CAAC,QAAQ,IAChB,OAAO,CAAC,WAAW;;;;;;sDAExB,8OAAC,iIAAA,CAAA,YAAS;4CAAa,WAAU;sDAC/B,cAAA,8OAAC;gDACC,WAAU;gDACV,UAAU,CAAC,IACT,iBAAiB,GAAG,OAAO;;kEAG7B,8OAAC;;4DAAO;4DAAE,SAAS,WAAW;;;;;;;oDAE7B,SAAS,GAAG,CAAC,CAAC,SAAS,yBACtB,8OAAC;4DAAsB,OAAO;sEAC3B;2DADU;;;;;;;;;;;2CAVH;;;;;sDAgBhB,8OAAC,iIAAA,CAAA,YAAS;4CACR,WAAU;4CACV,SAAS,IAAM,cAAc;sDAE5B,eAAe,sBACd,8OAAC;gDACC,OAAO,SAAS,oBAAoB,IAAI;gDACxC,UAAU,CAAC,IACT,iBAAiB,GAAG,OAAO;gDAE7B,QAAQ,IAAM,cAAc;gDAC5B,SAAS;gDACT,WAAU;;;;;qEAGZ,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,OAAO,SAAS,oBAAoB,IAAI;gDACxC,WAAU;gDACV,aAAY;;;;;;;;;;;sDAIlB,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC;gDACC,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,aAAa;gDACf;gDACA,OAAM;0DAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAGpB,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC;gDACC,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,qBAAqB;gDACvB;gDACA,OAAM;0DAEN,cAAA,8OAAC;oDACC,KAAI;oDACJ,WAAU;;;;;;;;;;;;;;;;;mCAnFX;;;;;4BAyFX;;;;;;;;;;;;;;;;;;;;;;;AAOd;uCAEe", "debugId": null}}, {"offset": {"line": 2507, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/antpui/frontend/src/app/testsuitesapi/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { AppSidebar } from \"@/components/dashboard-sidebar\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { SidebarInset, SidebarProvider } from \"@/components/ui/sidebar\";\r\nimport { useState, useEffect, useMemo } from \"react\";\r\nimport TestFlowUICreatorDialog from \"@/components/TestFlowUI\";\r\nimport { setModuleNamesGlobal } from \"@/services/testApi\";\r\nimport TestSuiteUiList from \"@/components/TestSuiteUiList\";\r\n\r\ninterface TestFlow {\r\n  name: string;\r\n  description: string;\r\n  testcases: string[];\r\n}\r\n\r\n\r\n\r\n// Fetch test flows\r\n// async function getTestFlowsFromAPI(): Promise<TestFlow[]> {\r\n//   try {\r\n//     const response = await fetch(\"/api/test-flows\");\r\n//     if (!response.ok) throw new Error(\"Failed to fetch test flows\");\r\n//     return response.json();\r\n//   } catch (error) {\r\n//     console.error(error);\r\n//     return [];\r\n//   }\r\n// }\r\n\r\n// Fetch test cases (all at once)\r\n// async function getTestCasesFromAPI(): Promise<TestCase[]> {\r\n//   try {\r\n//     const response = await fetch(\"/api/test-cases\");\r\n//     if (!response.ok) throw new Error(\"Failed to fetch test cases\");\r\n//     return response.json();\r\n//   } catch (error) {\r\n//     console.error(error);\r\n//     return [];\r\n//   }\r\n// }\r\n\r\n// function TestFlowContent({\r\n//   testFlowName,\r\n//   testFlows,\r\n//   allTestCases,\r\n// }: {\r\n//   testFlowName: string | null;\r\n//   testFlows: TestFlow[];\r\n//   allTestCases: TestCase[];\r\n// }) {\r\n//   // Filter test cases only once using memo\r\n//   const filteredTestCases = useMemo(() => {\r\n//     if (!testFlowName) return [];\r\n//     const testFlow = testFlows.find((flow) => flow.name === testFlowName);\r\n//     if (!testFlow) return [];\r\n//     return allTestCases.filter((tc) => testFlow.testcases.includes(tc.name));\r\n//   }, [testFlowName, testFlows, allTestCases]);\r\n\r\n//   if (!testFlowName) {\r\n//     return (\r\n//       <div className=\"p-8 text-center text-gray-500\">\r\n//         <div className=\"text-gray-400 mb-4\">\r\n//           <svg\r\n//             className=\"w-16 h-16 mx-auto mb-4\"\r\n//             fill=\"none\"\r\n//             stroke=\"currentColor\"\r\n//             viewBox=\"0 0 24 24\"\r\n//           >\r\n//             <path\r\n//               strokeLinecap=\"round\"\r\n//               strokeLinejoin=\"round\"\r\n//               strokeWidth={1.5}\r\n//               d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\r\n//             />\r\n//           </svg>\r\n//           <p className=\"text-lg font-medium text-gray-600\">\r\n//             Select a test flow to view test cases\r\n//           </p>\r\n//           <p className=\"text-sm text-gray-500\">\r\n//             Choose a test flow from the sidebar to see its associated test cases\r\n//           </p>\r\n//         </div>\r\n//       </div>\r\n//     );\r\n//   }\r\n\r\n//   if (filteredTestCases.length === 0) {\r\n//     return (\r\n//       <div className=\"p-8 text-center\">\r\n//         <div className=\"text-gray-500 mb-4\">\r\n//           <svg\r\n//             className=\"w-12 h-12 mx-auto mb-2\"\r\n//             fill=\"none\"\r\n//             stroke=\"currentColor\"\r\n//             viewBox=\"0 0 24 24\"\r\n//           >\r\n//             <path\r\n//               strokeLinecap=\"round\"\r\n//               strokeLinejoin=\"round\"\r\n//               strokeWidth={2}\r\n//               d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\r\n//             />\r\n//           </svg>\r\n//           <p className=\"text-lg font-medium\">No test cases found</p>\r\n//           <p className=\"text-sm\">\r\n//             This test flow doesn't have any test cases yet.\r\n//           </p>\r\n//         </div>\r\n//       </div>\r\n//     );\r\n//   }\r\n\r\n//   return (\r\n//     <div className=\"relative\">\r\n//       <AccordionTable\r\n//         testCases={filteredTestCases}\r\n//         showActionButtons={true}\r\n//         showCheckboxes={true}\r\n//         allowEditing={true}\r\n//         showCucumberButton={false}\r\n//         showPlayButton={false}\r\n//       />\r\n//     </div>\r\n//   );\r\n// }\r\n\r\nexport default function Page() {\r\n  const [dialogOpen, setDialogOpen] = useState(false);\r\n  const [editMode, setEditMode] = useState(false);\r\n  const [editingTestFlow, setEditingTestFlow] = useState<TestFlow | null>(null);\r\n  const [testFlows, setTestFlows] = useState<TestFlow[]>([]);\r\n  const [selectedTestSuite, setSelectedTestSuite] = useState<string | null>(\r\n    null\r\n  );\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [testSuiteNames, setTestSuiteNames] = useState<string[]>([]);\r\n  const [moduleNames, setModuleNames] = useState([]);\r\n\r\n  const [environment,setEnvironment] = useState(['qa','stage','production']);\r\n  const [userDefinedVariables,setUserDefinedvariables] = useState(['test test','test2, test2']);\r\n\r\n  \r\n  const [allowEditing, setAllowEditing] = useState(true);\r\n  \r\n  const [isSaving, setIsSaving] = useState(false);\r\n\r\n    const [editingCell, setEditingCell] = useState<{\r\n    row: number | null;\r\n    field: string | null;\r\n  }>({ row: null, field: null })\r\n\r\n  // useEffect(() => {\r\n  //   // Fetch both concurrently\r\n  //   async function fetchData() {\r\n  //     setLoading(true);\r\n  //     setError(null);\r\n  //     try {\r\n  //       const [flows, testCases] = await Promise.all([\r\n  //         getTestFlowsFromAPI(),\r\n  //         getTestCasesFromAPI(),\r\n  //       ]);\r\n  //       setTestFlows(flows);\r\n  //       setAllTestCases(testCases);\r\n\r\n  //       // Set default test flow\r\n  //       if (!selectedTestFlow) {\r\n  //         const defaultFlow = flows.find((flow) => flow.name === \"testflow1\");\r\n  //         setSelectedTestFlow(\r\n  //           defaultFlow ? \"testflow1\" : flows.length > 0 ? flows[0].name : null\r\n  //         );\r\n  //       }\r\n  //     } catch (err) {\r\n  //       console.error(err);\r\n  //       setError(\"Failed to load test flows or test cases\");\r\n  //     } finally {\r\n  //       setLoading(false);\r\n  //     }\r\n  //   }\r\n  //   fetchData();\r\n  // }, []);\r\n\r\n  \r\n\r\n  \r\n\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      try {\r\n        const res = await getAllTestSuiteNames();\r\n        const files: string[] = res.files;\r\n        console.log(files);\r\n        console.log(\"setLoading : \"+loading)\r\n        setTestSuiteNames(files);\r\n        console.log(testSuiteNames);\r\n        setSelectedTestSuite(files[0]);\r\n        getAndSetAllModuleNames();\r\n      } catch (err) {\r\n        console.error(err);\r\n        setError(\"Failed to load test flows or test cases\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchData();\r\n  }, []);\r\n\r\n  async function getAndSetAllModuleNames() {\r\n    console.log(selectedTestSuite);\r\n    if(selectedTestSuite){\r\n      const res = await getAllModuleNames(selectedTestSuite);\r\n      const data = res.data;\r\n      const files = data.map((dataItem : any)=> dataItem.moduleFiles);\r\n      console.log(files);\r\n      setModuleNames(files);\r\n      setModuleNamesGlobal(files);\r\n    }\r\n  }\r\n\r\n  const handleDialogChange = async (open: boolean) => {\r\n    setDialogOpen(open);\r\n    getAndSetAllModuleNames();\r\n\r\n    if (!open) {\r\n      // Reset edit mode when closing\r\n      setEditMode(false);\r\n      setEditingTestFlow(null);\r\n\r\n      // Refresh test flows and test cases after dialog closes\r\n      setLoading(true);\r\n      setError(null);\r\n      // try {\r\n      //   const [flows, testCases] = await Promise.all([\r\n      //     getTestFlowsFromAPI(),\r\n      //     getTestCasesFromAPI(),\r\n      //   ]);\r\n      //   setTestFlows(flows);\r\n      //   setAllTestCases(testCases);\r\n      // } catch (err) {\r\n      //   console.error(err);\r\n      //   setError(\"Failed to refresh test flows or test cases\");\r\n      // } finally {\r\n      //   setLoading(false);\r\n      // }\r\n      try {\r\n        const res = await getAllTestSuiteNames();\r\n        const files = res.files;\r\n        setTestSuiteNames(files);\r\n        setSelectedTestSuite(files[0]);\r\n      } catch (err) {\r\n        console.error(err);\r\n        setError(\"Failed to load test flows or test cases\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Handle edit button click\r\n  const handleEditTestFlow = () => {\r\n    if (selectedTestSuite) {\r\n      const flowToEdit = testFlows.find(\r\n        (flow) => flow.name === selectedTestSuite\r\n      );\r\n      if (flowToEdit) {\r\n        setEditingTestFlow(flowToEdit);\r\n        setEditMode(true);\r\n        setDialogOpen(true);\r\n      }\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <SidebarProvider\r\n        style={{ \"--sidebar-width\": \"45px\" } as React.CSSProperties}\r\n      >\r\n        <AppSidebar />\r\n        <SidebarInset>\r\n          <div className=\"flex h-screen items-center justify-center\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-[#15537C]\"></div>\r\n              <span>Loading test suites and test flows...</span>\r\n            </div>\r\n          </div>\r\n        </SidebarInset>\r\n      </SidebarProvider>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <SidebarProvider\r\n        style={{ \"--sidebar-width\": \"45px\" } as React.CSSProperties}\r\n      >\r\n        <AppSidebar />\r\n        <SidebarInset>\r\n          <div className=\"flex h-screen items-center justify-center\">\r\n            <div className=\"p-4 bg-red-50 border border-red-200 rounded-md\">\r\n              <p className=\"text-red-600 font-medium\">Error loading data</p>\r\n              <p className=\"text-red-500 text-sm\">{error}</p>\r\n              <Button\r\n                onClick={() => window.location.reload()}\r\n                className=\"mt-2 text-sm\"\r\n                variant=\"outline\"\r\n              >\r\n                Retry\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </SidebarInset>\r\n      </SidebarProvider>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <SidebarProvider\r\n      style={{ \"--sidebar-width\": \"45px\" } as React.CSSProperties}\r\n    >\r\n      <AppSidebar />\r\n      <SidebarInset>\r\n        <div className=\"flex h-screen overflow-hidden\">\r\n          <aside className=\"w-[15%] border-r bg-muted p-2 space-y-1 bg-[#F7F9FC] font-normal tracking-normal font-roboto\">\r\n            <div className=\"bg-[#15537C] text-white px-2 py-1 flex items-center space-x-1 hover:brightness-110 rounded-xs justify-start\">\r\n              <span className=\"font-bold text-sm ml-1 truncate whitespace-nowrap overflow-hidden max-w-[160px]\">\r\n                Test Suits\r\n              </span>\r\n            </div>\r\n            <ul className=\"text-[#37393A] py-2 flex flex-col gap-2 rounded-xs\">\r\n              {testSuiteNames.map((suiteName) => (\r\n                <li\r\n                  key={suiteName}\r\n                  className=\"text-left w-full text-sm hover:bg-[#B1DBEA]\"\r\n                >\r\n                  <button\r\n                    className={`w-full px-2 py-1 text-left ${\r\n                      selectedTestSuite === suiteName\r\n                        ? \"bg-[#B1DBEA] text-[#15537C] font-medium\"\r\n                        : \"\"\r\n                    }`}\r\n                    onClick={() => setSelectedTestSuite(suiteName)}\r\n                    // title={flow.description}\r\n                  >\r\n                    <div className=\"truncate\">{suiteName}</div>\r\n                  </button>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </aside>\r\n\r\n          <div className=\"flex-1 flex flex-col overflow-hidden\">      \r\n              <TestSuiteUiList selectedTestSuite={selectedTestSuite}/>\r\n          </div>\r\n        </div>\r\n      </SidebarInset>\r\n      {/* <TestFlowUICreatorDialog\r\n        open={dialogOpen}\r\n        onOpenChange={handleDialogChange}\r\n        editMode={editMode}\r\n        editingTestFlow={editingTestFlow}\r\n        moduleNames={moduleNames}\r\n      /> */}\r\n    </SidebarProvider>\r\n  );\r\n}\r\n\r\nasync function getAllTestFlowNames() {\r\n  const response = await fetch(\r\n    `${process.env.NEXT_PUBLIC_API_BASE_URL}/testflowui`\r\n  );\r\n  return await response.json();\r\n}\r\n\r\nasync function getAllTestSuiteNames() {\r\n  const response = await fetch(\r\n    `${process.env.NEXT_PUBLIC_API_BASE_URL}/testsuiteapi`\r\n  );\r\n  return await response.json();\r\n}\r\n\r\nasync function getAllModuleNames(selectedTestSuite : string) {\r\n  const response = await fetch(\r\n    `${process.env.NEXT_PUBLIC_API_BASE_URL}/testsuiteapi/${selectedTestSuite}`\r\n  );\r\n  return await response.json();\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AARA;;;;;;;;AA+He,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACvD;IAEF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAEjD,MAAM,CAAC,aAAY,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;QAAK;QAAQ;KAAa;IACzE,MAAM,CAAC,sBAAqB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;QAAY;KAAe;IAG5F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAG5C;QAAE,KAAK;QAAM,OAAO;IAAK;IAE5B,oBAAoB;IACpB,+BAA+B;IAC/B,iCAAiC;IACjC,wBAAwB;IACxB,sBAAsB;IACtB,YAAY;IACZ,uDAAuD;IACvD,iCAAiC;IACjC,iCAAiC;IACjC,YAAY;IACZ,6BAA6B;IAC7B,oCAAoC;IAEpC,iCAAiC;IACjC,iCAAiC;IACjC,+EAA+E;IAC/E,+BAA+B;IAC/B,gFAAgF;IAChF,aAAa;IACb,UAAU;IACV,sBAAsB;IACtB,4BAA4B;IAC5B,6DAA6D;IAC7D,kBAAkB;IAClB,2BAA2B;IAC3B,QAAQ;IACR,MAAM;IACN,iBAAiB;IACjB,UAAU;IAMV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI;gBACF,MAAM,MAAM,MAAM;gBAClB,MAAM,QAAkB,IAAI,KAAK;gBACjC,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,kBAAgB;gBAC5B,kBAAkB;gBAClB,QAAQ,GAAG,CAAC;gBACZ,qBAAqB,KAAK,CAAC,EAAE;gBAC7B;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC;gBACd,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,eAAe;QACb,QAAQ,GAAG,CAAC;QACZ,IAAG,mBAAkB;YACnB,MAAM,MAAM,MAAM,kBAAkB;YACpC,MAAM,OAAO,IAAI,IAAI;YACrB,MAAM,QAAQ,KAAK,GAAG,CAAC,CAAC,WAAkB,SAAS,WAAW;YAC9D,QAAQ,GAAG,CAAC;YACZ,eAAe;YACf,CAAA,GAAA,0HAAA,CAAA,uBAAoB,AAAD,EAAE;QACvB;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,cAAc;QACd;QAEA,IAAI,CAAC,MAAM;YACT,+BAA+B;YAC/B,YAAY;YACZ,mBAAmB;YAEnB,wDAAwD;YACxD,WAAW;YACX,SAAS;YACT,QAAQ;YACR,mDAAmD;YACnD,6BAA6B;YAC7B,6BAA6B;YAC7B,QAAQ;YACR,yBAAyB;YACzB,gCAAgC;YAChC,kBAAkB;YAClB,wBAAwB;YACxB,4DAA4D;YAC5D,cAAc;YACd,uBAAuB;YACvB,IAAI;YACJ,IAAI;gBACF,MAAM,MAAM,MAAM;gBAClB,MAAM,QAAQ,IAAI,KAAK;gBACvB,kBAAkB;gBAClB,qBAAqB,KAAK,CAAC,EAAE;YAC/B,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC;gBACd,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM,qBAAqB;QACzB,IAAI,mBAAmB;YACrB,MAAM,aAAa,UAAU,IAAI,CAC/B,CAAC,OAAS,KAAK,IAAI,KAAK;YAE1B,IAAI,YAAY;gBACd,mBAAmB;gBACnB,YAAY;gBACZ,cAAc;YAChB;QACF;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC,mIAAA,CAAA,kBAAe;YACd,OAAO;gBAAE,mBAAmB;YAAO;;8BAEnC,8OAAC,0IAAA,CAAA,aAAU;;;;;8BACX,8OAAC,mIAAA,CAAA,eAAY;8BACX,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMlB;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC,mIAAA,CAAA,kBAAe;YACd,OAAO;gBAAE,mBAAmB;YAAO;;8BAEnC,8OAAC,0IAAA,CAAA,aAAU;;;;;8BACX,8OAAC,mIAAA,CAAA,eAAY;8BACX,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAA2B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CACrC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;oCACrC,WAAU;oCACV,SAAQ;8CACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQb;IAEA,qBACE,8OAAC,mIAAA,CAAA,kBAAe;QACd,OAAO;YAAE,mBAAmB;QAAO;;0BAEnC,8OAAC,0IAAA,CAAA,aAAU;;;;;0BACX,8OAAC,mIAAA,CAAA,eAAY;0BACX,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAkF;;;;;;;;;;;8CAIpG,8OAAC;oCAAG,WAAU;8CACX,eAAe,GAAG,CAAC,CAAC,0BACnB,8OAAC;4CAEC,WAAU;sDAEV,cAAA,8OAAC;gDACC,WAAW,CAAC,2BAA2B,EACrC,sBAAsB,YAClB,4CACA,IACJ;gDACF,SAAS,IAAM,qBAAqB;0DAGpC,cAAA,8OAAC;oDAAI,WAAU;8DAAY;;;;;;;;;;;2CAZxB;;;;;;;;;;;;;;;;sCAmBb,8OAAC;4BAAI,WAAU;sCACX,cAAA,8OAAC,qIAAA,CAAA,UAAe;gCAAC,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAalD;AAEA,eAAe;IACb,MAAM,WAAW,MAAM,MACrB,iEAAwC,WAAW,CAAC;IAEtD,OAAO,MAAM,SAAS,IAAI;AAC5B;AAEA,eAAe;IACb,MAAM,WAAW,MAAM,MACrB,iEAAwC,aAAa,CAAC;IAExD,OAAO,MAAM,SAAS,IAAI;AAC5B;AAEA,eAAe,kBAAkB,iBAA0B;IACzD,MAAM,WAAW,MAAM,MACrB,iEAAwC,cAAc,EAAE,mBAAmB;IAE7E,OAAO,MAAM,SAAS,IAAI;AAC5B", "debugId": null}}]}
import { NextResponse } from "next/server";
import { readFileSync } from "fs";
import path from "path";
import fs from "fs-extra";

export async function GET() {
  try {

       const configPath = path.join(process.cwd(), "src", "config", "config.json");
        
        if (!(await fs.pathExists(configPath))) {
          return NextResponse.json(
            { error: "Config file not found" },
            { status: 404 }
          );
        }

       const configContent = await fs.readFile(configPath, "utf-8");
      const config = JSON.parse(configContent); 
      
    const filePath = path.resolve(config.projectPath+"/extent-report/ExtentHtml.html");

    const htmlContent = readFileSync(filePath, "utf-8");

    return new NextResponse(htmlContent, {
      headers: { "Content-Type": "text/html" },
    });
  } catch (error: any) {
    return new NextResponse(`Error reading HTML file: ${error.message}`, {
      status: 500,
    });
  }
}

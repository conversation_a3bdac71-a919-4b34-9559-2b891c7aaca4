export interface StepDefinition {
  pattern: string;
  type: "Given" | "When" | "Then" | "And" | "But";
  category?: string;
  description?: string;
}

export const stepDefinitions: StepDefinition[] = [
  {
    pattern: 'Given I load the "{string}" module "{string}" locators',
    type: "Given",
    category: "Setup",
  },
  {
    pattern: 'Given I send "{string}" as a "{string}" in to "{string}" page',
    type: "Given",
    category: "Actions",
  },
  { pattern: "Given I login to application", type: "Given", category: "Setup" },
  {
    pattern: 'And I click on "{string}" on "{string}" page',
    type: "And",
    category: "Actions",
  },
  {
    pattern: 'Then I verify "{string}" has data "{string}" on "{string}" page',
    type: "Then",
    category: "Verification",
  },
  {
    pattern:
      'Then I verify "{string}" (not )?exists "{string}" on "{string}" page',
    type: "Then",
    category: "Verification",
  },
  {
    pattern: 'And I select "{string}" with data "{string}" on "{string}" page',
    type: "And",
    category: "Actions",
  },
  {
    pattern: 'And I scroll to "{string}" on "{string}" page',
    type: "And",
    category: "Actions",
  },
  {
    pattern: "And I fill the form on {string} page",
    type: "And",
    category: "Actions",
  },
  {
    pattern: "And I fill the form on {string} page using data table",
    type: "And",
    category: "Actions",
  },
  {
    pattern: "And I verify below list of values on {string} page",
    type: "And",
    category: "Verification",
  },
  {
    pattern: "And I click on row {string} and column {string} on {string} page",
    type: "And",
    category: "Actions",
  },
  {
    pattern:
      "And I verify data {string} in row {string} and column {string} on {string} page",
    type: "And",
    category: "Verification",
  },
  {
    pattern:
      "And I verify table row {string} has data {string} on {string} page",
    type: "And",
    category: "Verification",
  },
  {
    pattern: "And I Press the {string} Key on the keyboard",
    type: "And",
    category: "Actions",
  },
  { pattern: "And I accept the alert", type: "And", category: "Actions" },
  { pattern: "And I dismiss the alert", type: "And", category: "Actions" },
  {
    pattern: "And I verify alert text with expected text {string}",
    type: "And",
    category: "Verification",
  },
  {
    pattern: "And I accept the alert with entering the text {string}",
    type: "And",
    category: "Actions",
  },
  {
    pattern: "And I click on {string} on {string} page using actions",
    type: "And",
    category: "Actions",
  },
  {
    pattern:
      "And I send {string} as a {string} in to {string} page using actions",
    type: "And",
    category: "Actions",
  },
  {
    pattern: "Given I am on the home page",
    type: "Given",
    category: "Navigation",
  },
  {
    pattern: "And I navigate to page {int}",
    type: "And",
    category: "Navigation",
  },
  {
    pattern: "Then I should see the content of page {int}",
    type: "Then",
    category: "Verification",
  },
  {
    pattern: "And I navigate to next page",
    type: "And",
    category: "Navigation",
  },
  {
    pattern: "Then I should see the content of previous page",
    type: "Then",
    category: "Verification",
  },
  {
    pattern: "And I navigate to previous page",
    type: "And",
    category: "Navigation",
  },
  {
    pattern: "Then I should see the content of next page",
    type: "Then",
    category: "Verification",
  },
  {
    pattern: "Then I capture the image and save it as {string}",
    type: "Then",
    category: "Image",
  },
  {
    pattern: "Then I compare application image with {string}",
    type: "Then",
    category: "Image",
  },
  {
    pattern: "Then I compare partial image {string} with application",
    type: "Then",
    category: "Image",
  },
  {
    pattern: "Then I navigate to page {int} using {string}",
    type: "Then",
    category: "Navigation",
  },
  {
    pattern: "Given I switch to new tab",
    type: "Given",
    category: "Navigation",
  },
  {
    pattern: "Given I switch to main tab",
    type: "Given",
    category: "Navigation",
  },
  {
    pattern: "Given I switch to last tab",
    type: "Given",
    category: "Navigation",
  },
  {
    pattern: "Given I switch to desired tab by {string}",
    type: "Given",
    category: "Navigation",
  },
];

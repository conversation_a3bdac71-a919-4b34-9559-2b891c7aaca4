import Footer from "@/components/Footer";
import Header from "@/components/Header";
import { LoginForm } from "@/components/login-form";
import Image from "next/image";

export default function Page() {
  return (
    <div className="min-h-screen flex flex-col justify-between bg-[linear-gradient(180deg,_#1F5C99_0%,_#102E4D_100%)] opacity-100">
      <Header />
      <div>
        <div className="flex w-full justify-end items-center">
          {" "}
          {/* Right + Center */}
          <div className="flex w-full justify-start items-center space-x-4">
            {" "}
            {/* Left + Center */}
            {/* Left-side image */}
            <div className="hidden md:block">
              {/* Hide on small screens */}
              <Image
                src="/login-left.svg"
                alt="Login Image"
                width={480}
                height={480}
                className="mt-4 ml-8 mb-4 w-[30rem] h-[30rem]  rounded-lg"
              />
            </div>
          </div>
          <div className="w-full max-w-sm">
            <LoginForm />
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}

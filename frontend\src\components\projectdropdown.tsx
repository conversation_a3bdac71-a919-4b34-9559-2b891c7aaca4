import {
  DropdownMenu,
  Dropdown<PERSON>enuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { getProjects } from "@/services/projectmanagement";
import { ChevronDown } from "lucide-react";
import * as React from "react";
import { useState } from "react";

interface ProjectDropdownProps {
  onSelect?: (project: string) => void;
}

export function Projectdropdown({ onSelect }: ProjectDropdownProps) {
  const projects = React.useMemo(() => getProjects(), []);
  const [selectedProject, setSelectedProject] = useState(projects[0]);

  const handleSelect = (project: string) => {
    setSelectedProject(project);
    if (onSelect) onSelect(project);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="bg-[#15537C] text-white px-2 py-1 flex items-center space-x-1 hover:brightness-110 rounded-xs justify-start">
          <span className="text-white/70 text-sm leading-5 font-normal tracking-normal font-roboto">
            Project:
          </span>
          <span className="font-bold text-sm ml-1 truncate whitespace-nowrap overflow-hidden max-w-[160px]">
            {selectedProject}
          </span>
          <ChevronDown className="w-4 h-4 ml-auto shrink-0" />
        </div>
      </DropdownMenuTrigger>

      <DropdownMenuContent>
        {projects.map((project: string, index: number) => (
          <DropdownMenuItem key={project} onClick={() => handleSelect(project)}>
            {project}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

require('dotenv').config();
const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

const tenantRoutes = require('./routes/tenants');
const roleRoutes = require('./routes/roles');
const userRoutes = require('./routes/users');
const projectRoutes = require('./routes/projects');
const loginRoutes = require('./routes/login');
const assignProject = require('./routes/projectAssignment');
const configRoutes = require('./routes/configRoutes');
const excel = require('./routes/apiTestCaseExcel');
const apiRoutes = require('./routes/api');
const testFlowUI = require('./routes/testFlowUI');
const testSuiteUI = require('./routes/testSuiteUI');

const app = express();
const PORT = process.env.PORT || 3000;

// ✅ Allow any origin for testing
app.use(cors({
  origin: '*',
}));


// app.use(cors({
//   origin: 'http://localhost:3000', // <- if your Next.js is on port 3000
//   credentials: true
// }));


// ✅ Middleware
app.use(bodyParser.json());
app.use('/api/tenants', tenantRoutes);
app.use('/api/roles', roleRoutes);
app.use('/api/users', userRoutes);
app.use('/api/projects', projectRoutes);
app.use('/api/login', loginRoutes);
app.use('/api/projectassignment', assignProject);
app.use('/api/config', configRoutes);
app.use('/api/excel', excel);
app.use('/api/testapi', apiRoutes);
app.use('/api/testflowui', testFlowUI);
app.use('/api/testsuiteapi', testSuiteUI);

/*
app.use(cors());
app.use(express.json());
*/


// ✅ Start Server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});


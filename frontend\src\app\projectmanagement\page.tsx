"use client";

import { useState } from "react";
import CreateNewProject from "@/components/new-project";
import AddUserForm from "@/components/AddUserForm"; // New import
import { AppSidebar } from "@/components/dashboard-sidebar";
import { Button } from "@/components/ui/button";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"; // New import
import { BigStatCardsContainer } from "@/components/newcard";

export default function Page() {
  const [showCreateProject, setShowCreateProject] = useState(false);
  const [showAddUser, setShowAddUser] = useState(false); // New state

  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "45px",
        } as React.CSSProperties
      }
    >
      <AppSidebar />
      <SidebarInset>
        <header className="bg-background sticky top-0 flex items-center justify-between border-b p-2">
          {/* Left Side: Title */}
          <h1 className="text-left font-medium text-[24px] leading-[20px] tracking-[0px] text-[#2C2E2E]">
            Projects
          </h1>
          {/* Right Side: Buttons */}
          <div className="flex gap-2 ml-auto">
            <Button
              className="bg-[#15537C] text-white hover:brightness-110 rounded-md w-34 h-6"
              onClick={() => setShowAddUser(true)}
            >
              Add User
            </Button>
            <Button
              className="bg-[#15537C] text-white hover:brightness-110 rounded-md w-34 h-6"
              onClick={() => setShowCreateProject(true)}
            >
              Create Project
            </Button>
          </div>
        </header>

        {/* Add your cards here */}
        <main className="p-6">
          <BigStatCardsContainer />
        </main>
      </SidebarInset>
      
      {/* Show Create Project Dialog */}
      <CreateNewProject
        open={showCreateProject}
        setOpen={setShowCreateProject}
      />

      {/* Show Add User Dialog */}
      <Dialog open={showAddUser} onOpenChange={setShowAddUser}>
        <DialogContent className="max-w-7xl w-[90vw] h-[80vh]">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold">Add New User</DialogTitle>
          </DialogHeader>
          <AddUserForm
            onRecordingStop={() => {}} // You can add any callback here if needed
            onCancel={() => setShowAddUser(false)}
          />
        </DialogContent>
      </Dialog>
    </SidebarProvider>
  );
}
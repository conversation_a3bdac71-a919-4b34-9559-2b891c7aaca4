"use client";

import { AppSidebar } from "@/components/dashboard-sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { useState, useEffect } from "react";
import TestFlowUICreatorDialog from "@/components/TestFlowUI";
import { setModuleNamesGlobal } from "@/services/testApi";
import { toast } from "@/hooks/use-toast";
import axios from "axios";
import { useRouter } from "next/navigation";
import TestFlowUIList from "@/components/testFlowUIList";

interface TestFlow {
  name: string;
  description: string;
  testcases: string[];
}

interface TestCase {
  id: number;
  name: string;
  filePath: string;
  data: any;
}

export default function APITestFlow() {
  const router = useRouter();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [editingTestFlow, setEditingTestFlow] = useState<TestFlow | null>(null);
  const [testFlows, setTestFlows] = useState<TestFlow[]>([]);
  const [allTestCases, setAllTestCases] = useState<TestCase[]>([]);
  const [selectedTestFlow, setSelectedTestFlow] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [testFlowNames, setTestFlowNames] = useState<string[]>([]);
  const [moduleNames, setModuleNames] = useState([]);

    useEffect(()=>{
    const fetchData = async () => {
      try{
      const res = await getAllTestFlowNames(); 
      const files = res.files;
      setTestFlowNames(files);
      setSelectedTestFlow(files[0]);
      getAndSetAllModuleNames();
      }      
      catch (err) {
        console.error(err);
        setError("Failed to load test flows or test cases");
      } finally {
        setLoading(false);
      }

    }

    fetchData();
  },[])

  async function getAndSetAllModuleNames(){
    const res = await getAllModuleNames(); 
    const files = res.files;
    setModuleNames(files);
    setModuleNamesGlobal(files);
  }

  const handleDialogChange = async (open: boolean) => {
    setDialogOpen(open);
    getAndSetAllModuleNames();

    if (!open) {
      // Reset edit mode when closing
      setEditMode(false);
      setEditingTestFlow(null);

      // Refresh test flows and test cases after dialog closes
      setLoading(true);
      setError(null);
      try{
      const res = await getAllTestFlowNames(); 
      const files = res.files;
      setTestFlowNames(files);
      setSelectedTestFlow(files[0]);
      }      
      catch (err) {
        console.error(err);
        setError("Failed to load test flows or test cases");
      } finally {
        setLoading(false);
      }
    }
  };

  // Handle edit button click
  const handleEditTestFlow = () => {
    if (selectedTestFlow) {
      const flowToEdit = testFlows.find(
        (flow) => flow.name === selectedTestFlow
      );
      if (flowToEdit) {
        setEditingTestFlow(flowToEdit);
        setEditMode(true);
        setDialogOpen(true);
      }
    }
  };

      // Enhanced handleRunTestSuite with better error handling
  const handleRunTestSuite = async () => {
   //   const router = useRouter();
    if (!selectedTestFlow) {
      toast({
        title: "No test flow selected",
        description: "Please select a test flow first",
        variant: "destructive",
      });

      return;
    }

    // setRunningTestSuite(true);

    try {
      console.log(`Running test flow: ${selectedTestFlow}`);



const response = await axios.post('/api/test-flow-api', {
  action: 'run',
  suiteName: selectedTestFlow,
});

      // Check if response is ok
      if (!response.status.toString().startsWith('2')) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.data;
      console.log("🔍 API Response:", result);

      // Handle different response scenarios with better error checking
      if (result.action === "STOPPED_DURING_RUN") {
        console.log("Test flow was stopped during execution");
        toast({
          title: "Stopped",
          description: result.message || "Test flow was stopped",
          variant: "success",
        });
      } else if (result.action === "COMPLETED" && result.success) {
        console.log("Test flow completed successfully");
        toast({
          title: "Completed",
          description: result.message || "Test flow completed successfully",
          variant: "success",
        });
      } else if (result.action === "FAILED") {
        console.log("Test flow failed");
        toast({
          title: "Failed",
          description: result.message || "Test flow execution failed",
          variant: "destructive",
        });
      } else if (result.success === true) {
        console.log("Test flow executed");
        toast({
          title: "Success",
          description: `Test flow "${selectedTestFlow}" executed successfully!`,
          variant: "success",
        });
      } else if (result.success === false) {
        // Handle explicit failure with better error message
        const errorMessage =
          result.error || result.message || "Unknown error occurred";
        console.error("Test flow run failed:", errorMessage);
        toast({
          title: "Failed to run",
          description: errorMessage,
          variant: "destructive",
        });
      } else {
        // Handle unexpected response format
        console.warn("Unexpected response format:", result);
        toast({
          title: "Unexpected response",
          description: "Check console for details",
        });
      }
       router.push("/dashboard"); // Redirect to dashboard after running
        
    } catch (error) {
      console.error("Network/Parsing error:", error);

      // Better error message handling
      let errorMessage = "Unknown error occurred";
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === "string") {
        errorMessage = error;
      }

      toast({
        title: "Network error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      // setRunningTestSuite(false);
         
    }
       
  };

  if (loading) {
    return (
      <SidebarProvider
        style={{ "--sidebar-width": "45px" } as React.CSSProperties}
      >
        <AppSidebar />
        <SidebarInset>
          <div className="flex h-screen items-center justify-center">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#15537C]"></div>
              <span>Loading test flows...</span>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  if (error) {
    return (
      <SidebarProvider
        style={{ "--sidebar-width": "45px" } as React.CSSProperties}
      >
        <AppSidebar />
        <SidebarInset>
          <div className="flex h-screen items-center justify-center">
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600 font-medium">Error loading data</p>
              <p className="text-red-500 text-sm">{error}</p>
              <Button
                onClick={() => window.location.reload()}
                className="mt-2 text-sm"
                variant="outline"
              >
                Retry
              </Button>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  return (
    <>
    <SidebarProvider
      style={{ "--sidebar-width": "45px" } as React.CSSProperties}
    >
      <AppSidebar />
      <SidebarInset>
        <div className="flex h-screen overflow-hidden">
          <aside className="w-[15%] border-r bg-muted p-2 space-y-1 bg-[#F7F9FC] font-normal tracking-normal font-roboto">
            <div className="bg-[#15537C] text-white px-2 py-1 flex items-center space-x-1 hover:brightness-110 rounded-xs justify-start">
              <span className="font-bold text-sm ml-1 truncate whitespace-nowrap overflow-hidden max-w-[160px]">
                Test Flows
              </span>
            </div>
            <ul className="text-[#37393A] py-2 flex flex-col gap-2 rounded-xs">
              {testFlowNames.map((flowName) => (
                <li
                  key={flowName}
                  className="text-left w-full text-sm hover:bg-[#B1DBEA]"
                >
                  <button
                    className={`w-full px-2 py-1 text-left ${
                      selectedTestFlow === flowName
                        ? "bg-[#B1DBEA] text-[#15537C] font-medium"
                        : ""
                    }`}
                    onClick={() => setSelectedTestFlow(flowName)}
                    // title={flow.description}
                  >
                    <div className="truncate">{flowName}</div>
                  </button>
                </li>
              ))}
            </ul>
          </aside>

          <div className="flex-1 flex flex-col overflow-hidden">
            <header className="bg-background flex items-emd justify-between border-b p-2">
              <div className="flex justify-end space-x-3 w-full">
                {/* <h1 className="text-left font-medium text-[24px] leading-[20px] tracking-[0px] text-[#2C2E2E]">
                  {selectedTestFlow
                    ? `Test Cases - ${selectedTestFlow}`
                    : "Test Flows"}
                </h1> */}
                              <Button
                className="bg-[#15537C] text-white hover:brightness-110 rounded-md w-34 h-6"
                onClick={() => {
                  setEditMode(false);
                  setEditingTestFlow(null);
                  setDialogOpen(true);
                }}
              >
                Create Test Flow
              </Button>
              <Button
                className="bg-[#15537C] text-white hover:brightness-110 rounded-md w-34 h-6"
                onClick={handleRunTestSuite}
                
              >
                Run Test Flow
              </Button>
              </div>

            </header>
            <div className="flex-1 overflow-auto p-4">
              <TestFlowUIList selectedTestFlow={selectedTestFlow} />
            </div>
          </div>
           

        </div>
        
      </SidebarInset>
          
      <TestFlowUICreatorDialog
        open={dialogOpen}
        onOpenChange={handleDialogChange}
        editMode={editMode}
        editingTestFlow={editingTestFlow}
        moduleNames={moduleNames}
      />
      
    </SidebarProvider>
   
    </>
  );
}

async function getAllTestFlowNames() {
  const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/testflowui`);
  return await response.json();
}

async function getAllModuleNames() {
  const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/testapi`);
  return await response.json();
}

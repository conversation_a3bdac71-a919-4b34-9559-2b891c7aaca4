const ExcelJS = require("exceljs");
const fs = require("fs").promises;
const path = require("path");

const UPLOADS_DIR = __dirname + '/../create-api';


async function fileExists(filePath) {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

function getOrCreateWorksheet(workbook, worksheetName = "Sheet 1") {
  let worksheet = workbook.getWorksheet(worksheetName) || workbook.getWorksheet(1);
  if (!worksheet) {
    worksheet = workbook.addWorksheet(worksheetName);
  }
  return worksheet;
}

function getNextAutoId(worksheet) {
  let maxId = 0;
  
  if (worksheet.rowCount > 1) {
    // Find the ID column index (assuming it's the first column or named "ID")
    const headerRow = worksheet.getRow(1);
    let idColumnIndex = 1; // Default to first column
    
    headerRow.eachCell((cell, colNumber) => {
      if (cell.value === 'ID' || cell.value === 'id') {
        idColumnIndex = colNumber;
      }
    });
    
    // Check all existing rows to find the maximum ID
    for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
      const row = worksheet.getRow(rowNumber);
      const cellValue = row.getCell(idColumnIndex).value;
      const id = parseInt(cellValue);
      if (!isNaN(id) && id > maxId) {
        maxId = id;
      }
    }
  }
  
  return maxId + 1;
}

async function updateOrCreateExcel(fileName, data) {
  const safeFileName = path.basename("TC_" + fileName);
  const filePath = path.join(UPLOADS_DIR, `${safeFileName}.xlsx`);
  let modifiedData;

  await fs.mkdir(UPLOADS_DIR, { recursive: true });

  const workbook = new ExcelJS.Workbook();

  if (await fileExists(filePath)) {
    const fileBuffer = await fs.readFile(filePath);
    await workbook.xlsx.load(fileBuffer);

    const worksheet = getOrCreateWorksheet(workbook);
    
    const autoId = getNextAutoId(worksheet);
    
    modifiedData = { id: autoId, ...data };

    console.log(`Worksheet found. Row count before adding: ${worksheet.rowCount}`);
    worksheet.insertRow(worksheet.rowCount + 1, Object.values(modifiedData));
    console.log("Row count after adding:", worksheet.rowCount);
    console.log(`File '${safeFileName}.xlsx' exists. Appending a new row with ID: ${autoId}`);
  } else {
    modifiedData = { id: 1, ...data };
    
    const headers = Object.keys(modifiedData).map((key) => ({ header: key, key: key }));
    const worksheet = workbook.addWorksheet("Sheet 1");
    worksheet.columns = headers;
    worksheet.addRow(modifiedData);
    console.log(`Creating new file '${safeFileName}.xlsx' with headers and first row with ID: 1`);
  }

  try {
    await workbook.xlsx.writeFile(filePath);
    console.log("Successfully saved the workbook.");
    return modifiedData.id;
  } catch (err) {
    console.error("Error writing the Excel file:", err.message);
    throw new Error(
      `Failed to write to ${filePath}. Check file permissions or if the file is open elsewhere.`
    );
  }
}

function findRowById(worksheet, id) {
  const headerRow = worksheet.getRow(1);
  if (!headerRow.values || headerRow.values.length === 0) {
    console.warn("No headers found in the worksheet.");
    return null;
  }

  let idColumnIndex = -1;
  headerRow.eachCell((cell, colNumber) => {
    if (cell.value && cell.value.toString().toLowerCase() === 'id') {
      idColumnIndex = colNumber;
    }
  });

  if (idColumnIndex === -1) {
    console.warn("No 'ID' column found in the worksheet.");
    return null;
  }

  console.log(`Looking for ID: ${id} in column ${idColumnIndex}`);

  for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
    const row = worksheet.getRow(rowNumber);
    const cellValue = row.getCell(idColumnIndex).value;
    
    console.log(`Row ${rowNumber}, Cell value: ${cellValue}, Looking for: ${id}`);
    
    if (cellValue != null && cellValue.toString() === id.toString()) {
      console.log(`Found matching row: ${rowNumber}`);
      return row;
    }
  }

  console.log(`No row found with ID: ${id}`);
  return null;
}

async function deleteRowFromExcel(fileName, id) {
  const safeFileName = path.basename("TC_" + fileName);
  const filePath = path.join(UPLOADS_DIR, `${safeFileName}.xlsx`);

  if (!(await fileExists(filePath))) {
    throw new Error(`File '${safeFileName}.xlsx' does not exist.`);
  }

  const workbook = new ExcelJS.Workbook();
  const fileBuffer = await fs.readFile(filePath);
  await workbook.xlsx.load(fileBuffer);

  const worksheet = getOrCreateWorksheet(workbook);
  console.log("id", id)
  const targetRow = findRowById(worksheet, id);

  if (!targetRow) {
    console.log(`No row found with ID: ${id}`);
    return false;
  }

  const rowNumber = targetRow.number;
  console.log(`Deleting row ${rowNumber} (ID: ${id}) from '${safeFileName}.xlsx'`);

  worksheet.spliceRows(rowNumber, 1);

  try {
    await workbook.xlsx.writeFile(filePath);
    console.log("Successfully deleted row and saved the workbook.");
    return true;
  } catch (err) {
    console.error("Error writing the Excel file:", err.message);
    throw new Error(
      `Failed to write to ${filePath}. Check file permissions or if the file is open elsewhere.`
    );
  }
}

async function updateRowInExcel(fileName, id, updateData) {
  const safeFileName = path.basename("TC_" + fileName);
  const filePath = path.join(UPLOADS_DIR, `${safeFileName}.xlsx`);

  if (!(await fileExists(filePath))) {
    throw new Error(`File '${safeFileName}.xlsx' does not exist.`);
  }

  const workbook = new ExcelJS.Workbook();
  const fileBuffer = await fs.readFile(filePath);
  await workbook.xlsx.load(fileBuffer);

  const worksheet = getOrCreateWorksheet(workbook);
  const targetRow = findRowById(worksheet, id);
  console.log(targetRow)

  if (!targetRow) {
    console.log(`No row found with ID: ${id}`);
    return false;
  }

  const headerRow = worksheet.getRow(1);
  const headers = {};

  headerRow.eachCell((cell, colNumber) => {
    if (cell.value) {
      headers[cell.value.toString()] = colNumber;
    }
  });

  console.log(`Headers found:`, Object.keys(headers));
  console.log(`Updating row ${targetRow.number} (ID: ${id}) in '${safeFileName}.xlsx'`);
  console.log(`Update data:`, updateData);

  let updatedFields = 0;
  for (const [key, value] of Object.entries(updateData)) {
    const colNumber = headers[key];
    if (colNumber) {
      console.log(`Updating column ${colNumber} (${key}) with value: ${value}`);
      targetRow.getCell(colNumber).value = value;
      updatedFields++;
      console.log(`Successfully updated ${key}: ${value}`);
    } else {
      console.warn(`Column '${key}' not found in the worksheet. Available columns: ${Object.keys(headers).join(', ')}`);
    }
  }

  if (updatedFields === 0) {
    console.warn("No fields were updated. Check if column names match exactly.");
    return false;
  }

  worksheet.getCell('A1').value = worksheet.getCell('A1').value;

  try {
    await workbook.xlsx.writeFile(filePath);
    console.log(`Successfully updated ${updatedFields} fields and saved the workbook.`);
    return true;
  } catch (err) {
    console.error("Error writing the Excel file:", err.message);
    throw new Error(
      `Failed to write to ${filePath}. Check file permissions or if the file is open elsewhere.`
    );
  }
}

async function readExcelRows(fileName) {
  const safeFileName = path.basename("TC_" + fileName);
  const filePath = path.join(UPLOADS_DIR, `${safeFileName}.xlsx`);

  if (!(await fileExists(filePath))) {
    throw new Error(`File '${safeFileName}.xlsx' does not exist.`);
  }

  const workbook = new ExcelJS.Workbook();
  const fileBuffer = await fs.readFile(filePath);
  await workbook.xlsx.load(fileBuffer);

  const worksheet = getOrCreateWorksheet(workbook);
  const rows = [];
  const headerRow = worksheet.getRow(1);
  const headers = [];

  headerRow.eachCell((cell, colNumber) => {
    headers[colNumber - 1] = cell.value;
  });

  console.log(`Headers: ${headers.join(', ')}`);

  for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
    const row = worksheet.getRow(rowNumber);
    const rowData = {};

    row.eachCell((cell, colNumber) => {
      const header = headers[colNumber - 1];
      if (header) {
        rowData[header] = cell.value;
      }
    });

    if (Object.values(rowData).some(value => value !== null && value !== undefined && value !== '')) {
      rows.push(rowData);
    }
  }

  console.log(`Found ${rows.length} data rows`);
  return rows;
}


module.exports = {
  updateOrCreateExcel,
  deleteRowFromExcel,
  updateRowInExcel,
  readExcelRows
};
"use client";

import { useState, useEffect } from "react";
import axios from "axios";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import {
  Search,
  ChevronRight,
  ChevronLeft,
  ChevronDown,
  ChevronUp,
  Loader2,
  CheckCircle,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";


// API response interfaces
interface ApiItem {
  id: number;
  name: string;
  type: string;
}

interface TestSuite {
  name: string;
  description: string;
  testFlows: string[];
  testCases: string[];
}

interface CreateTestSuiteDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  editMode?: boolean;
  editingTestSuite?: TestSuite | null;
}

export default function CreateTestSuiteDialog({
  open,
  onOpenChange,
  editMode = false,
  editingTestSuite = null,
}: CreateTestSuiteDialogProps) {
  // Form state
  const [testSuiteName, setTestSuiteName] = useState<string>("");
  const [testSuiteDescription, setTestSuiteDescription] = useState<string>("");
  const [selectedType, setSelectedType] = useState<string>("Test Flows");


  // Data state
  const [availableFlows, setAvailableFlows] = useState<ApiItem[]>([]);
  const [availableCases, setAvailableCases] = useState<ApiItem[]>([]);
  const [selectedItems, setSelectedItems] = useState<ApiItem[]>([]);

  // UI state
  const [leftSelectedIds, setLeftSelectedIds] = useState<number[]>([]);
  const [rightSelectedIds, setRightSelectedIds] = useState<number[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [searchSelectedQuery, setSearchSelectedQuery] = useState<string>("");
  const [selectDropdownOpen, setSelectDropdownOpen] = useState<boolean>(false);

  // Loading and error states
  const [isLoadingFlows, setIsLoadingFlows] = useState<boolean>(false);
  const [isLoadingCases, setIsLoadingCases] = useState<boolean>(false);
  const [isCreating, setIsCreating] = useState<boolean>(false);

  const { toast } = useToast();

  // Add custom CSS for focus styles and enhanced toast styling
  useEffect(() => {
    const style = document.createElement("style");
    style.innerHTML = `
      .checkbox-row {
        cursor: pointer;
      }
      .checkbox-row:hover {
        background-color: #f9fafb;
      }

      /* Enhanced toast styling */
      .success-toast {
        backdrop-filter: blur(8px);
        animation: slideInFromRight 0.3s ease-out, pulse 0.6s ease-in-out;
      }
      
      @keyframes slideInFromRight {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }
      
      @keyframes pulse {
        0%, 100% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.02);
        }
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Fetch test flows from API
  const fetchTestFlows = async () => {
    setIsLoadingFlows(true);


try {
  const response = await axios.get("/api/test-flows");

  const flows = response.data;

  const transformedFlows: ApiItem[] = flows.map(
    (flow: any, index: number) => ({
      id: index + 1,
      name: flow.name,
      type: "Test Flows",
    })
  );

  setAvailableFlows(
    transformedFlows.sort((a, b) => a.name.localeCompare(b.name))
  );
} catch (err: any) {
  console.error("Error fetching test flows:", err);
  toast({
    title: "Load failed",
    description: err.response?.data?.error || "Failed to load test flows",
    variant: "destructive",
  });
} finally {
  setIsLoadingFlows(false);
}

  };

  // Fetch test cases from API
  const fetchTestCases = async () => {
    setIsLoadingCases(true);

try {
  const response = await axios.get("/api/test-cases", {
    params: { type: "names" },
  });

  const cases = response.data;

  const transformedCases: ApiItem[] = cases.map((testCase: any) => ({
    id: testCase.id + 100,
    name: testCase.name,
    type: "Test Cases",
  }));

  setAvailableCases(
    transformedCases.sort((a, b) => a.name.localeCompare(b.name))
  );
} catch (err: any) {
  console.error("Error fetching test cases:", err);
  toast({
    title: "Load failed",
    description: err.response?.data?.error || "Failed to load test cases",
    variant: "destructive",
  });
} finally {
  setIsLoadingCases(false);
}

  };

  // Load data when dialog opens
  useEffect(() => {
    if (open) {
      fetchTestFlows();
      fetchTestCases();
    }
  }, [open]);

  // Populate form when editing
  useEffect(() => {
    if (editMode && editingTestSuite && open) {
      setTestSuiteName(editingTestSuite.name);
      setTestSuiteDescription(editingTestSuite.description || "");

      // We need to wait for the data to load before setting selected items
      const timer = setTimeout(() => {
        const flowItems: ApiItem[] = editingTestSuite.testFlows.map(
          (flowName, index) => ({
            id: index + 1,
            name: flowName,
            type: "Test Flows",
          })
        );

        const caseItems: ApiItem[] = editingTestSuite.testCases.map(
          (caseName, index) => ({
            id: index + 100,
            name: caseName,
            type: "Test Cases",
          })
        );

        setSelectedItems([...flowItems, ...caseItems]);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [editMode, editingTestSuite, open]);

  // Reset form when dialog closes
  useEffect(() => {
    if (!open) {
      setTestSuiteName("");
      setTestSuiteDescription("");
      setSelectedType("Test Flows");
      setSelectedItems([]);
      setLeftSelectedIds([]);
      setRightSelectedIds([]);
      setSearchQuery("");
      setSearchSelectedQuery("");
    }
  }, [open]);

  const getAvailableItems = (): ApiItem[] => {
    const currentItems =
      selectedType === "Test Flows" ? availableFlows : availableCases;
    // Filter out items that are already selected
    return currentItems.filter(
      (item) =>
        !selectedItems.some(
          (selected) =>
            selected.name === item.name && selected.type === item.type
        )
    );
  };

  const filteredItems = getAvailableItems().filter((item) =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredSelectedItems = selectedItems.filter((item) =>
    item.name.toLowerCase().includes(searchSelectedQuery.toLowerCase())
  );

  const handleLeftCheckboxChange = (id: number, checked: boolean): void => {
    setLeftSelectedIds((prev) =>
      checked ? [...prev, id] : prev.filter((itemId) => itemId !== id)
    );
  };

  const handleRightCheckboxChange = (id: number, checked: boolean): void => {
    setRightSelectedIds((prev) =>
      checked ? [...prev, id] : prev.filter((itemId) => itemId !== id)
    );
  };

  const handleSelectAllLeft = (checked: boolean): void => {
    setLeftSelectedIds(checked ? filteredItems.map((item) => item.id) : []);
  };

  const handleSelectAllRight = (checked: boolean): void => {
    setRightSelectedIds(
      checked ? filteredSelectedItems.map((item) => item.id) : []
    );
  };

  const areAllFilteredItemsSelected = (): boolean =>
    filteredItems.length > 0 &&
    filteredItems.every((item) => leftSelectedIds.includes(item.id));

  const areAllFilteredSelectedItemsSelected = (): boolean =>
    filteredSelectedItems.length > 0 &&
    filteredSelectedItems.every((item) => rightSelectedIds.includes(item.id));

  const toggleLeftCheckbox = (id: number): void => {
    setLeftSelectedIds((prev) =>
      prev.includes(id) ? prev.filter((itemId) => itemId !== id) : [...prev, id]
    );
  };

  const toggleRightCheckbox = (id: number): void => {
    setRightSelectedIds((prev) =>
      prev.includes(id) ? prev.filter((itemId) => itemId !== id) : [...prev, id]
    );
  };

  const handleAddItems = (): void => {
    const itemsToMove = getAvailableItems().filter((item) =>
      leftSelectedIds.includes(item.id)
    );

    const newSelectedItems = [...selectedItems, ...itemsToMove].sort((a, b) =>
      a.name.localeCompare(b.name)
    );

    setSelectedItems(newSelectedItems);
    setLeftSelectedIds([]);
  };

  const handleRemoveItems = (): void => {
    const itemsToRemove = selectedItems.filter((item) =>
      rightSelectedIds.includes(item.id)
    );

    setSelectedItems((prev) =>
      prev.filter((item) => !rightSelectedIds.includes(item.id))
    );

    setRightSelectedIds([]);
  };

  const handleCreateOrUpdateTestSuite = async () => {
    // Validation
    if (!testSuiteName.trim()) {
      toast({
      title: "Missing field",
      description: "Test Suite Name is required",
      variant: "destructive",
    });

      return;
    }

    if (selectedItems.length === 0) {
      toast({
      title: "Nothing selected",
      description: "At least one test flow or test case must be selected",
      variant: "destructive",
    });
      return;
    }

    setIsCreating(true);
try {
  const testFlows = selectedItems
    .filter((item) => item.type === "Test Flows")
    .map((item) => item.name);

  const testCases = selectedItems
    .filter((item) => item.type === "Test Cases")
    .map((item) => item.name);

  const payload = {
    name: testSuiteName.trim(),
    description: testSuiteDescription.trim(),
    testFlows,
    testCases,
  };

  let response;
  if (editMode && editingTestSuite) {
    // Update existing test suite
    response = await axios.put("/api/test-suite", {
      ...payload,
      originalName: editingTestSuite.name,
    });
  } else {
    // Create new test suite
    response = await axios.post("/api/test-suite", payload);
  }

  const result = response.data;

  toast({
    title: "Success",
    description:
      result.message ||
      `Test suite "${testSuiteName}" ${editMode ? "updated" : "created"} successfully!`,
    duration: 1500,
    style: {
      backgroundColor: "#00AB6A",
      borderColor: "#00966A",
      color: "white",
      borderRadius: "12px",
      boxShadow:
        "0 10px 25px rgba(0, 171, 106, 0.3), 0 4px 10px rgba(0, 0, 0, 0.1)",
      border: "1px solid #00966A",
    },
    className:
      "top-4 right-4 w-96 h-16 text-sm p-4 flex items-center gap-3 success-toast",
  });

  onOpenChange(false);
} catch (err: any) {
  const errorMsg =
    err.response?.data?.error ||
    (err instanceof Error
      ? err.message
      : `Failed to ${editMode ? "update" : "create"} test suite`);

  toast({
    title: "Error",
    description: errorMsg,
    variant: "destructive",
  });
} finally {
  setIsCreating(false);
}

  };

  const isLoading = isLoadingFlows || isLoadingCases;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-screen overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {editMode
              ? `Edit Test Suite: ${editingTestSuite?.name}`
              : "Create New Test Suite"}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Test Suite Name */}
          <div>
            <label className="font-medium">
              Test Suite Name<span className="text-red-500">*</span>
            </label>
            <Input
              placeholder="Provide a Test Suite Name"
              value={testSuiteName}
              onChange={(e) => setTestSuiteName(e.target.value)}
              required
              className="border border-[#A9ACAE]"
            />
          </div>

          {/* Description and Dropdown */}
          <div className="flex gap-4">
            <div className="flex-1">
              <label className="font-medium">Test Suite Description</label>
              <Textarea
                placeholder="Provide a Test Suite Description"
                value={testSuiteDescription}
                onChange={(e) => setTestSuiteDescription(e.target.value)}
                className="resize-none min-h-[38px] max-h-[38px] py-2 border border-[#A9ACAE]"
              />
            </div>

            <div className="w-[220px]">
              <label className="font-medium">Select</label>
              <Select
                onOpenChange={(isOpen) => {
                  setSelectDropdownOpen(isOpen);
                }}
                onValueChange={(value: string) => {
                  setSelectedType(value);
                  setSearchQuery("");
                  setLeftSelectedIds([]);
                }}
                defaultValue="Test Flows"
              >
                <SelectTrigger className="h-[38px] border border-[#A9ACAE] [&>svg]:hidden">
                  <div className="flex items-center justify-between w-full">
                    <SelectValue placeholder="Select Option" />
                    {selectDropdownOpen ? (
                      <ChevronUp className="h-4 w-4 opacity-50" />
                    ) : (
                      <ChevronDown className="h-4 w-4 opacity-50" />
                    )}
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem
                    value="Test Flows"
                    className="focus:bg-[#B1DBEA] data-[state=checked]:bg-[#B1DBEA]"
                  >
                    Test Flows
                  </SelectItem>
                  <SelectItem
                    value="Test Cases"
                    className="focus:bg-[#B1DBEA] data-[state=checked]:bg-[#B1DBEA]"
                  >
                    Test Cases
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Dual List Layout */}
          {isLoading ? (
            <div className="flex items-center justify-center h-64 border border-[#A9ACAE] rounded">
              <div className="flex items-center gap-2">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span>Loading...</span>
              </div>
            </div>
          ) : (
            <div className="flex gap-4">
              {/* Left Column - Available Items */}
              <div className="w-1/2">
                <p className="font-medium mb-2">
                  List of {selectedType} ({getAvailableItems().length})
                </p>

                <div className="border border-[#A9ACAE] rounded h-64 overflow-auto">
                  <div className="p-2 border-b border-[#A9ACAE] sticky top-0 bg-white z-10">
                    <div className="flex items-center justify-between">
                      <div
                        className="flex items-center gap-2 checkbox-row px-1 py-0.5 rounded"
                        onClick={() =>
                          handleSelectAllLeft(!areAllFilteredItemsSelected())
                        }
                      >
                        <input
                          type="checkbox"
                          onChange={(e) => {
                            e.stopPropagation();
                            handleSelectAllLeft(e.target.checked);
                          }}
                          onClick={(e) => e.stopPropagation()}
                          checked={areAllFilteredItemsSelected()}
                          className="mr-2 h-4 w-4 accent-blue-900 rounded text-white focus:ring-0"
                        />
                        <span className="text-sm font-medium">Select All</span>
                      </div>
                      <div className="relative w-40">
                        <Input
                          placeholder="Search..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="pr-8 h-8 border border-[#A9ACAE]"
                        />
                        <Search className="h-4 w-4 absolute right-3 top-2 text-gray-400" />
                      </div>
                    </div>
                  </div>

                  <div className="p-2 space-y-2">
                    {filteredItems.map((item) => (
                      <div
                        key={item.id}
                        className="flex items-center gap-2 p-1 checkbox-row"
                        onClick={() => toggleLeftCheckbox(item.id)}
                      >
                        <input
                          type="checkbox"
                          checked={leftSelectedIds.includes(item.id)}
                          onChange={(e) => {
                            e.stopPropagation();
                            handleLeftCheckboxChange(item.id, e.target.checked);
                          }}
                          onClick={(e) => e.stopPropagation()}
                          className="mr-2 h-4 w-4 accent-blue-900 rounded text-white focus:ring-0"
                        />
                        <span className="text-sm">{item.name}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Middle Buttons */}
              <div className="flex flex-col justify-center gap-2">
                <button
                  onClick={handleAddItems}
                  disabled={leftSelectedIds.length === 0}
                  className="bg-[#15537C] text-white w-12 h-8 rounded-md flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:brightness-110"
                >
                  <ChevronRight size={20} />
                </button>
                <button
                  onClick={handleRemoveItems}
                  disabled={rightSelectedIds.length === 0}
                  className="bg-white border border-[#A9ACAE] w-12 h-8 rounded-md flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  <ChevronLeft size={20} color="#15537C" />
                </button>
              </div>

              {/* Right Column - Selected Items */}
              <div className="w-1/2">
                <p className="font-medium mb-2">
                  Selected Test Flows (
                  {
                    selectedItems.filter((item) => item.type === "Test Flows")
                      .length
                  }
                  ) & Test Cases (
                  {
                    selectedItems.filter((item) => item.type === "Test Cases")
                      .length
                  }
                  )
                </p>
                <div className="border border-[#A9ACAE] rounded h-64 overflow-auto">
                  <div className="p-2 border-b border-[#A9ACAE] sticky top-0 bg-white z-10">
                    <div className="flex items-center justify-between">
                      <div
                        className="flex items-center gap-2 checkbox-row px-1 py-0.5 rounded"
                        onClick={() =>
                          handleSelectAllRight(
                            !areAllFilteredSelectedItemsSelected()
                          )
                        }
                      >
                        <input
                          type="checkbox"
                          onChange={(e) => {
                            e.stopPropagation();
                            handleSelectAllRight(e.target.checked);
                          }}
                          onClick={(e) => e.stopPropagation()}
                          checked={areAllFilteredSelectedItemsSelected()}
                          className="mr-2 h-4 w-4 accent-blue-900 rounded text-white focus:ring-0"
                        />
                        <span className="text-sm font-medium">Select All</span>
                      </div>
                      <div className="relative w-40">
                        <Input
                          placeholder="Search..."
                          value={searchSelectedQuery}
                          onChange={(e) =>
                            setSearchSelectedQuery(e.target.value)
                          }
                          className="pr-8 h-8 border border-[#A9ACAE]"
                        />
                        <Search className="h-4 w-4 absolute right-3 top-2 text-gray-400" />
                      </div>
                    </div>
                  </div>

                  <div className="p-2 space-y-2">
                    {filteredSelectedItems.map((item) => (
                      <div
                        key={item.id}
                        className="flex items-center gap-2 p-1 checkbox-row"
                        onClick={() => toggleRightCheckbox(item.id)}
                      >
                        <input
                          type="checkbox"
                          checked={rightSelectedIds.includes(item.id)}
                          onChange={(e) => {
                            e.stopPropagation();
                            handleRightCheckboxChange(
                              item.id,
                              e.target.checked
                            );
                          }}
                          onClick={(e) => e.stopPropagation()}
                          className="mr-2 h-4 w-4 accent-blue-900 rounded text-white focus:ring-0"
                        />
                        <span className="text-sm">{item.name}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            className="border-[#A9ACAE] text-gray-700 hover:bg-gray-50 hover:text-gray-800 w-24 rounded"
            onClick={() => onOpenChange(false)}
            disabled={isCreating}
          >
            Cancel
          </Button>
          <Button
            className="bg-[#00AB6A] hover:bg-[#00AB6A]/90 text-white w-24 rounded"
            onClick={handleCreateOrUpdateTestSuite}
            disabled={isCreating}
          >
            {isCreating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {editMode ? "Updating..." : "Creating..."}
              </>
            ) : editMode ? (
              "Update"
            ) : (
              "Create"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

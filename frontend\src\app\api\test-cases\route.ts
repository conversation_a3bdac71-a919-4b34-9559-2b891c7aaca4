import { promises as fs } from "fs";
import { NextResponse } from "next/server";
import config from "@/config/config.json";
const RECORDINGS_PATH = config.recordingsPath;

// Parse individual Gherkin step to match reference structure (action.label, action.selector, action.value, action.url)
function parseGherkinStep(gherkinLine: string, stepNumber: number): any | null {
  const trimmedLine = gherkinLine.trim();

  // Skip setup steps and empty lines
  if (
    !trimmedLine ||
    trimmedLine.includes("Given I am on the home page") ||
    trimmedLine.includes('Then I load the "elementProperties"')
  ) {
    return null;
  }

  // Parse click actions: Then I click on "label" on "url" page
  const clickMatch = trimmedLine.match(
    /Then I click on "([^"]*)" on "([^"]*)" page/
  );
  if (clickMatch) {
    return {
      action: "click",
      label: clickMatch[1], // label (what appears in feature file)
      selector: clickMatch[1], // selector (same as label for parsing)
    };
  }

  // Parse type/send actions: Then I send "label" as a "value" in to "url" page
  const sendMatch = trimmedLine.match(
    /Then I send "([^"]*)" as a "([^"]*)" in to "([^"]*)" page/
  );
  if (sendMatch) {
    return {
      action: "type",
      label: sendMatch[1], // label (what appears in feature file)
      selector: sendMatch[1], // selector (same as label for parsing)
      value: sendMatch[2], // value
    };
  }

  // Parse select actions: Then I select "label" with data "value" on "url" page
  const selectMatch = trimmedLine.match(
    /Then I select "([^"]*)" with data "([^"]*)" on "([^"]*)" page/
  );
  if (selectMatch) {
    return {
      action: "select",
      label: selectMatch[1], // label (what appears in feature file)
      selector: selectMatch[1], // selector (same as label for parsing)
      value: selectMatch[2], // value
    };
  }

  const assertionMatch = trimmedLine.match(
    /Then I verify "([^"]*)" has data "([^"]*)" on "([^"]*)" page/
  );
  if (assertionMatch) {
    return {
      action: "assertion",
      label: assertionMatch[1], // element/selector
      selector: assertionMatch[1], // same as label for parsing
      value: assertionMatch[2], // expected data
      url: assertionMatch[3], // page URL
    };
  }

  // Parse navigate actions: Then I should be navigated to "url"
  const navigateMatch = trimmedLine.match(
    /Then I should be navigated to "([^"]*)"/
  );
  if (navigateMatch) {
    return {
      action: "navigate",
      url: navigateMatch[1], // url
    };
  }

  // If no pattern matches, return null
  return null;
}

// Parse entire feature file content to raw step array (similar to original JSON format)
function parseFeatureFileToRawSteps(featureContent: string): any[] {
  const lines = featureContent.split("\n");
  const steps: any[] = [];

  for (const line of lines) {
    const parsedStep = parseGherkinStep(line, steps.length + 1);
    if (parsedStep) {
      steps.push(parsedStep);
    }
  }

  return steps;
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get("type");
    const featureName = searchParams.get("feature");

    const fileNames = await fs.readdir(RECORDINGS_PATH);
    const featureFiles = fileNames.filter((file) => file.endsWith(".feature"));

    // If requesting specific feature file content
    if (type === "feature" && featureName) {
      try {
        const featureFilePath = `${RECORDINGS_PATH}/${featureName}.feature`;
        const featureContent = await fs.readFile(featureFilePath, "utf-8");
        return NextResponse.json({ content: featureContent });
      } catch (error) {
        console.error(
          `Failed to read feature file ${featureName}.feature:`,
          error
        );
        return NextResponse.json({ content: "Feature file not found." });
      }
    }

    // If type=names, return only basic info for TestFlow component
    if (type === "names") {
      const testCaseNames = featureFiles.map((fileName, index) => ({
        id: index + 1,
        name: fileName.replace(".feature", ""),
      }));
      return NextResponse.json(testCaseNames);
    }

    // return full data with parsed content from feature files for AccordionTable
    const testCases = await Promise.all(
      featureFiles.map(async (fileName, index) => {
        const filePath = `${RECORDINGS_PATH}/${fileName}`;
        let parsedData = null;

        try {
          const featureContent = await fs.readFile(filePath, "utf-8");
          parsedData = parseFeatureFileToRawSteps(featureContent);
        } catch (err) {
          console.error(`Failed to parse feature file ${fileName}:`, err);
        }

        return {
          id: index + 1,
          name: fileName.replace(".feature", ""),
          //filePath: filePath,
          data: parsedData, // Add parsed feature content here
        };
      })
    );
    return NextResponse.json(testCases);
  } catch (error) {
    console.error("Error reading test cases directory:", error);
    return NextResponse.json(
      { error: "Failed to fetch test cases" },
      { status: 500 }
    );
  }
}

// POST handler for updating feature files with test flow tags
export async function POST(request: Request) {
  try {
    const { testFlowName, selectedTestCases } = await request.json();

    // validates the input
    if (!testFlowName || !testFlowName.trim()) {
      return NextResponse.json(
        { error: "Test flow name is required" },
        { status: 400 }
      );
    }

    if (!selectedTestCases || selectedTestCases.length === 0) {
      return NextResponse.json(
        { error: "At least one test case must be selected" },
        { status: 400 }
      );
    }

    // Ensure feature file tags use underscore format for file system compatibility
    const tagToAdd = `@${testFlowName.trim().replace(/\s+/g, "_")}`;
    const filesToUpdate = [];
    const filesAlreadyHaveTag = [];

    // STEP 1: checks each file and categorize them
    for (const testCase of selectedTestCases) {
      const featureFilePath = `${RECORDINGS_PATH}/${testCase.name}.feature`;

      try {
        // reads existing feature file content
        const content = await fs.readFile(featureFilePath, "utf-8");
        const lines = content.split("\n");

        // checks if first line contains tags and if tag already exists
        if (lines[0].trim().startsWith("@")) {
          const existingTags = lines[0].trim();
          if (existingTags.includes(tagToAdd)) {
            // if tag already exists in this file - skip it
            filesAlreadyHaveTag.push(testCase.name);
          } else {
            // if it has other tags but not new tag - update it
            filesToUpdate.push(testCase);
          }
        } else {
          // if no tags at all - update it
          filesToUpdate.push(testCase);
        }
      } catch (fileError) {
        console.error(
          `Failed to read feature file ${testCase.name}.feature:`,
          fileError
        );
        return NextResponse.json(
          {
            error: `Failed to read feature file: ${testCase.name}.feature`,
          },
          { status: 500 }
        );
      }
    }

    // STEP 2: it update only the files that don't have the tag
    let updatedCount = 0;
    for (const testCase of filesToUpdate) {
      const featureFilePath = `${RECORDINGS_PATH}/${testCase.name}.feature`;

      try {
        // reads existing feature file content
        const content = await fs.readFile(featureFilePath, "utf-8");
        const lines = content.split("\n");

        let updatedContent;

        // checks if first line already has tags (starts with @)
        if (lines[0].trim().startsWith("@")) {
          // appends new tag to existing tags on first line
          lines[0] = `${lines[0].trim()} ${tagToAdd}`;
          updatedContent = lines.join("\n");
        } else {
          // if no existing tags, add new tag as first line
          updatedContent = `${tagToAdd}\n${content}`;
        }

        // writes updated content back to file
        await fs.writeFile(featureFilePath, updatedContent, "utf-8");
        updatedCount++;

        console.log(
          `Successfully updated feature file: ${testCase.name}.feature`
        );
      } catch (fileError) {
        console.error(
          `Failed to update feature file ${testCase.name}.feature:`,
          fileError
        );
        // continues with other files even if one fails
      }
    }

    // STEP 3: prepares response message
    let message;

    if (updatedCount === 0 && filesAlreadyHaveTag.length > 0) {
      // Scenario 3: if no files updated - all already had the tag
      message = `${
        filesAlreadyHaveTag.length
      } files already have the Name: ${filesAlreadyHaveTag.join(", ")}.`;
    } else {
      // Scenario 1 & 2: Some or all files were updated
      message = `Test flow '${testFlowName}' created successfully!`;
      if (updatedCount > 0) {
        message += ` Updated ${updatedCount} file.`;
      }
      if (filesAlreadyHaveTag.length > 0) {
        message += ` ${
          filesAlreadyHaveTag.length
        } files already had the tag: ${filesAlreadyHaveTag.join(", ")}.`;
      }
    }

    return NextResponse.json({
      success: true,
      message: message,
      updatedFiles: updatedCount,
      skippedFiles: filesAlreadyHaveTag.length,
      skippedFileNames: filesAlreadyHaveTag,
    });
  } catch (error) {
    console.error("Error creating test flow:", error);
    return NextResponse.json(
      { error: "Failed to create test flow" },
      { status: 500 }
    );
  }
}

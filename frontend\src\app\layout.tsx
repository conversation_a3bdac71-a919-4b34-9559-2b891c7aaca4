import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { ToasterProvider } from "@/components/toaster-provider";

export const metadata: Metadata = {
  title: "Abjayon No-Code Test Platform",
  description: "Abjayon No-Code Test Platform",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body>
        <main>{children}</main>
        <ToasterProvider />
      </body>
    </html>
  );
}
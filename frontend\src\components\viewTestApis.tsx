"use client";
import { useEffect, useState } from "react";
import { AppSidebar } from "@/components/dashboard-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { ProjectManagementContent } from "@/components/projectmanagement";
import { setModuleNamesGlobal } from "@/services/testApi";

export default function ViewTestApis() {
  const [selectedSection, setSelectedSection] = useState("");
  const [moduleNames, setModuleNames] = useState([]);

  useEffect(()=>{
    fetchData();
  },[])

  const fetchData = async (selectionModule?: string) => {
    const res = await getAllModuleNames();
    const files = res.files;
    setModuleNames(files);
    setModuleNamesGlobal(files);
    setSelectedSection(selectionModule || files[0]);
  }; 

  const onModuleChange= async (moduleName?: string) => {
    fetchData(moduleName);
  }

  
  return (
    <SidebarProvider
      style={{ "--sidebar-width": "45px" } as React.CSSProperties}
    >
      <AppSidebar />
      
      <SidebarInset>
        <div className="flex h-screen overflow-hidden">
          {/* Sidebar - fixed and non-scrollable */}
          <aside className="w-[15%] border-r bg-muted p-2 space-y-1 bg-[#F7F9FC] font-normal tracking-normal font-roboto">
            <ul className="text-[#37393A]  flex flex-col gap-2 rounded-xs">
              <div className="bg-[#15537C] text-white px-2 py-1 flex items-center space-x-1 hover:brightness-110 rounded-xs justify-start">
              <span className="font-bold text-sm ml-1 truncate whitespace-nowrap overflow-hidden max-w-[160px]">
                List of Modules
              </span>
            </div>
                {moduleNames.map((module)=>(
                  <li className="text-left w-full text-sm hover:bg-[#B1DBEA]" key={module}>
                  <button
                  className={`w-full px-2 py-1 text-left ${
                    selectedSection === module
                      ? "bg-[#B1DBEA] text-[#15537C] font-medium" 
                      : ""
                  }`}
                  onClick={() => setSelectedSection(module)}
                >
                  {module}
                </button>
                </li>
                ))}
            </ul>
          </aside>

          <div className="flex-1 flex flex-col overflow-hidden">
            <div className="flex-1 overflow-auto p-4">
              <ProjectManagementContent
                section='APIs'
                name={selectedSection}
                onModuleChange={onModuleChange}
              />
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}

async function getAllModuleNames() {
  const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/testapi`);
  return await response.json();
}
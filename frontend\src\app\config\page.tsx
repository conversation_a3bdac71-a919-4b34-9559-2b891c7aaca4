"use client";

import { AppSidebar } from "@/components/dashboard-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import * as React from "react";
import { ConfigManagement } from "@/components/configmanagement";

type User = {
  role: string;
  assignee: string;
};

type Project = {
  id: number;
  title: string;
  users: User[];
  tenant_id?: number;
  name?: string;
  description?: string;
  start_date?: string | null;
  end_date?: string | null;
  is_enabled?: number;
  created_at?: string;
};

export default function Page(){
   
    return(
        <SidebarProvider 
         style={
            {
            "--sidebar-width": "45px",
            } as React.CSSProperties
        }
        >
            <AppSidebar />
            <SidebarInset>
                <ConfigManagement />
                {/* Add dynamic configs here */}
                
            </SidebarInset>
        </SidebarProvider>
    )
}
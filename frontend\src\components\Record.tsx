'use client';
import { useState, useEffect } from 'react';
import { Play } from 'lucide-react';
import axios from 'axios';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

interface RecordProps {
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  testCaseName?: string;
  featureContent?: string; // ✅ Add this new prop
  isElementExtractor?: boolean; // ✅ Add this to distinguish between test cases and element extractors
}

const Record: React.FC<RecordProps> = ({ 
  isOpen: externalIsOpen, 
  onOpenChange: externalOnOpenChange,
  testCaseName,
  featureContent, // ✅ Add this parameter
  isElementExtractor = false // ✅ Add this parameter with default value
}) => {
  const [internalIsOpen, setInternalIsOpen] = useState(false);
  
  const isOpen = externalIsOpen !== undefined ? externalIsOpen : internalIsOpen;
  const setIsOpen = externalOnOpenChange || setInternalIsOpen;

  // State for dynamic content
  const [scenarioDetails, setScenarioDetails] = useState('');
  const [loading, setLoading] = useState(false);

  // ✅ UPDATED: Handle both passed content and fetched content
  useEffect(() => {
    if (isOpen) {
      if (featureContent) {
        // If content is passed directly (for Element Extractors), use it
        setScenarioDetails(featureContent);
        setLoading(false);
      } else if (testCaseName) {
        // If no content passed but testCaseName exists (for Test Cases), fetch it
        fetchFeatureContent(testCaseName, isElementExtractor);
      } else {
        setScenarioDetails('No content available');
        setLoading(false);
      }
    }
  }, [isOpen, testCaseName, featureContent, isElementExtractor]);

  // ✅ UPDATED: Function to fetch feature content from appropriate API
  const fetchFeatureContent = async (fileName: string, isExtractor: boolean) => {
    setLoading(true);
try {
  // Choose the appropriate API endpoint based on content type
  const apiUrl = isExtractor 
    ? `/api/element-extractor?type=feature&feature=${fileName}`
    : `/api/test-cases?type=feature&feature=${fileName}`;

  const response = await axios.get(apiUrl);
  const data = response.data;

  setScenarioDetails(data.content);
} catch (error) {
  console.error('Error fetching feature content:', error);
  setScenarioDetails('Error loading feature file content.');
} finally {
  setLoading(false);
}
  };

  const handlePlayBack = () => {
    console.log('Play Back clicked');
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      {externalIsOpen === undefined && (
        <DialogTrigger asChild>
          <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
            Recorded
          </button>
        </DialogTrigger>
      )}
      
      <DialogContent className="w-[1262px] h-[565px] max-w-none p-0 overflow-hidden">
        <div className="flex flex-col h-full">
          {/* Custom Header with dark background */}
          <DialogHeader className="px-6 h-[53px] bg-[#5F6161] text-white flex flex-row items-center justify-between space-y-0">
            <DialogTitle className="text-sm font-medium">
              {/* ✅ UPDATED: Dynamic title based on content type */}
              {isElementExtractor ? "Element Extractor" : "Recorded Test Case"}
            </DialogTitle>
          </DialogHeader>

          {/* Main content area with padding */}
          <div className="flex flex-col justify-between flex-grow px-8 py-6">
            {/* Test Case Info Display */}
            <div className="w-full text-sm text-gray-800 space-y-4">
              <div className="flex items-center gap-2">
                <span className="font-medium text-gray-700 whitespace-nowrap">
                  {/* ✅ UPDATED: Dynamic label based on content type */}
                  {isElementExtractor ? "Element Extractor Name:" : "Test Case Name:"}
                </span>
                <div className="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-900">
                  {testCaseName}
                </div>
              </div>

              <div className="w-full h-[329px]">
                <textarea
                  className="w-full h-full font-mono text-sm bg-gray-100 border border-gray-300 rounded-md p-4 resize-none overflow-auto"
                  readOnly
                  value={loading ? 'Loading feature content...' : scenarioDetails}
                />
              </div>
            </div>

            {/* Buttons */}
            <div className="flex justify-between">
              <button
                onClick={() => setIsOpen(false)}
                className="flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200 text-gray-700 font-medium shadow-sm"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default Record;

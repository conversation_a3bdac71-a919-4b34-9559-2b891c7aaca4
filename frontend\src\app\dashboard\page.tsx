"use client";

import { useState, useEffect } from "react";
import { AppSidebar } from "@/components/dashboard-sidebar";
// import CreateNewProject from "@/components/new-project";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";

import axios from 'axios';
export default function Page() {
  const [reportError, setReportError] = useState(false);

  // Check if report is available
  useEffect(() => {
    const checkReport = async () => {


try {
  const response = await axios.get('/api/report'); // Relative path, no baseURL
  // No need to check response.ok — Axios throws if status is not 2xx
} catch (error) {
  setReportError(true); // Called on error or non-2xx response
}

    };

    checkReport();
  }, []);

  const renderReportContent = () => {
    if (reportError) {
      return (
        <div className="p-8 text-center text-gray-500">
          <div className="text-gray-400 mb-4">
            <svg
              className="w-16 h-16 mx-auto mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            <p className="text-lg font-medium text-gray-600">
              No test report available
            </p>
            <p className="text-sm text-gray-500">
              Run your tests to generate a report, or check your configuration
            </p>
          </div>
        </div>
      );
    }

    return (
      <iframe
        src="/api/report"
        className="w-full h-[calc(100vh)] border-none"
      />
    );
  };

  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "45px",
        } as React.CSSProperties
      }
    >
      <AppSidebar />
      <SidebarInset>
        {renderReportContent()}
      </SidebarInset>

    
    </SidebarProvider>
  );
}
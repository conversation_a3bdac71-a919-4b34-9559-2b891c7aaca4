import type { Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { startRecordingSchema, stopRecordingSchema, playbackSchema } from "@shared/schema";
import { RecorderService } from "./services/recorder.service";
import { GeneratorService } from "./services/generator.service";
import { ZodError } from "zod";
import { fromZodError } from "zod-validation-error";
import path from "path";
import fs from "fs";
import runPlayback from "./services/playbackfromfile";  

// ONLY NEW IMPORT - Import the new route functions
import { 
  setCurrentProjectPathRoute, 
  getCurrentProjectPathRoute, 
  getCurrentProjectPath 
} from "./setCurrentProjectPath/route";

const recorder = new RecorderService();

const generator = new GeneratorService();

// Use dynamic path instead of process.cwd()
function getRecordingsDir(): string {
  const recordingsDir = path.join(getCurrentProjectPath(), "src\\test\\resources\\features");
  if (!fs.existsSync(recordingsDir)) {
    fs.mkdirSync(recordingsDir, { recursive: true });
  }
  return recordingsDir;
}

export async function registerRoutes(app: Express): Promise<Server> {
  //  ONLY NEW ROUTES - Add the new project path routes
  app.post('/setCurrentProjectPath', setCurrentProjectPathRoute);
  app.get('/getCurrentProjectPath', getCurrentProjectPathRoute);

  // Validate request body middleware
  const validate = (schema: any) => (req: Request, res: Response, next: Function) => {
    try {
       console.log("Validate request body:", req.body);
      req.body = schema.parse(req.body);
     
      next();
    } catch (err) {
      if (err instanceof ZodError) {
        const validationError = fromZodError(err);
        res.status(400).json({ error: validationError.message });
      } else {
        res.status(400).json({ error: "Invalid request data" });
      }
    }
  };

  // Get all recordings
  // app.get("/api/recordings", async (req: Request, res: Response) => {
  //   try {
  //     const recordings = await storage.getRecordings();
  //     res.json(recordings);
  //   } catch (error) {
  //     res.status(500).json({ error: "Failed to fetch recordings" });
  //   }
  // });



app.post('/api/playback-file', async (req, res) => {
  const { browser, jsonFile } = req.body;

  if (!browser || !jsonFile) {
    return res.status(400).json({ error: 'browser and jsonFile are required' });
  }

  try {
    await runPlayback(browser, jsonFile);
    res.status(200).json({ message: 'Playback completed successfully' });
  } catch (err) {
    console.error('Playback error:', err);
    res.status(500).json({ error: 'Playback failed', details: err.message });
  }
});



  // Get a single recording
  // app.get("/api/recordings/:id", async (req: Request, res: Response) => {
  //   try {
  //     const id = parseInt(req.params.id);
  //     const recording = await storage.getRecording(id);
      
  //     if (!recording) {
  //       return res.status(404).json({ error: "Recording not found" });
  //     }
      
  //     res.json(recording);
  //   } catch (error) {
  //     res.status(500).json({ error: "Failed to fetch recording" });
  //   }
  // });

  // Start recording
  app.post("/api/start-recording", validate(startRecordingSchema), async (req: Request, res: Response) => {
    try {
      const { name, url, browser, scenario } = req.body;
      
      console.log("Starting recording with data:", { name, url, browser, scenario });
      // Create a new recording in storage
      const recording = await storage.createRecording({ name, url, browser, scenario });
      
      // Start the browser session
      await recorder.startRecording(recording.id, url, browser);
      
      res.json({
        sessionId: recording.id,
        status: "recording",
        startTime: recording.createdAt
      });
    } catch (error) {
      console.error("Failed to start recording:", error);
      res.status(500).json({ error: "Failed to start recording" });
    }
  });


  // Stop recording
  app.post("/api/stop-recording", validate(stopRecordingSchema), async (req: Request, res: Response) => {
    try {
      const { recordingId } = req.body;
      const recording = await storage.getRecording(recordingId);
      
      if (!recording) {
        return res.status(404).json({ error: "Recording not found" });
      }
      
      // Stop the recording session
      await recorder.stopRecording(recordingId);
      
      //  UPDATED: Use dynamic recordings directory
      const recordingsDir = getRecordingsDir();
      
      // Generate JSON and feature files
      const jsonFileName = `${recording.name.toLowerCase().replace(/\s+/g, '_')}.json`;
      const featureFileName = `${recording.name.toLowerCase().replace(/\s+/g, '_')}.feature`;
      
      const jsonPath = path.join(recordingsDir, jsonFileName);
      const featurePath = path.join(recordingsDir, featureFileName);
      
      // Generate the files
      await generator.generateJsonFile(recording, jsonPath);
      await generator.generateFeatureFile(recording, featurePath);
      
      // Update recording with file paths
      const updatedRecording = await storage.setRecordingFiles(
        recordingId, 
        `/recordings/${jsonFileName}`,
        `/recordings/${featureFileName}`
      );
      
      // Update recording status
      await storage.updateRecordingStatus(recordingId, "completed");
      
      // Calculate total recording time in seconds
      const startTime = new Date(recording.createdAt).getTime();
      const endTime = new Date().getTime();
      const recordingTime = Math.round((endTime - startTime) / 1000);
      
      res.json({
        sessionId: recordingId,
        status: "completed",
        recordingTime,
        stepsCount: recording.steps,
        files: {
          json: `/recordings/${jsonFileName}`,
          feature: `/recordings/${featureFileName}`
        }
      });
    } catch (error) {
      console.error("Failed to stop recording:", error);
      res.status(500).json({ error: "Failed to stop recording" });
    }
  });

  // Playback recording
  // app.post("/api/playback", validate(playbackSchema), async (req: Request, res: Response) => {
  //   try {
  //     const { recordingId, browser, headless } = req.body;
  //     const recording = await storage.getRecording(recordingId);
      
  //     if (!recording) {
  //       return res.status(404).json({ error: "Recording not found" });
  //     }
      
  //     // Start the playback in a separate process to not block the response
  //     const playbackId = `play${Date.now()}`;
      
  //     // Begin playback in the background
  //     playback.startPlayback(playbackId, recording, browser, headless);
      
  //     res.json({
  //       playbackId,
  //       status: "started"
  //     });
  //   } catch (error) {
  //     console.error("Failed to start playback:", error);
  //     res.status(500).json({ error: "Failed to start playback" });
  //   }
  // });

  // // Serve the recordings directory for downloading JSON and feature files
  // app.use('/recordings', (req, res, next) => {
  //   const filePath = path.join(recordingsDir, req.path);
    
  //   // Basic security check to prevent directory traversal
  //   if (!filePath.startsWith(recordingsDir)) {
  //     return res.status(403).json({ error: "Access denied" });
  //   }
    
  //   // Check if file exists
  //   if (fs.existsSync(filePath) && fs.statSync(filePath).isFile()) {
  //     return res.sendFile(filePath);
  //   }
    
  //   next();
  // });

  const httpServer = createServer(app);

  return httpServer;
}
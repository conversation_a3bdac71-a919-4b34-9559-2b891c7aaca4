import { promises as fs } from "fs";
import { NextResponse } from "next/server";
import config from "@/config/config.json";

// Get current project from config
const getCurrentProject = () => {
  return config.projectName || "defaultProject";
};

// Dynamic path for element extractor
const getElementExtractorPath = () => {
  const currentProject = getCurrentProject();
  return `D:/TargetCode/${currentProject}/webElementExtractor/src/test/resources/features`;
};

// Parse individual Gherkin step for element extractors
function parseExtractorGherkinStep(gherkinLine: string): any | null {
  const trimmedLine = gherkinLine.trim();

  // Skip setup steps and empty lines
  if (!trimmedLine || trimmedLine.startsWith("#") || trimmedLine.startsWith("@")) {
    return null;
  }

  // Parse element extractor specific steps
  // Given I capture web elements from the current page
  if (trimmedLine.includes("I capture web elements from the current page")) {
    return { action: "capture", description: "Capture web elements from current page" };
  }

  // And I generate properties file from captured elements  
  if (trimmedLine.includes("I generate properties file from captured elements")) {
    return { action: "generate", description: "Generate properties file from captured elements" };
  }

  // Given I navigate to the page
  const navigateMatch = trimmedLine.match(/I navigate to the page "([^"]*)"/);
  if (navigateMatch) {
    return { action: "navigate", url: navigateMatch[1], description: `Navigate to ${navigateMatch}` };
  }

  // When I capture current page web elements
  if (trimmedLine.includes("I capture current page web elements")) {
    return { action: "capture_page", description: "Capture current page web elements" };
  }

  // Then I generate properties file as
  const generateAsMatch = trimmedLine.match(/I generate properties file as "([^"]*)"/);
  if (generateAsMatch) {
    return { action: "generate_as", fileName: generateAsMatch[1], description: `Generate properties file as ${generateAsMatch}` };
  }

  // Then I copy UI element properties files from extractor to modules
  if (trimmedLine.includes("I copy UI element properties files from extractor to modules")) {
    return { action: "copy_files", description: "Copy UI element properties files from extractor to modules" };
  }

  // Given I have element properties files available
  if (trimmedLine.includes("I have element properties files available")) {
    return { action: "check_files", description: "Check element properties files available" };
  }

  // When I run autoSuggestions utils
  if (trimmedLine.includes("I run autoSuggestions utils")) {
    return { action: "run_utils", description: "Run autoSuggestions utils" };
  }

  // Then I restart IntelliJ to apply changes
  if (trimmedLine.includes("I restart IntelliJ to apply changes")) {
    return { action: "restart_ide", description: "Restart IntelliJ to apply changes" };
  }

  // Generic step parsing for other Gherkin steps
  if (trimmedLine.startsWith("Given") || trimmedLine.startsWith("When") || 
      trimmedLine.startsWith("Then") || trimmedLine.startsWith("And")) {
    return { action: "generic", description: trimmedLine };
  }

  return null;
}

// Parse entire feature file content to raw step array
function parseExtractorFeatureToSteps(featureContent: string): any[] {
  const lines = featureContent.split("\n");
  const steps: any[] = [];

  for (const line of lines) {
    const parsedStep = parseExtractorGherkinStep(line);
    if (parsedStep) {
      steps.push(parsedStep);
    }
  }

  return steps;
}

// ✅ UPDATED GET - Handle both listing and feature content requests
export async function GET(request: Request) {
  try {
    // ✅ ADD: Parse URL search parameters
    const url = new URL(request.url);
    const type = url.searchParams.get('type');
    const feature = url.searchParams.get('feature');

    // ✅ ADD: Handle feature content request
    if (type === 'feature' && feature) {
      console.log('🔍 Fetching feature content for:', feature);
      
      const elementExtractorPath = getElementExtractorPath();
      const featureFilePath = `${elementExtractorPath}/${feature}.feature`;
      
      console.log('📁 Reading file from:', featureFilePath);
      
      try {
        const featureContent = await fs.readFile(featureFilePath, "utf-8");
        console.log('✅ File read successfully, length:', featureContent.length);
        
        return NextResponse.json({ 
          success: true,
          content: featureContent 
        });
      } catch (fileError: unknown) { // ✅ FIX: Type the error
        console.error('❌ File read error:', fileError);
        
        // ✅ FIX: Safe error message access
        const errorMessage = fileError instanceof Error ? fileError.message : 'Unknown error';
        
        return NextResponse.json({ 
          success: false,
          content: "Feature file not found or could not be read.",
          error: errorMessage 
        }, { status: 404 });
      }
    }

    // ✅ EXISTING: List existing element extractors with parsed content
    const elementExtractorPath = getElementExtractorPath();
    
    // Check if directory exists
    try {
      await fs.access(elementExtractorPath);
    } catch {
      // Directory doesn't exist, return empty array
      return NextResponse.json([]);
    }

    const fileNames = await fs.readdir(elementExtractorPath);
    const featureFiles = fileNames.filter((file) => file.endsWith(".feature"));

    // Parse content like test cases do
    const elementExtractors = await Promise.all(
      featureFiles.map(async (fileName, index) => {
        const filePath = `${elementExtractorPath}/${fileName}`;
        let parsedData = null;
        let gherkinContent = "";

        try {
          gherkinContent = await fs.readFile(filePath, "utf-8");
          parsedData = parseExtractorFeatureToSteps(gherkinContent);
        } catch (err) {
          console.error(`Failed to parse extractor file ${fileName}:`, err);
        }

        return {
          id: index + 1,
          name: fileName.replace(".feature", ""),
          filePath,
          gherkinContent, // Add full content
          data: parsedData, // Add parsed steps like test cases
        };
      })
    );

    return NextResponse.json(elementExtractors);
    
  } catch (error: unknown) { // ✅ FIX: Type the outer error
    if (error instanceof Error) {
      console.error("Error reading element extractors directory:", error.message);
    } else {
      console.error("Unknown error reading element extractors directory:", error);
    }
    return NextResponse.json(
      { error: "Failed to fetch element extractors" },
      { status: 500 }
    );
  }
}

// POST - Create/Save new element extractor
export async function POST(request: Request) {
  try {
    const { fileName, gherkinContent } = await request.json();

    // Validate input
    if (!fileName || !fileName.trim()) {
      return NextResponse.json(
        { error: "File name is required" },
        { status: 400 }
      );
    }

    if (!gherkinContent || !gherkinContent.trim()) {
      return NextResponse.json(
        { error: "Gherkin content is required" },
        { status: 400 }
      );
    }

    const elementExtractorPath = getElementExtractorPath();
    const featureFilePath = `${elementExtractorPath}/${fileName}.feature`;

    try {
      // Ensure directory exists
      await fs.mkdir(elementExtractorPath, { recursive: true });
      
      // Write the feature file
      await fs.writeFile(featureFilePath, gherkinContent, "utf-8");

      console.log(`Successfully created element extractor: ${fileName}.feature`);

      return NextResponse.json({
        success: true,
        message: `Element extractor '${fileName}' created successfully`,
        filePath: featureFilePath,
      });

    } catch (fileError: unknown) { // ✅ FIX: Type the error
      if (fileError instanceof Error) {
        console.error(`Failed to create element extractor file:`, fileError.message);
      } else {
        console.error(`Failed to create element extractor file:`, fileError);
      }
      return NextResponse.json(
        { error: "Failed to create element extractor file" },
        { status: 500 }
      );
    }

  } catch (error: unknown) { // ✅ FIX: Type the outer error
    if (error instanceof Error) {
      console.error("Error saving element extractor:", error.message);
    } else {
      console.error("Error saving element extractor:", error);
    }
    return NextResponse.json(
      { error: "Failed to save element extractor" },
      { status: 500 }
    );
  }
}

// DELETE - Delete element extractor
export async function DELETE(request: Request) {
  try {
    const { fileName } = await request.json();
    
    if (!fileName || !fileName.trim()) {
      return NextResponse.json(
        { error: "File name is required" },
        { status: 400 }
      );
    }

    const elementExtractorPath = getElementExtractorPath();
    const featureFilePath = `${elementExtractorPath}/${fileName}.feature`;
    
    try {
      // Check if file exists first
      await fs.access(featureFilePath);
      
      // Delete the file
      await fs.unlink(featureFilePath);
      console.log(`Successfully deleted element extractor: ${fileName}.feature`);
      
      return NextResponse.json({ 
        success: true, 
        message: `Element extractor '${fileName}' deleted successfully` 
      });
    } catch (fileError: unknown) {
      console.error(`Failed to delete element extractor file:`, fileError);
      
      if (fileError instanceof Error) {
        // Handle specific error cases
        if ('code' in fileError && fileError.code === 'ENOENT') {
          return NextResponse.json(
            { error: `File '${fileName}.feature' not found` },
            { status: 404 }
          );
        } else if ('code' in fileError && fileError.code === 'EACCES') {
          return NextResponse.json(
            { error: "Permission denied - cannot delete file" },
            { status: 403 }
          );
        } else {
          return NextResponse.json(
            { error: "Failed to delete element extractor file" },
            { status: 500 }
          );
        }
      } else {
        return NextResponse.json(
          { error: "Unknown error occurred while deleting file" },
          { status: 500 }
        );
      }
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error("Error deleting element extractor:", error.message);
    } else {
      console.error("Unknown error deleting element extractor:", error);
    }
    return NextResponse.json(
      { error: "Failed to delete element extractor" },
      { status: 500 }
    );
  }
}

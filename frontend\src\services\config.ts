  export const fetchConfigs = async () => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/config/read`
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch configs: ${response.status}`);
      }

      const configData = await response.json();
      return configData.data
    } catch (error: unknown) {
      console.error("Error fetching configs:", error);
      // setUsersFetchError(error.message || "Failed to load users");
    } 
  };
"use client";

import { getNewStep, Step } from "@/data/getInitialSteps";
import axios from 'axios';
import { playFeatureFile } from "@/app/api/recorder/record";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { useEffect, useState } from "react";
import { Lock, Unlock, ChevronDown, ChevronUp } from "lucide-react"; // Import lock icons and chevrons
import Record from "@/components/Record";
import { TestApi } from "./testapis";
import ViewAPIDialog from "./viewAPIDialog";
import { normalizeBody } from "@/services/testApi";

// Import interfaces from elements component
import { UIElement, ElementGroup } from "@/components/elements";

// Interface for flattened element options
interface ElementOption {
  name: string;
  fullKey: string;
  value: string;
  type: string;
  groupName: string;
  moduleName: string; // NEW: Add moduleName field
}

// Fetch and flatten elements from API
async function fetchElementOptions(): Promise<ElementOption[]> {


try {
  const response = await axios.get<ElementGroup[]>('/api/elements');
  const elementGroups = response.data;

  const options: ElementOption[] = [];

  elementGroups.forEach((group) => {
    group.elements.forEach((element) => {
      options.push({
        name: element.name,
        fullKey: element.fullKey,
        value: element.value,
        type: element.type,
        groupName: group.name,
        moduleName: element.moduleName,
      });
    });
  });

  return options;
} catch (error) {
  console.error("Error fetching element options:", error);
  return [];
}

}

// Convert raw feature file step format to Step type
function convertRawSteps(rawSteps: any[]): Step[] {
  return rawSteps.map((raw, index) => ({
    step: index + 1,
    type: raw.type || "",
    action: raw.action || "",
    element:
      raw.action === "type"
        ? raw.label || ""
        : raw.selector || raw.label || raw.url || "",
    data: raw.action === "type" ? raw.value || "" : raw.value || "",
    screenshot: "",
    url: raw.url || "",
    elementModule: "", // NEW: Add elementModule field for tracking
  }));
}

// Component for a single test case accordion
function SingleTestCase({
  caseNumber,
  caseName,
  initialSteps,
  showActionButtons = true,
  showCheckboxes = true,
  allowEditing = true,
  showCucumberButton = true,
  showPlayButton = true,
  onDeleteTestCase, 
}: {
  caseNumber: number;
  caseName?: string;
  initialSteps: Step[];
  showActionButtons?: boolean;
  showCheckboxes?: boolean;
  allowEditing?: boolean;
  showCucumberButton?: boolean;
  showPlayButton?: boolean;
  onDeleteTestCase?: (testCaseId: number, testCaseName: string) => Promise<void>; 
}) {
  const INITIAL_ROW_COUNT = 3;
  const [steps, setSteps] = useState<Step[]>(initialSteps);
  const [selectedSteps, setSelectedSteps] = useState(
    Array(INITIAL_ROW_COUNT).fill(false)
  );
  const [selectAll, setSelectAll] = useState(false);
  const [editingCell, setEditingCell] = useState<{
    row: number | null;
    field: string | null;
  }>({ row: null, field: null });

  // Add element options state
  const [elementOptions, setElementOptions] = useState<ElementOption[]>([]);
  const [isLoadingElements, setIsLoadingElements] = useState(true);

  // Tracks the original steps count to identify new rows
  const [originalStepsCount, setOriginalStepsCount] = useState(
    initialSteps.length
  );
  const [isSaving, setIsSaving] = useState(false);

  // Add accordion open state to track chevron direction
  const [isAccordionOpen, setIsAccordionOpen] = useState(false);

  // Add lock state for this test case
  const [isLocked, setIsLocked] = useState(false);

  // Smart change detection state
  const [originalSteps, setOriginalSteps] = useState<Step[]>(initialSteps);
  const [changedStepIndices, setChangedStepIndices] = useState<Set<number>>(
    new Set()
  );
  const [deletedStepIndices, setDeletedStepIndices] = useState<Set<number>>(
    new Set()
  );

  // NEW: Add playing state for play-once functionality
  const [isPlaying, setIsPlaying] = useState(false);

  // Fetch element options on component mount
  useEffect(() => {
    const loadElementOptions = async () => {
      setIsLoadingElements(true);
      const options = await fetchElementOptions();
      setElementOptions(options);
      setIsLoadingElements(false);
    };

    loadElementOptions();
  }, []);

  // Element Dropdown Component
  const ElementDropdown = ({
    currentValue,
    onSelect,
    onClose,
  }: {
    currentValue: string;
    onSelect: (name: string, value: string, moduleName: string) => void; // NEW: Add moduleName parameter
    onClose: () => void;
  }) => {
    return (
      <DropdownMenu
        open={true}
        onOpenChange={(open) => {
          if (!open) onClose();
        }}
      >
        <DropdownMenuTrigger className="w-full text-left px-2 py-1 border border-gray-300 rounded bg-white">
          {currentValue || "Select element"}
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-[300px] max-h-[200px] overflow-y-auto">
          {isLoadingElements ? (
            <div className="px-2 py-1 text-sm text-gray-500">
              Loading elements...
            </div>
          ) : elementOptions.length === 0 ? (
            <div className="px-2 py-1 text-sm text-gray-500">
              No elements found
            </div>
          ) : (
            elementOptions.map((option, idx) => (
              <DropdownMenuItem
                key={`${option.groupName}-${option.fullKey}-${idx}`}
                onClick={() =>
                  onSelect(option.fullKey, option.value, option.moduleName)
                } // NEW: Pass moduleName
                className="flex flex-col items-start"
              >
                <div className="flex items-center justify-between w-full">
                  <span className="font-medium">{option.fullKey}</span>
                  <span className="text-xs text-gray-500 bg-gray-100 px-1 rounded">
                    {option.groupName}
                  </span>
                </div>
              </DropdownMenuItem>
            ))
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  };

  const toggleSelectAll = () => {
    const newVal = !selectAll;
    setSelectAll(newVal);
    setSelectedSteps(Array(steps.length).fill(newVal));
  };

  const toggleStep = (index: number) => {
    const updated = [...selectedSteps];
    updated[index] = !updated[index];
    setSelectedSteps(updated);
    setSelectAll(updated.every((v) => v));
  };

  const handleAddRow = () => {
    if (isLocked) return; // prevents adding rows when locked
    const newStep = getNewStep(steps.length + 1);
    setSteps([...steps, newStep]);
    setSelectedSteps([...selectedSteps, false]);
  };

  // handleDeleteSelected with select-all detection
  const handleDeleteSelected = async () => {
    if (isLocked) return; // prevents deleting when locked

    // Check if all steps are selected (select all scenario)
    const allStepsSelected = selectedSteps.length === steps.length && 
                            selectedSteps.every(selected => selected === true);

    if (allStepsSelected && onDeleteTestCase) {
      // User wants to delete entire test case
      const confirmDelete = window.confirm(
        `Are you sure you want to delete the entire test case "${caseName}"?\n\nThis will check for dependencies in Test Flows and Test Suites.`
      );
      
      if (confirmDelete) {
        try {
          await onDeleteTestCase(caseNumber, caseName || `TestCase${caseNumber}`);
          // Note: The parent component will handle UI updates after successful deletion
        } catch (error) {
          console.error('Error deleting test case:', error);
          alert('Failed to delete test case. Please try again.');
        }
      }
      return;
    }

    // logic for deleting individual steps
    // Track deleted step indices for feature file updates
    selectedSteps.forEach((isSelected, index) => {
      if (isSelected) {
        // Track all deletions, not just original steps
        if (index < originalStepsCount) {
          // This is an original step that was already in the feature file
          setDeletedStepIndices((prev) => new Set([...prev, index]));
        }
        // For steps beyond originalStepsCount, we don't need to track deletion
        // because they might not have been saved to feature file yet
      }
    });

    const filteredSteps = steps.filter((_, index) => !selectedSteps[index]);
    const renumbered = filteredSteps.map((step, i) => ({
      ...step,
      step: i + 1,
    }));
    const updatedSelection = selectedSteps.filter((selected) => !selected);
    setSteps(renumbered);
    setSelectedSteps(updatedSelection);
    setSelectAll(false);
  };

  const handleCellClick = (rowIndex: number, field: string) => {
    if (isLocked) return; // prevents editing when locked
    setEditingCell({ row: rowIndex, field });
  };

  const handleCellChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    rowIndex: number,
    field: keyof Step
  ) => {
    if (isLocked) return; // prevents changes when locked
    const updatedSteps = [...steps];
    updatedSteps[rowIndex] = {
      ...updatedSteps[rowIndex],
      [field]: e.target.value,
    };
    setSteps(updatedSteps);

    // Track changes for existing steps (smart change detection)
    if (rowIndex < originalSteps.length) {
      setChangedStepIndices((prev) => new Set([...prev, rowIndex]));
    }
  };

  const handleBlur = () => {
    setEditingCell({ row: null, field: null });
  };

  // UPDATED: Enhanced handlePlayClick with play-once functionality
  const handlePlayClick = async (fileName: string) => {
    // Prevent multiple clicks - exit early if already playing
    if (isPlaying) {
      console.log("Test case is already running, please wait...");
      return;
    }

    try {
      setIsPlaying(true); // Make image light and disable clicking
      const featureFileName = `${caseName}.feature`;
      console.log(`Playing feature file: ${featureFileName}`);
      const result = await playFeatureFile(
        featureFileName,
        "chromium",
        "antp-web"
      );
      console.log("Feature playback result:", result);
    } catch (error) {
      console.error("Feature playback failed", error);
    } finally {
      setIsPlaying(false); // Always restore normal state when done
    }
  };

  const handleSaveClick = async () => {
    if (isLocked) return; // prevents saving when locked

    if (!caseName) {
      console.error("Case name is required for saving");
      return;
    }

    // checks if there are any steps to save
    const validSteps = steps.filter(
      (step) => step.action && step.action.trim() !== ""
    );

    if (validSteps.length === 0) {
      console.log("No valid steps to save");
      return;
    }

    setIsSaving(true);

    try {
      // Smart change detection for feature file updates
      interface FeatureChange {
        type: "update" | "insert" | "delete";
        jsonIndex: number;
        featureLineIndex: number;
        newStep?: any;
      }

      const featureChanges: FeatureChange[] = [];

      // Compare current steps with what's currently saved (use originalStepsCount, not originalSteps.length)
      const currentSavedStepsCount = originalStepsCount;

      // Find modified existing steps (within the currently saved range)
      changedStepIndices.forEach((index) => {
        if (index < currentSavedStepsCount) {
          const originalStep = originalSteps[index];
          const currentStep = steps[index];

          if (
            originalStep &&
            currentStep &&
            (originalStep.action !== currentStep.action ||
              originalStep.element !== currentStep.element ||
              originalStep.data !== currentStep.data)
          ) {
            // Find the actual selector value for the element name
            const selectedElement = elementOptions.find(
              (opt) => opt.fullKey === currentStep.element
            );
            const actualSelector = selectedElement
              ? selectedElement.value
              : currentStep.element;

            // Convert current step to format for feature file
            const stepForFeature: any = {
              action: currentStep.action,
              elementModule: selectedElement?.moduleName, // NEW: Add elementModule
            };

            if (currentStep.action === "type") {
              if (actualSelector && actualSelector.trim() !== "") {
                stepForFeature.label = currentStep.element; // Keep friendly name
                stepForFeature.selector = actualSelector; // Use actual selector
              }
              if (currentStep.data && currentStep.data.trim() !== "") {
                stepForFeature.value = currentStep.data;
              }
            } else if (currentStep.action === "assertion") {
              // Add specific handling for assertion steps
              if (actualSelector && actualSelector.trim() !== "") {
                stepForFeature.label = currentStep.element;
                stepForFeature.selector = actualSelector;
              }
              if (currentStep.data && currentStep.data.trim() !== "") {
                stepForFeature.value = currentStep.data;
              }
              // Include URL field for assertion steps
              if (currentStep.url && currentStep.url.trim() !== "") {
                stepForFeature.url = currentStep.url;
              }
            } else {
              if (actualSelector && actualSelector.trim() !== "") {
                if (actualSelector.includes("https")) {
                  stepForFeature.url = actualSelector;
                } else {
                  stepForFeature.label = currentStep.element;
                  stepForFeature.selector = actualSelector;
                }
              }
              if (currentStep.data && currentStep.data.trim() !== "") {
                stepForFeature.value = currentStep.data;
              }
            }

            featureChanges.push({
              type: "update",
              jsonIndex: index,
              featureLineIndex: index, // Will be adjusted by API based on actual file structure
              newStep: stepForFeature,
            });
          }
        }
      });

      // Find new steps that need to be added to feature file
      // This includes: steps beyond currentSavedStepsCount
      const stepsToAddToFeature = steps.slice(currentSavedStepsCount);
      stepsToAddToFeature.forEach((step, idx) => {
        if (step.action && step.action.trim() !== "") {
          // Find the actual selector value for the element name
          const selectedElement = elementOptions.find(
            (opt) => opt.fullKey === step.element
          );
          const actualSelector = selectedElement
            ? selectedElement.value
            : step.element;

          // Convert new step to format for feature file
          const stepForFeature: any = {
            action: step.action,
            elementModule: selectedElement?.moduleName, // NEW: Add elementModule
          };

          if (step.action === "type") {
            if (actualSelector && actualSelector.trim() !== "") {
              stepForFeature.label = step.element;
              stepForFeature.selector = actualSelector;
            }
            if (step.data && step.data.trim() !== "") {
              stepForFeature.value = step.data;
            }
          } else {
            if (actualSelector && actualSelector.trim() !== "") {
              if (actualSelector.includes("https")) {
                stepForFeature.url = actualSelector;
              } else {
                stepForFeature.label = step.element;
                stepForFeature.selector = actualSelector;
              }
            }
            if (step.data && step.data.trim() !== "") {
              stepForFeature.value = step.data;
            }
          }

          featureChanges.push({
            type: "insert",
            jsonIndex: currentSavedStepsCount + idx,
            featureLineIndex: currentSavedStepsCount + idx, // Will be adjusted by API
            newStep: stepForFeature,
          });
        }
      });

      // Find steps that need to be removed from feature file
      // If current steps count is less than saved count, some steps were deleted
      if (steps.length < currentSavedStepsCount) {
        // Calculate how many steps were deleted from the end
        const deletedCount = currentSavedStepsCount - steps.length;

        // Add delete operations for the removed steps (from the end)
        for (let i = 0; i < deletedCount; i++) {
          const deletedIndex = steps.length + i; // Index of deleted step in original numbering
          featureChanges.push({
            type: "delete",
            jsonIndex: deletedIndex,
            featureLineIndex: deletedIndex + 3,
          });
        }
      }

      // Also handle explicit deletions tracked by deletedStepIndices
      deletedStepIndices.forEach((index) => {
        // Only add if not already handled by the count-based deletion above
        if (index < steps.length) {
          featureChanges.push({
            type: "delete",
            jsonIndex: index,
            featureLineIndex: index, // Will be adjusted by API
          });
        }
      });

      // Check if we have any new steps (beyond original count) for updating feature file (backward compatibility)
      const newStepsForCompatibility = steps.slice(originalStepsCount);
      const validNewSteps = newStepsForCompatibility.filter(
        (step) => step.action && step.action.trim() !== ""
      );

      // converts new steps to format for feature file (if any) (backward compatibility)
      const newStepsForFeature = validNewSteps.map((step) => {
        // Find the actual selector value for the element name
        const selectedElement = elementOptions.find(
          (opt) => opt.fullKey === step.element
        );
        const actualSelector = selectedElement
          ? selectedElement.value
          : step.element;

        const stepForFeature: any = {
          action: step.action,
          elementModule: selectedElement?.moduleName, // NEW: Add elementModule
        };

        if (step.action === "type") {
          if (actualSelector && actualSelector.trim() !== "") {
            stepForFeature.label = step.element;
            stepForFeature.selector = actualSelector;
          }
          if (step.data && step.data.trim() !== "") {
            stepForFeature.value = step.data;
          }
        } else if (step.action === "assertion") {
          // Add specific handling for assertion steps
          if (actualSelector && actualSelector.trim() !== "") {
            stepForFeature.label = step.element;
            stepForFeature.selector = actualSelector;
          }
          if (step.data && step.data.trim() !== "") {
            stepForFeature.value = step.data;
          }
          // Include URL field for assertion steps
          if (step.url && step.url.trim() !== "") {
            stepForFeature.url = step.url;
          }
        } else {
          // Determine if element is URL or selector/label for other actions
          if (actualSelector && actualSelector.trim() !== "") {
            if (actualSelector.includes("https")) {
              stepForFeature.url = actualSelector;
            } else {
              stepForFeature.label = step.element;
              stepForFeature.selector = actualSelector;
            }
          }
          // adding value if data exists
          if (step.data && step.data.trim() !== "") {
            stepForFeature.value = step.data;
          }
        }

        return stepForFeature;
      });

      // Sending to API to save (feature file only)


try {
  const response = await axios.post('/api/test-cases/save', {
    fileName: caseName,
    newSteps: newStepsForFeature.length > 0 ? newStepsForFeature : [],
    featureChanges: featureChanges.length > 0 ? featureChanges : undefined,
  });

  const result = response.data;
  console.log("Save successful:", result);
} catch (error) {
  console.error("Failed to save steps:", error);
  throw new Error("Failed to save steps");
}


      // Updates original steps count to include the saved steps
      setOriginalStepsCount(steps.length);

      // Reset change tracking after successful save
      setOriginalSteps([...steps]);
      setChangedStepIndices(new Set());
      setDeletedStepIndices(new Set());
    } catch (error) {
      console.error("Error saving steps:", error);
    } finally {
      setIsSaving(false);
    }
  };

  // Handle lock toggle
  const handleLockToggle = () => {
    setIsLocked(!isLocked);
    // Close any open editing cells when locking
    if (!isLocked) {
      setEditingCell({ row: null, field: null });
    }
    console.log(`Test case ${caseName} lock state: ${!isLocked}`);
  };

  const [recordDialogOpen, setRecordDialogOpen] = useState(false);

  return (
    <>
      <AccordionItem value={`test-case-${caseNumber}`}>
        <AccordionTrigger
          className="hover:no-underline p-0 [&>svg]:hidden"
          onClick={() => setIsAccordionOpen(!isAccordionOpen)}
        >
          <div
            className="flex items-center h-[45px] px-4 w-full"
            style={{
              // background: "#DEDEDE 0% 0% no-repeat padding-box",
              // borderRadius: "6px 6px 0px 0px",
              opacity: 1,
            }}
          >
            <div className="flex items-center flex-1">
              {showCheckboxes && (
                <input
                  type="checkbox"
                  checked={selectAll}
                  onChange={toggleSelectAll}
                  onClick={(e) => e.stopPropagation()}
                  className="mr-3"
                  disabled={isLocked} // Disable checkbox when locked
                />
              )}
              <span className="text-left text-base font-medium">
                {caseName || `Test Case ${caseNumber}`}
                {isLocked && (
                  <span className="ml-2 text-red-500 text-sm">(Locked)</span>
                )}
              </span>
            </div>
            <div className="flex items-center gap-3 pr-2">
              {showActionButtons && (
                <>
                  {/* Lock/Unlock Toggle Button */}
                  <a
                    onClick={(e) => {
                      e.stopPropagation();
                      handleLockToggle();
                    }}
                    className="cursor-pointer"
                    title={isLocked ? "Unlock test case" : "Lock test case"}
                  >
                    {isLocked ? (
                      <Lock className="w-4 h-4 text-red-500" />
                    ) : (
                      <Unlock className="w-4 h-4 text-gray-600" />
                    )}
                  </a>

                  <a
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSaveClick();
                    }}
                    className={`cursor-pointer ${
                      isLocked ? "opacity-50 cursor-not-allowed" : ""
                    }`}
                    title="Save new steps"
                  >
                    <img
                      src="/file.svg"
                      className="w-[16px] h-[14px] cursor-pointer"
                      style={{ opacity: isSaving || isLocked ? 0.5 : 1 }}
                    />
                  </a>

                  {/* Cucumber Button - Conditionally Rendered */}
                  {showCucumberButton && (
                    <a
                      onClick={(e) => {
                        e.stopPropagation();
                        setRecordDialogOpen(true);
                      }}
                    >
                      <img
                        src="/Cucumber.svg"
                        className="w-[16px] h-[19px] cursor-pointer"
                      />
                    </a>
                  )}

                  <a
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddRow();
                    }}
                    className={`${
                      isLocked ? "opacity-50 cursor-not-allowed" : ""
                    }`}
                  >
                    <img
                      src="/Group 23495.svg"
                      className="w-[16px] h-[14px] cursor-pointer"
                    />
                  </a>

                  {/* UPDATED: Play Button with play-once functionality */}
                  {showPlayButton && (
                    <a
                      onClick={(e) => {
                        e.stopPropagation();
                        if (isPlaying) return; // Block clicks when running
                        const fileName = caseName
                          ? caseName
                          : `${caseNumber}.json`;
                        handlePlayClick(fileName);
                      }}
                      className={`${
                        isPlaying ? "cursor-not-allowed" : "cursor-pointer"
                      }`}
                      title={
                        isPlaying ? "Test case is running..." : "Run test case"
                      }
                    >
                      <img
                        src="/play.svg"
                        className="w-[16px] h-[19px]"
                        style={{
                          opacity: isPlaying ? 0.3 : 1,
                          transition: "opacity 0.3s ease",
                        }}
                      />
                    </a>
                  )}

                  <a
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteSelected();
                    }}
                    className={`${
                      isLocked ? "opacity-50 cursor-not-allowed" : ""
                    }`}
                  >
                    <img
                      src="/Group 21846.svg"
                      className="w-[16px] h-[14px] cursor-pointer"
                    />
                  </a>
                </>
              )}
              {/* Custom Chevron Arrow */}
              {isAccordionOpen ? (
                <ChevronUp className="h-4 w-4 shrink-0 text-gray-600" />
              ) : (
                <ChevronDown className="h-4 w-4 shrink-0 text-gray-600" />
              )}
            </div>
          </div>
        </AccordionTrigger>

        <AccordionContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                {showCheckboxes && <TableHead className="w-[5%]"></TableHead>}
                <TableHead className="w-[10%]">Steps#</TableHead>
                <TableHead className="w-[15%]">Action</TableHead>
                <TableHead className="w-[20%]">Element (Alias)</TableHead>
                <TableHead className="w-[20%]">Input/Data</TableHead>
                <TableHead className="w-[15%]">Screenshot</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {steps.map((step, index) => (
                <TableRow
                  key={step.step}
                  className={
                    index % 2 === 0 ? "bg-[#F7F9FC]" : "bg-[#FFFFFF4D]"
                  }
                >
                  {showCheckboxes && (
                    <TableCell className="w-[5%]">
                      <input
                        type="checkbox"
                        checked={selectedSteps[index]}
                        onChange={() => toggleStep(index)}
                        className="accent-blue-500"
                        disabled={isLocked} // Disable checkbox when locked
                      />
                    </TableCell>
                  )}
                  <TableCell className="w-[10%]">{step.step}</TableCell>
                  {/* Action - conditional editing */}
                  <TableCell className="w-[15%]">
                    {allowEditing &&
                    !isLocked &&
                    editingCell.row === index &&
                    editingCell.field === "action" ? (
                      <DropdownMenu
                        open={true}
                        onOpenChange={(open) => {
                          if (!open) {
                            setEditingCell({ row: null, field: null });
                          }
                        }}
                      >
                        <DropdownMenuTrigger className="w-full text-left px-2 py-1 border border-gray-300 rounded bg-white">
                          {step.action || "Select action"}
                        </DropdownMenuTrigger>
                        <DropdownMenuContent className="w-[var(--radix-dropdown-menu-trigger-width)]">
                          <DropdownMenuItem
                            onClick={() => {
                              const updatedSteps = [...steps];
                              updatedSteps[index] = {
                                ...updatedSteps[index],
                                action: "navigate",
                              };
                              setSteps(updatedSteps);
                              // Track change for existing steps
                              if (index < originalSteps.length) {
                                setChangedStepIndices(
                                  (prev) => new Set([...prev, index])
                                );
                              }
                              setEditingCell({ row: null, field: null });
                            }}
                          >
                            navigate
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              const updatedSteps = [...steps];
                              updatedSteps[index] = {
                                ...updatedSteps[index],
                                action: "click",
                              };
                              setSteps(updatedSteps);
                              // Track change for existing steps
                              if (index < originalSteps.length) {
                                setChangedStepIndices(
                                  (prev) => new Set([...prev, index])
                                );
                              }
                              setEditingCell({ row: null, field: null });
                            }}
                          >
                            click
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              const updatedSteps = [...steps];
                              updatedSteps[index] = {
                                ...updatedSteps[index],
                                action: "type",
                              };
                              setSteps(updatedSteps);
                              // Track change for existing steps
                              if (index < originalSteps.length) {
                                setChangedStepIndices(
                                  (prev) => new Set([...prev, index])
                                );
                              }
                              setEditingCell({ row: null, field: null });
                            }}
                          >
                            type
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              const updatedSteps = [...steps];
                              updatedSteps[index] = {
                                ...updatedSteps[index],
                                action: "assertion",
                              };
                              setSteps(updatedSteps);
                              // Track change for existing steps
                              if (index < originalSteps.length) {
                                setChangedStepIndices(
                                  (prev) => new Set([...prev, index])
                                );
                              }
                              setEditingCell({ row: null, field: null });
                            }}
                          >
                            assertion
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    ) : (
                      <span
                        onClick={
                          allowEditing && !isLocked
                            ? () => handleCellClick(index, "action")
                            : undefined
                        }
                        className={isLocked ? "cursor-not-allowed" : ""}
                      >
                        {step.action || "—"}
                      </span>
                    )}
                  </TableCell>
                  {/* Element - conditional editing with dropdown  */}
                  <TableCell className="w-[20%]">
                    {allowEditing &&
                    !isLocked &&
                    editingCell.row === index &&
                    editingCell.field === "element" ? (
                      <ElementDropdown
                        currentValue={step.element}
                        onSelect={(fullKeyName, value, moduleName) => {
                          // NEW: Add moduleName parameter
                          // Changed parameter name for clarity
                          const updatedSteps = [...steps];
                          updatedSteps[index] = {
                            ...updatedSteps[index],
                            element: fullKeyName, // Store full key name for display
                            elementModule: moduleName, // NEW: Store module info for later use
                          };
                          setSteps(updatedSteps);

                          // Track changes for existing steps
                          if (index < originalSteps.length) {
                            setChangedStepIndices(
                              (prev) => new Set([...prev, index])
                            );
                          }
                          setEditingCell({ row: null, field: null });
                        }}
                        onClose={() =>
                          setEditingCell({ row: null, field: null })
                        }
                      />
                    ) : (
                      <span
                        onClick={
                          allowEditing && !isLocked
                            ? () => handleCellClick(index, "element")
                            : undefined
                        }
                        className={`cursor-pointer ${
                          isLocked ? "cursor-not-allowed" : ""
                        }`}
                      >
                        {step.element || "—"}
                      </span>
                    )}
                  </TableCell>
                  {/* Data - conditional editing */}
                  <TableCell className="w-[20%]">
                    {allowEditing &&
                    !isLocked &&
                    editingCell.row === index &&
                    editingCell.field === "data" ? (
                      <input
                        value={step.data}
                        onChange={(e) => handleCellChange(e, index, "data")}
                        onBlur={handleBlur}
                        autoFocus
                        className="w-full bg-white border border-gray-300 rounded px-2 py-1"
                      />
                    ) : (
                      <span
                        onClick={
                          allowEditing && !isLocked
                            ? () => handleCellClick(index, "data")
                            : undefined
                        }
                        className={isLocked ? "cursor-not-allowed" : ""}
                      >
                        {step.data || "—"}
                      </span>
                    )}
                  </TableCell>
                  {/* Screenshot */}
                  <TableCell className="w-[15%] text-blue-500 cursor-pointer">
                    <img
                      src="/Group 23576.svg"
                      className="w-[16px] h-[19px] inline-block mr-2"
                    />
                    {step.screenshot}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </AccordionContent>
      </AccordionItem>
      <Record
        isOpen={recordDialogOpen}
        onOpenChange={setRecordDialogOpen}
        testCaseName={caseName}
      />
    </>
  );
}

// Main component that renders all test cases
export function AccordionTable({
  testCases = [],
  refreshTrigger = 0,
  showActionButtons = true,
  showCheckboxes = true,
  allowEditing = true,
  showCucumberButton = true,
  showPlayButton = true,
  onDeleteTestCase, // NEW: Add this prop
}: {
  testCases: Array<{ id: number; name: string; filePath: string; data: any }>;
  refreshTrigger?: number;
  showActionButtons?: boolean;
  showCheckboxes?: boolean;
  allowEditing?: boolean;
  showCucumberButton?: boolean;
  showPlayButton?: boolean;
  onDeleteTestCase?: (testCaseId: number, testCaseName: string) => Promise<void>; // NEW: Add this type
}) {
  if (testCases.length === 0) {
    return (
      <div className="p-8 text-center text-gray-500">
        <div className="text-gray-400 mb-4">
          <svg
            className="w-16 h-16 mx-auto mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
            />
          </svg>
          <p className="text-lg font-medium text-gray-600">
            No test cases found
          </p>
          <p className="text-sm text-gray-500">
            Create your first Test Case to get started
          </p>
        </div>
      </div>
    );
  }

  return (
    <Accordion type="multiple" className="w-full h-full space-y-0.5">
      {testCases.map((testCase) => (
        <SingleTestCase
          key={testCase.id}
          caseNumber={testCase.id}
          caseName={testCase.name}
          initialSteps={convertRawSteps(testCase.data)}
          showActionButtons={showActionButtons}
          showCheckboxes={showCheckboxes}
          allowEditing={allowEditing}
          showCucumberButton={showCucumberButton}
          showPlayButton={showPlayButton}
          onDeleteTestCase={onDeleteTestCase} // NEW: Pass the delete handler
        />
      ))}
    </Accordion>
  );
}

export function AccordionAPITable({
  TestAPIs = [],
  refreshTrigger = 0,
  showActionButtons = true,
  showCheckboxes = true,
  allowEditing = true,
  showCucumberButton = true,
  showPlayButton = true,
  handleDelete,
  handleDialogClose
}: {
  TestAPIs: TestApi[];
  refreshTrigger?: number;
  showActionButtons?: boolean;
  showCheckboxes?: boolean;
  allowEditing?: boolean;
  showCucumberButton?: boolean;
  showPlayButton?: boolean;
  handleDelete: (api: any) => void;
  handleDialogClose: (open: boolean) => void;
}) {

  console.log(TestAPIs)

  if (TestAPIs.length === 0) {
    return (
       <div className="p-8 text-center text-gray-500">
        <div className="text-gray-400 mb-4">
          <svg
            className="w-16 h-16 mx-auto mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
            />
          </svg>
           
          <p className="text-lg font-medium text-gray-600">
            No test APIs found
          </p>
          <p className="text-sm text-gray-500">
            Create your first Test API to get started
          </p>
        </div>
      </div>
    );}

      
  return (
    <Accordion type="multiple" className="w-full h-full space-y-0.5">
      {TestAPIs.map((testCase) => (
        <SingleTestAPI
          key={testCase.id}
          apiNumber={testCase.id || 0}
          apiName={testCase.name}
          apiData={testCase}
          // initialSteps={convertRawSteps([])} // Assuming no steps for Test APIs
          // showActionButtons={showActionButtons}
          // showCheckboxes={showCheckboxes}
          // allowEditing={allowEditing}
          showCucumberButton={showCucumberButton}
          // showPlayButton={showPlayButton}
          handleDelete={handleDelete}
          handleDialogClose={handleDialogClose}
        />
      ))}
    </Accordion>
  );
}

// Component for a single test case accordion
function SingleTestAPI({
  apiNumber,
  apiName,
  showActionButtons = true,
  // showCheckboxes = true,
  showCucumberButton = true,
  // showPlayButton = true,
  apiData,
  handleDelete,
  handleDialogClose
}: {
  apiNumber: number;
  apiName?: string;
  showActionButtons?: boolean;
  // showCheckboxes?: boolean;
  showCucumberButton?: boolean;
  // showPlayButton?: boolean;
  apiData?: TestApi;
  handleDelete: (api: any) => void;
  handleDialogClose: (open: boolean) => void
}) {
  // const INITIAL_ROW_COUNT = 3;
  // const [steps, setSteps] = useState<Step[]>(initialSteps);
  // const [selectedSteps, setSelectedSteps] = useState(
  //   Array(INITIAL_ROW_COUNT).fill(false)
  // );
  // const [selectAll, setSelectAll] = useState(false);
  // const [editingCell, setEditingCell] = useState<{
  //   row: number | null;
  //   field: string | null;
  // }>({ row: null, field: null });

  // Tracks the original steps count to identify new rows
  // const [originalStepsCount, setOriginalStepsCount] = useState(
  //   initialSteps.length
  // );
  const [isSaving, setIsSaving] = useState(false);

  // Add accordion open state to track chevron direction
  const [isAccordionOpen, setIsAccordionOpen] = useState(false);

  // Add lock state for this test case
  const [isLocked, setIsLocked] = useState(false);

  // Smart change detection state
  // const [originalSteps, setOriginalSteps] = useState<Step[]>(initialSteps);
  // const [changedStepIndices, setChangedStepIndices] = useState<Set<number>>(
  //   new Set()
  // );
  // const [deletedStepIndices, setDeletedStepIndices] = useState<Set<number>>(
  //   new Set()
  // );


  // Handle lock toggle
  const handleLockToggle = () => {
    setIsLocked(!isLocked);
    // Close any open editing cells when locking
    // if (!isLocked) {
    //   setEditingCell({ row: null, field: null });
    // }
    console.log(`Test case ${apiName} lock state: ${!isLocked}`);
  };

  const [apiDialogOpen, setapiDialogOpen] = useState(false);
  const [selectedApiData, setSelectedApiData] = useState<TestApi | null>(null);

  return (
    <>
      <AccordionItem value={`test-case-${apiNumber}`}>
        <AccordionTrigger
          className="hover:no-underline p-0 [&>svg]:hidden"
          onClick={() => setIsAccordionOpen(!isAccordionOpen)}
        >
          <div
            className="flex items-center h-[45px] px-4 w-full"
            style={{
              // background: "#DEDEDE 0% 0% no-repeat padding-box",
              // borderRadius: "6px 6px 0px 0px",
              opacity: 1,
            }}
          >
            <div className="flex items-center flex-1">
              {/* {showCheckboxes && (
                <input
                  type="checkbox"
                  checked={selectAll}
                  // onChange={toggleSelectAll}
                  onClick={(e) => e.stopPropagation()}
                  className="mr-3"
                  disabled={isLocked} // Disable checkbox when locked
                />
              )} */}
              <span className="text-left text-base font-medium">
                {apiName || `Test Case ${apiNumber}`}
                {isLocked && (
                  <span className="ml-2 text-red-500 text-sm">(Locked)</span>
                )}
              </span>
            </div>
            <div className="flex items-center gap-3 pr-2">
              {showActionButtons && (
                <>
                  {/* Lock/Unlock Toggle Button */}
                  <a
                    onClick={(e) => {
                      e.stopPropagation();
                      handleLockToggle();
                    }}
                    className="cursor-pointer"
                    title={isLocked ? "Unlock test case" : "Lock test case"}
                  >
                    {isLocked ? (
                      <Lock className="w-4 h-4 text-red-500" />
                    ) : (
                      <Unlock className="w-4 h-4 text-gray-600" />
                    )}
                  </a>

                  {/* <a
                    onClick={(e) => {
                      e.stopPropagation();
                      // handleSaveClick();
                    }}
                    className={`cursor-pointer ${
                      isLocked ? "opacity-50 cursor-not-allowed" : ""
                    }`}
                    title="Save new steps"
                  >
                    <img
                      src="/file.svg"
                      className="w-[16px] h-[14px] cursor-pointer"
                      style={{ opacity: isSaving || isLocked ? 0.5 : 1 }}
                    />
                  </a> */}

                  {/* Cucumber Button - Conditionally Rendered */}
                  {showCucumberButton && (
                    <a
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedApiData(apiData || null);
                        setapiDialogOpen(true);
                      }}
                      title="View/edit API"
                    >
                      <img
                        src="/eye.svg"
                        className="w-[16px] h-[19px] cursor-pointer"
                      />
                    </a>
                  )}

                  {/* <a
                    onClick={(e) => {
                      e.stopPropagation();
                      // handleAddRow();
                    }}
                    className={`${
                      isLocked ? "opacity-50 cursor-not-allowed" : ""
                    }`}
                  >
                    <img
                      src="/Group 23495.svg"
                      className="w-[16px] h-[14px] cursor-pointer"
                    />
                  </a> */}

                  {/* Play Button - Conditionally Rendered */}
                  {/* {showPlayButton && (
                    <a
                      onClick={(e) => {
                        e.stopPropagation();
                        const fileName = apiName
                          ? apiName
                          : `${apiNumber}.json`;
                        // handlePlayClick(fileName);
                      }}
                      className="cursor-pointer"
                    >
                      <img
                        src="/play.svg"
                        className="w-[16px] h-[19px] cursor-pointer"
                      />
                    </a>
                  )} */}

                  <a
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDelete(apiData);
                      // handleDeleteSelected();
                    }}
                    className={`${
                      isLocked ? "opacity-50 cursor-not-allowed" : ""
                    }`}
                  >
                    <img
                      src="/Group 21846.svg"
                      className="w-[16px] h-[14px] cursor-pointer"
                    />
                  </a>
                </>
              )}
              {/* Custom Chevron Arrow */}
              {isAccordionOpen ? (
                <ChevronUp className="h-4 w-4 shrink-0 text-gray-600" />
              ) : (
                <ChevronDown className="h-4 w-4 shrink-0 text-gray-600" />
              )}
            </div>
          </div>
        </AccordionTrigger>

<AccordionContent className="p-0">
  <Table className="w-full table-fixed">
    <TableHeader>
      <TableRow>
        
        <TableHead className="w-[10%]">Request Type</TableHead>
        <TableHead className="w-[15%]">URL</TableHead>
        {apiData?.requestType === "GET" ? (
          <TableHead className="w-[20%]">Parameters</TableHead>
        ) : (
          <TableHead className="w-[20%]">Body</TableHead>
        )}
        <TableHead className="w-[7%]">Output</TableHead>
        <TableHead className="w-[14%]">Response Validation</TableHead>
        <TableHead className="w-[14%]">Export Variable</TableHead>
      </TableRow>
    </TableHeader>

    <TableBody>
      <TableRow>
        
        <TableCell className="w-[10%]">{apiData?.requestType}</TableCell>
        <TableCell className="w-[15%]">{apiData?.url}</TableCell>
        {apiData?.requestType === "GET" ? (
          <TableCell className="w-[20%]">
            {apiData?.queryParams ? (
              <>{apiData?.queryParams}</>
            ): <> - </>}
            </TableCell>
        ) : (
<TableCell className="w-[20%] min-w-0">
  {apiData?.body ? (
    <div
      className="block truncate cursor-pointer"
      title={normalizeBody(apiData.body)}
    >
      {normalizeBody(apiData.body)}
    </div>
  ) : (
    <div className="truncate w-full">-</div>
  )}
</TableCell>

        )}

<TableCell className="w-[10%]">{apiData?.expectedStatusCode}</TableCell>

<TableCell className="w-[15%] min-w-0">
  {apiData?.responseValidation ? (
    <div
      className="block truncate cursor-pointer"
      title={JSON.stringify(apiData.responseValidation, null, 2)}
    >
      {JSON.stringify(apiData.responseValidation)}
    </div>
  ) : (
    <div className="truncate w-full">-</div>
  )}
</TableCell>

<TableCell className="w-[10%] min-w-0">
  {apiData?.exportVariable ? (
    <div
      className="block truncate cursor-pointer"
      title={JSON.stringify(apiData.exportVariable, null, 2)}
    >
      {JSON.stringify(apiData.exportVariable)}
    </div>
  ) : (
    <div className="truncate w-full">-</div>
  )}
</TableCell>
      </TableRow>
    </TableBody>
  </Table>
</AccordionContent>



      </AccordionItem>
      {/* <Record
        isOpen={apiDialogOpen}
        onOpenChange={setapiDialogOpen}
        testapiName={apiName}
      /> */}
      <ViewAPIDialog
        isOpen={apiDialogOpen}
        // onOpenChange={handleDialogClose}
        onOpenChange={setapiDialogOpen}
        apiData={selectedApiData}
      />
    </>
  );
}

export default AccordionTable;

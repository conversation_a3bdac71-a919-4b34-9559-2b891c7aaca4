import { NextResponse } from "next/server";
import fs from "fs-extra";
import path from "path";
import { glob } from "glob";
import { parseStringPromise, Builder } from "xml2js";
import { updateXmlTag } from "@/lib/xmlUtils";
import AdmZip from "adm-zip";
import PropertiesReader from "properties-reader";

export async function POST(req: Request) {
  const { projectName, projectUrl } = await req.json();

  if (!projectName) {
    return NextResponse.json(
      { error: "Project name is required" },
      { status: 400 }
    );
  }

  const zipPath = path.resolve(
    process.cwd(),
    "src",
    "data",
    "backendframework.zip"
  );
  const targetPath = path.resolve(`D:/TargetCode/${projectName}`);

  try {
    if (await fs.pathExists(targetPath)) {
      return NextResponse.json(
        { error: "Project already exists!" },
        { status: 400 }
      );
    }

    // Check if ZIP file exists
    if (!(await fs.pathExists(zipPath))) {
      console.error("ZIP file missing:", zipPath);
      return NextResponse.json(
        { error: "Template ZIP file not found or corrupted" },
        { status: 500 }
      );
    }

    //Copy project template
    const zip = new AdmZip(zipPath);
    zip.extractAllTo(targetPath, true);

    // //Replace placeholders in all files
    // const files = glob.sync(`${targetPath}/**/*.*`);
    // for (const file of files) {
    //   let content = await fs.readFile(file, "utf-8");
    //   content = content.replace(/__PROJECT_NAME__/g, projectName);
    //   await fs.writeFile(file, content);
    // }

    //Locate nested pom.xml
    const pomPath = path.join(targetPath, "antp-web", "pom.xml");

    if (await fs.pathExists(pomPath)) {
      const pomContent = await fs.readFile(pomPath, "utf-8");
      const pomObj = await parseStringPromise(pomContent);

      //Use helper to update nested <projectName>
      const updated = updateXmlTag(
        pomObj,
        [
          "project",
          "build",
          0,
          "plugins",
          0,
          "plugin",
          0,
          "executions",
          0,
          "execution",
          0,
          "configuration",
          0,
          "projectName",
          0,
        ],
        projectName
      );

      if (updated) {
        console.log(" <projectName> updated!");
      } else {
        console.warn(" Could not find <projectName> to update!");
      }

      const builder = new Builder();
      const updatedPom = builder.buildObject(pomObj);

      await fs.writeFile(pomPath, updatedPom, "utf-8");
    }

    // Update config.json with new project details
    const configPath = path.join(process.cwd(), "src", "config", "config.json");
    try {
      let config = {};

      // Read existing config if it exists
      if (await fs.pathExists(configPath)) {
        const configContent = await fs.readFile(configPath, "utf-8");
        config = JSON.parse(configContent);
      }

      // Update project-related fields
      const currentProjectPath = `D:/TargetCode/${projectName}`;
      const updatedConfig = {
        ...config,
        projectName: projectName,
        currentProjectPath: currentProjectPath,
        recordingsPath: `${currentProjectPath}/antp-web/src/test/resources/features`,
        testflowpath: `${currentProjectPath}/antp-web/src/testflows.json`,
        testsuitepath: `${currentProjectPath}/antp-web/src/testsuites.json`,
        projectPath: `${currentProjectPath}/antp-web`,
        elementsPath: `${currentProjectPath}/antp-web/src/main/resources/elementProperties`,
        elementextractorpath: `${currentProjectPath}/webElementExtractor/src/test/resources/features`,
        projectConfigurations: `${currentProjectPath}/antp-web/src/main/resources/config/url.properties`
      };

      // Write updated config back to file
      await fs.writeFile(
        configPath,
        JSON.stringify(updatedConfig, null, 2),
        "utf-8"
      );
      console.log(` Config updated with project: ${projectName}`);
    } catch (configError) {
      console.error(" Failed to update config.json:", configError);
      // Don't fail the entire request if config update fails
    }

    try {
      const urlpropertiespath = path.join(
        targetPath,
        "antp-web",
        "src",
        "main",
        "resources",
        "config",
        "url.properties"
      );
      // Load the .properties file
      const urlproperties = PropertiesReader(urlpropertiespath);
      urlproperties.set("applicationHome", projectUrl);
      const updatedContent = urlproperties.getAllProperties();
      let updatedString = "";
      for (const [key, value] of Object.entries(updatedContent)) {
        updatedString += `${key}=${value}\n`;
      }
      fs.writeFileSync(urlpropertiespath, updatedString, "utf-8");
    } catch (propertyfileerror) {
      console.error("Failed to update url.properties:", propertyfileerror);
      // Don't fail the entire request if properties file update fails
    }

    return NextResponse.json({
      message: `Project '${projectName}' created successfully!`,
    });
  } catch (err) {
    console.error(err);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
